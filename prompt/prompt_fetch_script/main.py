import requests
from bs4 import BeautifulSoup
import mysql.connector
import json
import logging
import time

# CREATE TABLE IF NOT EXISTS prompts_01 (
#     id INT AUTO_INCREMENT PRIMARY KEY,
#     title VARCHAR(255) NOT NULL,            -- 提示词标题
#     teaser TEXT,                            -- 提示词的简短描述/引导语
#     prompt_text TEXT NOT NULL,              -- 核心提示词文本
#     tags JSON,                              -- 标签 (存储为JSON数组字符串)
#     source_url VARCHAR(512) NULL,           -- 原始来源链接 (可选)
#     scraped_url VARCHAR(512) NOT NULL,      -- 抓取的页面URL
#     scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 抓取时间
#     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
#     UNIQUE KEY unique_prompt (title(200), scraped_url(200)) -- 联合唯一键防止重复 (根据需要调整长度)
# );
# --- 配置 ---
URL = "https://xn--o0uq09burn.com/" # 目标网站URL (已从描述更新)
DB_CONFIG = {
    'host': '***********',        # 数据库主机名 (例如 'localhost' 或 IP 地址)
    'port': 8306,
    'user': 'root',    # 你的数据库用户名
    'password': 'dataeng_test',# 你的数据库密码
    'database': 'llm_prompt_crawler' # 你要使用的数据库名称
}
TABLE_NAME = 'prompts_01'

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 函数定义 ---

def fetch_html(url):
    """获取指定URL的HTML内容"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=20)
        response.raise_for_status() # 如果请求失败则抛出HTTPError异常
        # 显式指定编码，如果网站编码不规范可能需要调整
        response.encoding = response.apparent_encoding
        logging.info(f"成功获取URL内容: {url}")
        return response.text
    except requests.exceptions.RequestException as e:
        logging.error(f"请求URL时出错 {url}: {e}")
        return None

def parse_prompts(html_content):
    """解析HTML并提取提示词信息"""
    if not html_content:
        return []

    soup = BeautifulSoup(html_content, 'lxml') # 使用lxml解析器
    prompt_list = []

    # 定位所有提示词卡片 (根据你的HTML分析，可能需要调整选择器)
    # 查找所有包含提示词的 <li> 元素，通常在一个特定的 <ul> 或 <div> 下
    # 更新选择器以匹配实际HTML结构
    prompt_cards = soup.select('ul.showcaseList_Cwj2 > li.card')
    if not prompt_cards:
         # 尝试备用选择器（如果结构可能变化）
         prompt_cards = soup.find_all('li', class_='card shadow--md') # 更通用的查找方式

    logging.info(f"发现 {len(prompt_cards)} 个潜在的提示词卡片。")

    for card in prompt_cards:
        try:
            data = {}

            # 提取标题
            title_tag = card.select_one('h4.showcaseCardTitle_zvaY > a')
            data['title'] = title_tag.text.strip() if title_tag else None

            # 提取描述和提示文本
            p_tags = card.select('p.showcaseCardBody_fqoj')
            if len(p_tags) >= 1:
                 # 第一个p标签通常是引导语/Teaser
                 teaser_raw = p_tags[0].text.strip()
                 if '👉' in teaser_raw:
                     data['teaser'] = teaser_raw.split('👉', 1)[-1].strip()
                 else:
                     data['teaser'] = teaser_raw # 如果没有箭头，取全部
            else:
                 data['teaser'] = None

            if len(p_tags) >= 2:
                 # 第二个p标签通常是核心prompt
                 data['prompt_text'] = p_tags[1].text.strip()
            else:
                 # 如果只有一个p标签，尝试判断它是否是prompt
                 if data['teaser'] and '作为一名' in data['teaser'] or '我想让你充当' in data['teaser']: # 简单启发式判断
                     data['prompt_text'] = data['teaser']
                     data['teaser'] = None # 将其移至prompt，清空teaser
                 else:
                     data['prompt_text'] = None # 否则无法确定prompt

            # 必须有标题和提示文本才处理
            if not data['title'] or not data['prompt_text']:
                logging.warning(f"跳过一个卡片，缺少标题或提示文本: Title='{data.get('title', 'N/A')}'")
                continue

            # 提取标签
            tags = []
            tag_elements = card.select('ul.cardFooter_tbwZ li.tag_dHH4 span.textLabel_NAck')
            for tag_span in tag_elements:
                tags.append(tag_span.text.strip())
            data['tags'] = json.dumps(tags, ensure_ascii=False) # 存储为JSON字符串

            # 提取来源链接 (可选)
            source_link_tag = card.select_one('ul.cardFooter_tbwZ a[href*="github.com"], ul.cardFooter_tbwZ a[href*="learnprompting.org"], ul.cardFooter_tbwZ a[href*="playpcesor.com"]') # 增加了更多可能的来源域名
            data['source_url'] = source_link_tag['href'] if source_link_tag else None

            prompt_list.append(data)
            logging.debug(f"成功解析提示: {data['title']}")

        except Exception as e:
            title_attempt = card.select_one('h4.showcaseCardTitle_zvaY > a')
            title_text = title_attempt.text.strip() if title_attempt else "未知标题"
            logging.error(f"解析卡片 '{title_text}' 时出错: {e}", exc_info=True) # exc_info=True 会记录堆栈跟踪

    logging.info(f"成功解析出 {len(prompt_list)} 个提示词。")
    return prompt_list

def connect_db(config):
    """连接到MySQL数据库"""
    try:
        connection = mysql.connector.connect(**config)
        logging.info("成功连接到MySQL数据库。")
        return connection
    except mysql.connector.Error as err:
        logging.error(f"连接数据库失败: {err}")
        if err.errno == mysql.connector.errorcode.ER_ACCESS_DENIED_ERROR:
            logging.error("用户名或密码错误")
        elif err.errno == mysql.connector.errorcode.ER_BAD_DB_ERROR:
            logging.error("数据库不存在")
        else:
            logging.error(err)
        return None

def create_table_if_not_exists(cursor):
    """创建数据库表（如果不存在）"""
    create_table_query = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        teaser TEXT,
        prompt_text TEXT NOT NULL,
        tags JSON,
        source_url VARCHAR(512) NULL,
        scraped_url VARCHAR(512) NOT NULL,
        scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_prompt (title(200), scraped_url(200))
    );
    """
    try:
        cursor.execute(create_table_query)
        logging.info(f"表 '{TABLE_NAME}' 检查/创建成功。")
    except mysql.connector.Error as err:
        logging.error(f"创建表 '{TABLE_NAME}' 失败: {err}")

def insert_prompt(cursor, db_connection, prompt_data, scraped_url):
    """将单个提示词插入数据库，检查重复"""
    # 检查是否存在（基于联合唯一键）
    check_query = f"SELECT id FROM {TABLE_NAME} WHERE title = %s AND scraped_url = %s"
    try:
        cursor.execute(check_query, (prompt_data['title'], scraped_url))
        if cursor.fetchone():
            logging.info(f"提示 '{prompt_data['title']}' 在URL '{scraped_url}' 已存在，跳过插入。")
            return False # 表示未插入
    except mysql.connector.Error as err:
         logging.error(f"检查提示 '{prompt_data['title']}' 是否存在时出错: {err}")
         return False # 出错，不尝试插入

    # 准备插入语句
    insert_query = f"""
    INSERT INTO {TABLE_NAME}
    (title, teaser, prompt_text, tags, source_url, scraped_url)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    values = (
        prompt_data['title'],
        prompt_data['teaser'],
        prompt_data['prompt_text'],
        prompt_data['tags'],
        prompt_data.get('source_url'), # 使用get以防键不存在
        scraped_url
    )

    try:
        cursor.execute(insert_query, values)
        db_connection.commit()
        logging.info(f"成功插入提示: {prompt_data['title']}")
        return True # 表示成功插入
    except mysql.connector.Error as err:
        logging.error(f"插入提示 '{prompt_data['title']}' 到数据库失败: {err}")
        db_connection.rollback() # 插入失败时回滚
        return False # 表示未插入
    except Exception as e:
        logging.error(f"插入提示 '{prompt_data['title']}' 时发生未知错误: {e}")
        db_connection.rollback()
        return False


# --- 主程序 ---
if __name__ == "__main__":
    logging.info("脚本开始执行...")

    # 1. 获取HTML
    html = fetch_html(URL)
    if not html:
        logging.error("无法获取HTML内容，脚本终止。")
        exit()

    # 2. 解析HTML
    prompts = parse_prompts(html)
    if not prompts:
        logging.warning("未解析到任何提示词。")
        # exit() # 可以选择退出，或者继续尝试连接数据库（如果表已存在）

    # 3. 连接数据库并插入数据
    db_conn = connect_db(DB_CONFIG)
    if db_conn and db_conn.is_connected():
        cursor = db_conn.cursor()

        # 确保表存在
        create_table_if_not_exists(cursor)

        # 插入数据
        inserted_count = 0
        skipped_count = 0
        if prompts: # 只有在解析到内容时才尝试插入
            for prompt in prompts:
                if insert_prompt(cursor, db_conn, prompt, URL):
                    inserted_count += 1
                else:
                    skipped_count += 1
                time.sleep(0.1) # 短暂延时，减轻数据库压力（可选）

        # 关闭连接
        cursor.close()
        db_conn.close()
        logging.info("数据库连接已关闭。")
        logging.info(f"脚本执行完毕。共解析 {len(prompts)} 个提示词，成功插入 {inserted_count} 个，跳过 {skipped_count} 个重复/错误项。")
    else:
        logging.error("未能连接到数据库，无法存储数据。")

    logging.info("脚本执行结束。")