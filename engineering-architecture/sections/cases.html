<section id="cases" class="mb-24">
                <h2 class="text-3xl font-bold mb-8 text-blue-400 flex items-center">
                    <span class="bg-blue-500 bg-opacity-20 text-blue-300 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3">3</span>
                    实践案例分析
                </h2>
                
                     <!-- 3.1 Disruptor框架案例分析 -->
                     <div class="mb-12">
                        <h3 class="text-2xl font-semibold mb-6 text-blue-300 border-l-4 border-blue-400 pl-3">3.1 Disruptor框架案例分析</h3>
                        
                        <!-- Disruptor介绍 -->
                        <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 mb-8 card-hover">
                            <div class="flex flex-col md:flex-row gap-8 items-center mb-6">
                                <div class="md:w-1/3">
                                    <div class="bg-gray-900 p-6 rounded-xl flex justify-center items-center">
                                        <svg width="240" height="100" viewBox="0 0 240 100" class="text-blue-400">
                                            <g fill="currentColor">
                                                <path d="M20,20 h200 v60 h-200 z" fill="none" stroke="currentColor" stroke-width="4" />
                                                <circle cx="120" cy="50" r="30" fill="none" stroke="currentColor" stroke-width="4" />
                                                <path d="M120,20 v60" stroke="currentColor" stroke-width="2" stroke-dasharray="5,5" />
                                                <path d="M90,50 h60" stroke="currentColor" stroke-width="2" stroke-dasharray="5,5" />
                                                <!-- 箭头表示旋转方向 -->
                                                <path d="M150,50 a30,30 0 1,0 -30,-30" fill="none" stroke="currentColor" stroke-width="2" />
                                                <path d="M145,25 l5,-5 l-5,-5" fill="none" stroke="currentColor" stroke-width="2" />
                                            </g>
                                        </svg>
                                    </div>
                                </div>
                                <div class="md:w-2/3">
                                    <h4 class="text-xl font-medium mb-4 text-yellow-400">Disruptor的核心设计理念</h4>
                                    <p class="mb-4">Disruptor是由LMAX Exchange开发的高性能并发框架，专为低延迟、高吞吐量而设计。它通过巧妙的数据结构和内存布局，实现了超越传统队列的性能。</p>
                                    <div class="bg-blue-900 bg-opacity-20 p-3 rounded-lg border border-blue-700">
                                        <p class="text-sm text-blue-300">Disruptor相比传统队列方案可提供高达10倍以上的性能提升，特别适合事件处理系统和金融交易系统。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="grid md:grid-cols-3 gap-6 mb-6">
                                <div class="bg-gray-900 bg-opacity-40 p-5 rounded-xl border border-gray-700">
                                    <h5 class="font-medium mb-3 text-blue-300">无锁设计</h5>
                                    <p class="text-gray-300 text-sm">采用CAS和内存屏障而非锁，避免上下文切换开销，同时保证线程安全和可见性。</p>
                                </div>
                                <div class="bg-gray-900 bg-opacity-40 p-5 rounded-xl border border-gray-700">
                                    <h5 class="font-medium mb-3 text-blue-300">预分配资源</h5>
                                    <p class="text-gray-300 text-sm">环形缓冲区在启动时一次性分配，避免运行时动态分配和GC开销。</p>
                                </div>
                                <div class="bg-gray-900 bg-opacity-40 p-5 rounded-xl border border-gray-700">
                                    <h5 class="font-medium mb-3 text-blue-300">批处理机制</h5>
                                    <p class="text-gray-300 text-sm">支持在单次交互中处理多个事件，大幅提高吞吐量和效率。</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Disruptor 图片展示 -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                            <div class="bg-gray-900 bg-opacity-40 p-4 rounded-xl border border-gray-700 text-center card-hover">
                                <a href="../assets/images/disruptor_architecture.png" target="_blank" rel="noopener noreferrer" title="点击查看大图">
                                    <img src="../assets/images/disruptor_architecture.png" alt="Disruptor 架构图" class="w-full h-48 object-contain rounded-lg mb-3 cursor-pointer transition-transform duration-300 hover:scale-105">
                                </a>
                                <p class="text-sm text-gray-300">Disruptor 架构</p>
                            </div>
                            <div class="bg-gray-900 bg-opacity-40 p-4 rounded-xl border border-gray-700 text-center card-hover">
                                <a href="../assets/images/ring_buffer.png" target="_blank" rel="noopener noreferrer" title="点击查看大图">
                                    <img src="../assets/images/ring_buffer.png" alt="环形缓冲区示意图" class="w-full h-48 object-contain rounded-lg mb-3 cursor-pointer transition-transform duration-300 hover:scale-105">
                                </a>
                                <p class="text-sm text-gray-300">环形缓冲区 (Ring Buffer)</p>
                            </div>
                            <div class="bg-gray-900 bg-opacity-40 p-4 rounded-xl border border-gray-700 text-center card-hover">
                                <a href="../assets/images/disruptor.png" target="_blank" rel="noopener noreferrer" title="点击查看大图">
                                    <img src="../assets/images/disruptor.png" alt="Disruptor 概览图" class="w-full h-48 object-contain rounded-lg mb-3 cursor-pointer transition-transform duration-300 hover:scale-105">
                                </a>
                                <p class="text-sm text-gray-300">Disruptor 概览</p>
                            </div>
                        </div>
                        
                        <!-- 环形缓冲区 -->
                        <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 mb-8 card-hover">
                            <h4 class="text-xl font-medium mb-6 text-blue-300">环形缓冲区的工作原理</h4>
                            
                            <div class="grid md:grid-cols-2 gap-8">
                                <div>
                                    <p class="mb-4">Disruptor的核心是RingBuffer（环形缓冲区），它是一个固定大小的数组，通过索引的循环使用模拟无限长度的队列。</p>
                                    
                                    <h5 class="font-medium mb-3 text-yellow-400">关键组件</h5>
                                    <ul class="list-disc pl-5 space-y-2 text-gray-300 mb-6">
                                        <li><span class="text-blue-300 font-medium">RingBuffer</span>：存储事件的主数据结构</li>
                                        <li><span class="text-blue-300 font-medium">Sequence</span>：跟踪事件处理的位置标识</li>
                                        <li><span class="text-blue-300 font-medium">Sequencer</span>：协调生产者和消费者的访问</li>
                                        <li><span class="text-blue-300 font-medium">SequenceBarrier</span>：确保消费者不会读取正在写入的数据</li>
                                        <li><span class="text-blue-300 font-medium">WaitStrategy</span>：定义消费者等待新事件的策略</li>
                                    </ul>
                                    
                                    <div class="p-3 bg-gray-900 rounded-lg border border-gray-700">
                                        <p class="text-sm text-gray-300">环形缓冲区的大小必须是2的幂，这使得通过位操作可以高效计算索引，避免昂贵的模运算。</p>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="bg-gray-900 p-4 rounded-lg overflow-hidden text-sm mb-4">
                                        <div class="code-title">Disruptor基本使用示例</div>
                                        <pre><code class="language-java">// 事件定义
    public class ValueEvent {
        private long value;
        public void setValue(long value) { this.value = value; }
        public long getValue() { return value; }
    }
    
    // 创建Disruptor
    Disruptor<ValueEvent> disruptor = new Disruptor<>(
        ValueEvent::new,         // 事件工厂
        1024,                    // 环形缓冲区大小，必须是2的幂
        Executors.defaultThreadFactory(),
        ProducerType.SINGLE,     // 单生产者模式
        new YieldingWaitStrategy() // 等待策略
    );
    
    // 注册事件处理器
    disruptor.handleEventsWith((event, sequence, endOfBatch) -> {
        System.out.println("Processed: " + event.getValue());
    });
    
    // 启动Disruptor
    disruptor.start();
    
    // 发布事件
    RingBuffer<ValueEvent> ringBuffer = disruptor.getRingBuffer();
    ringBuffer.publishEvent((event, sequence, arg) -> {
        event.setValue(arg);
    }, 10L);</code></pre>
                                    </div>
                                    
                                    <div class="flex justify-center">
                                        <div class="bg-gray-900 rounded-lg p-4 inline-flex items-center text-sm">
                                            <i class="ri-information-line text-yellow-400 mr-2 text-lg"></i>
                                            <span>RingBuffer使用序列屏障和内存屏障确保线程间的可见性，而无需使用锁。</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 缓存行填充 -->
                        <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 mb-8 card-hover">
                            <h4 class="text-xl font-medium mb-6 text-blue-300">缓存行填充（False Sharing）问题</h4>
                            
                            <div class="grid md:grid-cols-2 gap-8">
                                <div>
                                    <h5 class="font-medium mb-3 text-yellow-400">什么是False Sharing</h5>
                                    <p class="mb-4">False Sharing是多线程应用中常见的性能问题，当多个线程修改同一个缓存行中的不同变量时，会导致缓存行失效，引起不必要的内存同步。</p>
                                    
                                    <div class="bg-gray-900 p-4 rounded-lg mb-4">
                                        <h6 class="font-medium mb-2 text-blue-300">缓存行原理</h6>
                                        <ul class="list-disc pl-5 space-y-1 text-gray-300 text-sm">
                                            <li>现代CPU从内存读取数据时会加载整个缓存行（通常为64字节）</li>
                                            <li>如果两个变量在同一缓存行，一个线程修改其中一个变量会导致整个缓存行失效</li>
                                            <li>其他CPU核心需要重新从主存加载数据，即使它们只访问未被修改的变量</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="mt-5 overflow-x-auto w-full">
                                        <h4 class="text-xl font-semibold text-red-300 mb-4 text-center">CPU 访问延迟对比</h4>
                                        <table class="min-w-full divide-y divide-gray-700 bg-gray-800 rounded-lg shadow">
                                            <thead class="bg-gray-750">
                                                <tr>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">存储层级</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">典型延迟 (近似值)</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">类比感受 (若寄存器=1秒)</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-gray-800 divide-y divide-gray-700">
                                                <tr><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">CPU 寄存器</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~0.2 ns</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">1 秒</td></tr>
                                                <tr><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">一级缓存 (L1)</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~0.5 ns</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~2.5 秒</td></tr>
                                                <tr><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">二级缓存 (L2)</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~5 ns</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~25 秒</td></tr>
                                                <tr><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">三级缓存 (L3)</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~15 ns</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~1 分 15 秒</td></tr>
                                                <tr><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">主内存 (RAM)</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~75 ns</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~6 分 15 秒</td></tr>
                                                <tr><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">固态硬盘 (SSD NVMe)</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~50 µs</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~2.9 天</td></tr>
                                                <tr><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">机械硬盘 (HDD)</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~10 ms</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~1.6 年</td></tr>
                                                <tr><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">网络 Socket (同数据中心)</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~0.5 ms</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~29 天</td></tr>
                                                <tr><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">网络 Socket (跨地域)</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~50 ms</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">~8 年</td></tr>
                                            </tbody>
                                        </table>
                                        <p class="text-xs text-gray-500 mt-2 text-center">*注意：以上数值为典型近似值，实际表现受多种因素影响。类比仅为直观感受数量级差异。</p>
                                    </div>
                                </div>
                                
                                <div>
                                    <h5 class="font-medium mb-3 text-yellow-400">Disruptor如何解决</h5>
                                    <p class="mb-4">Disruptor框架通过缓存行填充（Cache Line Padding）技术解决False Sharing问题，确保关键变量独占整个缓存行。</p>
                                    
                                    <div class="bg-gray-900 p-4 rounded-lg overflow-hidden text-sm">
                                        <div class="code-title">Sequence类的缓存行填充实现</div>
                                        <pre><code class="language-java">// Disruptor中的Sequence类（简化版）
    class LhsPadding {
        protected long p1, p2, p3, p4, p5, p6, p7;
    }
    
    class Value extends LhsPadding {
        protected volatile long value;
    }
    
    class RhsPadding extends Value {
        protected long p9, p10, p11, p12, p13, p14, p15;
    }
    
    public class Sequence extends RhsPadding {
        // value变量的前后各填充7个long变量
        // 确保value独占一个缓存行，避免与其他变量共享
        
        public Sequence() {
            this(0);
        }
        
        public Sequence(long initialValue) {
            UNSAFE.putOrderedLong(this, VALUE_OFFSET, initialValue);
        }
        
        public long get() {
            return value;
        }
        
        // 其他方法...
    }</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 性能优化实践 -->
                        <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 card-hover">
                            <h4 class="text-xl font-medium mb-6 text-blue-300">性能优化实践</h4>
                            
                            <div class="grid md:grid-cols-3 gap-6 mb-8">
                                <div class="bg-gray-900 bg-opacity-40 p-5 rounded-xl border border-gray-700 card-hover">
                                    <div class="text-yellow-400 text-2xl mb-4">
                                        <i class="ri-align-vertically"></i>
                                    </div>
                                    <h5 class="font-medium mb-3 text-blue-300">缓存行对齐</h5>
                                    <p class="text-gray-300 text-sm">确保频繁访问的变量独占缓存行，避免因其他线程修改邻近变量导致的缓存失效。</p>
                                    <div class="mt-3 p-2 bg-gray-800 rounded-lg text-xs">
                                        <span class="text-blue-300">实现方式：</span> 使用填充变量确保核心数据结构占满整个缓存行
                                    </div>
                                </div>
                                
                                <div class="bg-gray-900 bg-opacity-40 p-5 rounded-xl border border-gray-700 card-hover">
                                    <div class="text-yellow-400 text-2xl mb-4">
                                        <i class="ri-stack-fill"></i>
                                    </div>
                                    <h5 class="font-medium mb-3 text-blue-300">填充策略</h5>
                                    <p class="text-gray-300 text-sm">在Java 8之前，使用额外的long变量；Java 8后，可以使用@Contended注解（需启用JVM选项）。</p>
                                    <div class="mt-3 p-2 bg-gray-800 rounded-lg text-xs">
                                        <span class="text-blue-300">Java 8示例：</span> @sun.misc.Contended 注解
                                    </div>
                                </div>
                                
                                <div class="bg-gray-900 bg-opacity-40 p-5 rounded-xl border border-gray-700 card-hover">
                                    <div class="text-yellow-400 text-2xl mb-4">
                                        <i class="ri-bar-chart-grouped-fill"></i>
                                    </div>
                                    <h5 class="font-medium mb-3 text-blue-300">实际效果对比</h5>
                                    <p class="text-gray-300 text-sm">在高并发场景下，合理的缓存行填充可将吞吐量提升2-8倍，特别是在多核CPU环境中效果更显著。</p>
                                    <div class="mt-3 p-2 bg-gray-800 rounded-lg text-xs">
                                        <span class="text-blue-300">关键指标：</span> 降低缓存未命中率，减少内存访问延迟
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg border border-blue-700">
                                <h5 class="font-medium mb-3 flex items-center text-blue-300">
                                    <i class="ri-lightbulb-line mr-2"></i>
                                    <span>实际应用建议</span>
                                </h5>
                                <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                    <li>在自定义高性能数据结构时，考虑缓存行对齐问题</li>
                                    <li>并不是所有场景都需要缓存行填充，应根据性能测试结果决定</li>
                                    <li>了解硬件特性（如缓存行大小）有助于更精确的优化</li>
                                    <li>在Java 9+环境中，考虑使用VarHandle替代Unsafe实现更安全的内存访问</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                  
                 <!-- 3.2 Java并发计数器性能对比 -->
                 <div>
                    <h3 class="text-2xl font-semibold mb-6 text-blue-300 border-l-4 border-blue-400 pl-3">3.2 Java并发计数器性能对比</h3>
                    
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 mb-8 card-hover">
                        <h4 class="text-xl font-medium mb-6 text-blue-300">AtomicLong的实现原理</h4>
                        
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <p class="mb-4">AtomicLong是Java并发包中提供的原子操作类，基于CAS操作实现线程安全的计数器功能。</p>
                                
                                <h5 class="font-medium mb-3 text-yellow-400">核心特性</h5>
                                <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                    <li>基于volatile变量和Unsafe类的CAS操作</li>
                                    <li>提供原子性的读-改-写操作</li>
                                    <li>实现简单直观，适用于低竞争场景</li>
                                    <li>所有线程竞争同一个共享变量</li>
                                </ul>
                                
                                <div class="mt-4 p-4 bg-yellow-900 bg-opacity-30 rounded-lg border border-yellow-700">
                                    <h6 class="font-medium mb-2 text-yellow-400 flex items-center">
                                        <i class="ri-error-warning-line mr-2"></i>
                                        <span>性能瓶颈</span>
                                    </h6>
                                    <p class="text-gray-300 text-sm">在高竞争环境下，多线程频繁更新同一个AtomicLong变量会导致大量CAS失败，引起线程自旋重试，浪费CPU资源。</p>
                                </div>
                            </div>
                            
                            <div>
                                <div class="bg-gray-900 p-4 rounded-lg overflow-hidden text-sm mb-6">
                                    <div class="code-title">AtomicLong核心实现</div>
                                    <pre><code class="language-java">public class AtomicLong extends Number implements java.io.Serializable {
    private static final Unsafe unsafe = Unsafe.getUnsafe();
    private static final long valueOffset;
    static {
        try {
            valueOffset = unsafe.objectFieldOffset
                (AtomicLong.class.getDeclaredField("value"));
        } catch (Exception ex) { throw new Error(ex); }
    }
    
    private volatile long value;
    
    public final long incrementAndGet() {
        return unsafe.getAndAddLong(this, valueOffset, 1L) + 1L;
    }
    
    public final boolean compareAndSet(long expect, long update) {
        return unsafe.compareAndSwapLong(this, valueOffset, expect, update);
    }
    
    // 其他方法...
}</code></pre>
                                </div>
                                
                                <div class="flex justify-center">
                                    <div class="bg-gray-900 rounded-lg p-4 inline-flex items-center text-sm">
                                        <i class="ri-information-line text-blue-400 mr-2 text-lg"></i>
                                        <span>CAS操作在竞争激烈时会导致频繁的CPU缓存同步，降低整体性能</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- LongAdder的设计思路 -->
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 mb-8 card-hover">
                        <h4 class="text-xl font-medium mb-6 text-blue-300">LongAdder的设计思路</h4>
                        
                        <div class="flex flex-col md:flex-row gap-8 items-center mb-8">
                            <div class="md:w-1/3 flex flex-col gap-4">
                                <div class="bg-gray-900 p-6 rounded-xl flex justify-center items-center">
                                    <svg width="200" height="160" viewBox="0 0 200 160" class="text-blue-400">
                                        <!-- 主结构 -->
                                        <rect x="20" y="10" width="160" height="30" fill="none" stroke="currentColor" stroke-width="2" rx="4" />
                                        <text x="100" y="30" text-anchor="middle" fill="currentColor" font-size="14">LongAdder</text>
                                        
                                        <!-- 单元格数组 -->
                                        <g>
                                            <rect x="30" y="60" width="30" height="30" fill="none" stroke="currentColor" stroke-width="2" />
                                            <text x="45" y="80" text-anchor="middle" fill="currentColor" font-size="12">C1</text>
                                            
                                            <rect x="70" y="60" width="30" height="30" fill="none" stroke="currentColor" stroke-width="2" />
                                            <text x="85" y="80" text-anchor="middle" fill="currentColor" font-size="12">C2</text>
                                            
                                            <rect x="110" y="60" width="30" height="30" fill="none" stroke="currentColor" stroke-width="2" />
                                            <text x="125" y="80" text-anchor="middle" fill="currentColor" font-size="12">C3</text>
                                            
                                            <rect x="150" y="60" width="30" height="30" fill="none" stroke="currentColor" stroke-width="2" />
                                            <text x="165" y="80" text-anchor="middle" fill="currentColor" font-size="12">C4</text>
                                        </g>
                                        
                                        <!-- 线程 -->
                                        <g>
                                            <circle cx="45" cy="120" r="15" fill="none" stroke="currentColor" stroke-width="2" />
                                            <text x="45" y="125" text-anchor="middle" fill="currentColor" font-size="12">T1</text>
                                            <line x1="45" y1="105" x2="45" y2="90" stroke="currentColor" stroke-width="2" stroke-dasharray="3,3" />
                                            
                                            <circle cx="85" cy="120" r="15" fill="none" stroke="currentColor" stroke-width="2" />
                                            <text x="85" y="125" text-anchor="middle" fill="currentColor" font-size="12">T2</text>
                                            <line x1="85" y1="105" x2="85" y2="90" stroke="currentColor" stroke-width="2" stroke-dasharray="3,3" />
                                            
                                            <circle cx="125" cy="120" r="15" fill="none" stroke="currentColor" stroke-width="2" />
                                            <text x="125" y="125" text-anchor="middle" fill="currentColor" font-size="12">T3</text>
                                            <line x1="125" y1="105" x2="125" y2="90" stroke="currentColor" stroke-width="2" stroke-dasharray="3,3" />
                                            
                                            <circle cx="165" cy="120" r="15" fill="none" stroke="currentColor" stroke-width="2" />
                                            <text x="165" y="125" text-anchor="middle" fill="currentColor" font-size="12">T4</text>
                                            <line x1="165" y1="105" x2="165" y2="90" stroke="currentColor" stroke-width="2" stroke-dasharray="3,3" />
                                        </g>
                                    </svg>
                                </div>
                                <div class="bg-gray-900 bg-opacity-40 p-4 rounded-xl border border-gray-700 text-center card-hover">
                                    <a href="../assets/images/longadder_logic.png" target="_blank" rel="noopener noreferrer" title="点击查看大图">
                                        <img src="../assets/images/longadder_logic.png" alt="LongAdder 原理图" class="w-full h-32 object-contain rounded-lg mb-3 cursor-pointer transition-transform duration-300 hover:scale-105">
                                    </a>
                                    <p class="text-sm text-gray-300">LongAdder 原理</p>
                                </div>
                            </div>
                            
                            <div class="md:w-2/3">
                                <p class="mb-4">LongAdder是Java 8引入的高性能计数器，通过分散竞争提高并发性能。它在内部维护了一个基础值base和一个Cell数组，通过分散更新来降低冲突。</p>
                                
                                <h5 class="font-medium mb-3 text-yellow-400">关键设计</h5>
                                <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                    <li><span class="text-blue-300 font-medium">减少竞争</span>：不同线程更新不同的Cell，减少CAS失败</li>
                                    <li><span class="text-blue-300 font-medium">动态扩展</span>：Cell数组会根据竞争程度动态扩容</li>
                                    <li><span class="text-blue-300 font-medium">分段更新</span>：每个线程基于线程探针值映射到对应的Cell槽位</li>
                                    <li><span class="text-blue-300 font-medium">汇总计算</span>：读取操作需要计算所有Cell值的总和，成本较高</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="bg-gray-900 p-4 rounded-lg overflow-hidden text-sm">
                            <div class="code-title">LongAdder核心实现（简化版）</div>
                            <pre><code class="language-java">public class LongAdder extends Striped64 implements Serializable {
    // 从Striped64继承: long base; volatile Cell[] cells;

    public void add(long x) {
        Cell[] cs; long b, v; int m; Cell c;
        if ((cs = cells) != null || !casBase(b = base, b + x)) {
            boolean uncontended = true;
            if (cs == null || (m = cs.length - 1) < 0 ||
                (c = cs[getProbe() & m]) == null ||
                !(uncontended = c.cas(v = c.value, v + x)))
                longAccumulate(x, null, uncontended);
        }
    }

    public long sum() {
        // 计算所有Cell值与base的总和
        Cell[] cs = cells;
        long sum = base;
        if (cs != null) {
            for (Cell c : cs)
                if (c != null)
                    sum += c.value;
        }
        return sum;
    }

    // 其他方法...
}</code></pre>
                        </div>
                    </div>
                    
                    <!-- 性能测试数据对比 -->
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 mb-8 card-hover">
                        <h4 class="text-xl font-medium mb-6 text-blue-300">性能测试数据对比</h4>
                        
                        <div class="bg-gray-900 bg-opacity-50 p-6 rounded-xl mb-8">
                            <h5 class="font-medium mb-4 text-yellow-400">测试场景</h5>
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <p class="mb-3"><span class="text-blue-300">硬件环境：</span> 64核CPU，128GB内存</p>
                                    <p class="mb-3"><span class="text-blue-300">测试方法：</span> 多线程并发递增计数器10亿次</p>
                                    <p><span class="text-blue-300">测试变量：</span> 不同线程数下的吞吐量和延迟</p>
                                </div>
                                <div>
                                    <p class="mb-3"><span class="text-blue-300">测试对象：</span></p>
                                    <ul class="list-disc pl-5 space-y-1 text-gray-300">
                                        <li>synchronized计数器</li>
                                        <li>AtomicLong计数器</li>
                                        <li>LongAdder计数器</li>
                                        <li>LongAccumulator计数器</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid md:grid-cols-2 gap-8 mb-6">
                            <div>
                                <h5 class="font-medium mb-4 text-yellow-400 text-center">吞吐量对比 (每秒操作数)</h5>
                                <div class="bg-gray-900 p-4 rounded-lg h-64 flex items-end justify-around">
                                    <!-- 模拟柱状图 -->
                                    <div class="flex flex-col items-center">
                                        <div class="w-16 bg-red-500 bg-opacity-70" style="height: 40px;"></div>
                                        <div class="mt-2 text-xs">synchronized</div>
                                        <div class="mt-1 text-xs text-gray-400">5.2M/s</div>
                                    </div>
                                    <div class="flex flex-col items-center">
                                        <div class="w-16 bg-yellow-500 bg-opacity-70" style="height: 120px;"></div>
                                        <div class="mt-2 text-xs">AtomicLong</div>
                                        <div class="mt-1 text-xs text-gray-400">21.5M/s</div>
                                    </div>
                                    <div class="flex flex-col items-center">
                                        <div class="w-16 bg-green-500 bg-opacity-70" style="height: 210px;"></div>
                                        <div class="mt-2 text-xs">LongAdder</div>
                                        <div class="mt-1 text-xs text-gray-400">125.7M/s</div>
                                    </div>
                                    <div class="flex flex-col items-center">
                                        <div class="w-16 bg-blue-500 bg-opacity-70" style="height: 200px;"></div>
                                        <div class="mt-2 text-xs">LongAccumulator</div>
                                        <div class="mt-1 text-xs text-gray-400">119.3M/s</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h5 class="font-medium mb-4 text-yellow-400 text-center">在不同线程数下的性能比率(相对于单线程)</h5>
                                <div class="bg-gray-900 p-4 rounded-lg chart-container h-64">
                                    <!-- 线图的SVG模拟 -->
                                    <svg width="50%" height="20%" viewBox="0 0 300 200" class="text-gray-300">
                                        <!-- 坐标轴 -->
                                        <line x1="40" y1="180" x2="40" y2="20" stroke="currentColor" stroke-width="1" />
                                        <line x1="40" y1="180" x2="280" y2="180" stroke="currentColor" stroke-width="1" />
                                        
                                        <!-- X轴标签 -->
                                        <text x="70" y="195" text-anchor="middle" fill="currentColor" font-size="10">4线程</text>
                                        <text x="120" y="195" text-anchor="middle" fill="currentColor" font-size="10">8线程</text>
                                        <text x="170" y="195" text-anchor="middle" fill="currentColor" font-size="10">16线程</text>
                                        <text x="220" y="195" text-anchor="middle" fill="currentColor" font-size="10">32线程</text>
                                        <text x="270" y="195" text-anchor="middle" fill="currentColor" font-size="10">64线程</text>
                                        
                                        <!-- Y轴标签 -->
                                        <text x="30" y="180" text-anchor="end" fill="currentColor" font-size="10">1x</text>
                                        <text x="30" y="140" text-anchor="end" fill="currentColor" font-size="10">2x</text>
                                        <text x="30" y="100" text-anchor="end" fill="currentColor" font-size="10">4x</text>
                                        <text x="30" y="60" text-anchor="end" fill="currentColor" font-size="10">8x</text>
                                        <text x="30" y="20" text-anchor="end" fill="currentColor" font-size="10">16x</text>
                                        
                                        <!-- 网格线 -->
                                        <line x1="40" y1="140" x2="280" y2="140" stroke="currentColor" stroke-width="0.5" stroke-dasharray="2,2" />
                                        <line x1="40" y1="100" x2="280" y2="100" stroke="currentColor" stroke-width="0.5" stroke-dasharray="2,2" />
                                        <line x1="40" y1="60" x2="280" y2="60" stroke="currentColor" stroke-width="0.5" stroke-dasharray="2,2" />
                                        <line x1="40" y1="20" x2="280" y2="20" stroke="currentColor" stroke-width="0.5" stroke-dasharray="2,2" />
                                        
                                        <!-- 数据线 -->
                                        <!-- AtomicLong -->
                                        <polyline points="70,170 120,165 170,170 220,175 270,178" 
                                            fill="none" stroke="#EAB308" stroke-width="2" />
                                            
                                        <!-- LongAdder -->
                                        <polyline points="70,140 120,100 170,70 220,50 270,30" 
                                            fill="none" stroke="#10B981" stroke-width="2" />
                                            
                                        <!-- 散点 -->
                                        <!-- AtomicLong -->
                                        <circle cx="70" cy="170" r="3" fill="#EAB308" />
                                        <circle cx="120" cy="165" r="3" fill="#EAB308" />
                                        <circle cx="170" cy="170" r="3" fill="#EAB308" />
                                        <circle cx="220" cy="175" r="3" fill="#EAB308" />
                                        <circle cx="270" cy="178" r="3" fill="#EAB308" />
                                        
                                        <!-- LongAdder -->
                                        <circle cx="70" cy="140" r="3" fill="#10B981" />
                                        <circle cx="120" cy="100" r="3" fill="#10B981" />
                                        <circle cx="170" cy="70" r="3" fill="#10B981" />
                                        <circle cx="220" cy="50" r="3" fill="#10B981" />
                                        <circle cx="270" cy="30" r="3" fill="#10B981" />
                                        
                                        <!-- 图例 -->
                                        <rect x="180" y="30" width="10" height="2" fill="#EAB308" />
                                        <text x="195" y="33" fill="currentColor" font-size="10">AtomicLong</text>
                                        
                                        <rect x="180" y="45" width="10" height="2" fill="#10B981" />
                                        <text x="195" y="48" fill="currentColor" font-size="10">LongAdder</text>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg border border-blue-700">
                            <h5 class="font-medium mb-2 flex items-center text-blue-300">
                                <i class="ri-lightbulb-line mr-2"></i>
                                <span>测试结论</span>
                            </h5>
                            <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                <li>在低竞争环境下（1-4线程），AtomicLong和LongAdder性能相近</li>
                                <li>随着线程数增加，AtomicLong性能急剧下降，而LongAdder保持良好扩展性</li>
                                <li>在64线程高竞争环境下，LongAdder吞吐量比AtomicLong高出约15倍</li>
                                <li>LongAdder的读取操作（sum()）比AtomicLong慢，需要权衡读写比例</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 使用场景分析 -->
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 card-hover">
                        <h4 class="text-xl font-medium mb-6 text-blue-300">使用场景分析</h4>
                        
                        <div class="grid md:grid-cols-2 gap-8 mb-8">
                            <div class="bg-gray-900 bg-opacity-40 p-6 rounded-xl border border-gray-700">
                                <h5 class="font-medium mb-4 text-green-400">适合使用AtomicLong的场景</h5>
                                <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                    <li>低竞争环境下的计数器</li>
                                    <li>需要频繁读取的计数器</li>
                                    <li>需要CAS的原子性语义（如getAndSet）</li>
                                    <li>需要精确递增顺序的场景</li>
                                    <li>内存占用敏感的应用</li>
                                </ul>
                                
                                <div class="mt-4 p-3 bg-green-900 bg-opacity-20 rounded-lg">
                                    <p class="text-sm text-gray-300">典型应用：序列号生成、并发访问计数、可见性保证</p>
                                </div>
                            </div>
                            
                            <div class="bg-gray-900 bg-opacity-40 p-6 rounded-xl border border-gray-700">
                                <h5 class="font-medium mb-4 text-blue-400">适合使用LongAdder的场景</h5>
                                <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                    <li>高竞争环境下的计数器</li>
                                    <li>写多读少的统计场景</li>
                                    <li>只需要最终一致性的结果</li>
                                    <li>追求最大吞吐量的场景</li>
                                    <li>多核心CPU环境</li>
                                </ul>
                                
                                <div class="mt-4 p-3 bg-blue-900 bg-opacity-20 rounded-lg">
                                    <p class="text-sm text-gray-300">典型应用：统计指标、度量系统、高并发计数器</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="p-5 bg-gray-900 bg-opacity-40 rounded-xl border border-gray-700">
                            <h5 class="font-medium mb-3 text-yellow-400 flex items-center">
                                <i class="ri-code-box-line mr-2"></i>
                                <span>实际代码示例</span>
                            </h5>
                            <div class="bg-gray-900 p-4 rounded-lg overflow-hidden text-sm">
                                <pre><code class="language-java">// 高性能并发统计系统示例

// 创建多个计数器
Map<String, LongAdder> counters = new ConcurrentHashMap<>();

// 并发更新计数
public void count(String key) {
   // 惰性初始化计数器
   counters.computeIfAbsent(key, k -> new LongAdder()).increment();
}

// 获取报表数据
public Map<String, Long> getReport() {
   Map<String, Long> report = new HashMap<>();
   counters.forEach((key, adder) -> report.put(key, adder.sum()));
   return report;
}

// 批量计数优化版本
public void batchIncrement(String key, long delta) {
   // 获取或创建计数器
   LongAdder adder = counters.computeIfAbsent(key, k -> new LongAdder());
   adder.add(delta);  // 增加指定值
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section> 