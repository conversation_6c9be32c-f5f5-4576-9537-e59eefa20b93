<section id="theory" class="mb-24">
                <h2 class="text-3xl font-bold mb-8 text-blue-400 flex items-center">
                    <span class="bg-blue-500 bg-opacity-20 text-blue-300 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3">2</span>
                    理论基础
                </h2>
                
                <!-- 2.1 CAS原理 -->
                <div class="mb-12">
                    <h3 class="text-2xl font-semibold mb-6 text-blue-300 border-l-4 border-blue-400 pl-3">2.1 CAS（Compare-and-Swap）原理</h3>
                    
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 mb-8 card-hover">
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <h4 class="text-xl font-medium mb-4 text-yellow-400">CAS操作的基本概念</h4>
                                <p class="mb-4">CAS (Compare-And-Swap) 是一种无锁算法，用于在多线程环境中安全地修改共享变量。它是许多无锁数据结构的基础。</p>
                                <p>CAS操作包含三个操作数：</p>
                                <ul class="list-disc pl-5 space-y-2 text-gray-300 mb-4">
                                    <li><span class="text-blue-300 font-medium">内存位置 (V)</span>：要更新的变量</li>
                                    <li><span class="text-blue-300 font-medium">预期原值 (A)</span>：更新前的预期值</li>
                                    <li><span class="text-blue-300 font-medium">新值 (B)</span>：要更新的新值</li>
                                </ul>
                                <p>CAS操作将内存位置的值与预期原值比较，如果相匹配，则将其更新为新值。这个操作是原子的。</p>
                            </div>
                            <div>
                                <h4 class="text-xl font-medium mb-4 text-yellow-400">CAS的工作流程</h4>
                                
                                <div class="bg-gray-900 p-4 rounded-lg overflow-hidden mb-4 text-sm">
                                    <div class="flex space-x-2 mb-2">
                                        <span class="inline-block w-3 h-3 rounded-full bg-red-500"></span>
                                        <span class="inline-block w-3 h-3 rounded-full bg-yellow-500"></span>
                                        <span class="inline-block w-3 h-3 rounded-full bg-green-500"></span>
                                    </div>
                                    <pre><code class="language-java">do {
    // 读取当前值
    oldValue = value;
    
    // 基于oldValue计算新值
    newValue = computeNewValue(oldValue);
    
    // 调用CAS尝试更新，如果失败继续循环
} while(!compareAndSet(value, oldValue, newValue));</code></pre>
                                </div>
                                
                                <p class="text-gray-300">CAS操作是一种乐观的方法：假设冲突很少发生，先尝试操作，只在真正遇到冲突时再解决冲突。它避免了传统锁定带来的线程阻塞和上下文切换成本。</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- CAS的优缺点分析 -->
                    <div class="grid md:grid-cols-2 gap-6 mb-8">
                        <div class="bg-gray-800 bg-opacity-30 p-6 rounded-xl border border-gray-700 card-hover">
                            <h4 class="text-lg font-medium mb-4 text-green-400">CAS的优点</h4>
                            <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                <li><span class="highlight">无锁机制</span>：避免了传统锁机制的上下文切换开销</li>
                                <li><span class="highlight">高性能</span>：在低竞争环境下性能优于传统锁</li>
                                <li><span class="highlight">无死锁风险</span>：不会因为锁获取顺序而引发死锁</li>
                                <li><span class="highlight">细粒度控制</span>：可以针对特定操作进行优化</li>
                            </ul>
                        </div>
                        
                        <div class="bg-gray-800 bg-opacity-30 p-6 rounded-xl border border-gray-700 card-hover">
                            <h4 class="text-lg font-medium mb-4 text-red-400">CAS的缺点</h4>
                            <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                <li><span class="highlight">ABA问题</span>：无法感知值的中间变化过程</li>
                                <li><span class="highlight">自旋开销</span>：高竞争环境下会导致大量CPU资源浪费</li>
                                <li><span class="highlight">只能保证单个变量的原子性</span>：复合操作需要额外处理</li>
                                <li><span class="highlight">可能导致饥饿</span>：理论上线程可能一直失败</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- CAS在Java中的实现 -->
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 mb-8 card-hover">
                        <h4 class="text-xl font-medium mb-4 text-blue-300">CAS在Java中的实现（Atomic类）</h4>
                        
                        <p class="mb-4">Java中的<code>java.util.concurrent.atomic</code>包提供了一系列原子类，它们内部使用CAS操作实现线程安全的原子更新。</p>
                        
                        <div class="grid md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <div class="bg-gray-900 p-4 rounded-lg overflow-hidden mb-4 text-sm">
                                    <div class="code-title">AtomicInteger示例</div>
                                    <pre><code class="language-java">AtomicInteger counter = new AtomicInteger(0);

// 原子递增并获取结果
int result = counter.incrementAndGet();

// 原子更新
boolean updated = counter.compareAndSet(result, 10);

// 非阻塞的原子操作
counter.updateAndGet(x -> x * 2);</code></pre>
                                </div>
                            </div>
                            
                            <div>
                                <div class="bg-gray-900 p-4 rounded-lg overflow-hidden text-sm">
                                    <div class="code-title">底层实现原理</div>
                                    <pre><code class="language-java">// AtomicInteger内部实现（简化版）
private volatile int value;

public final boolean compareAndSet(int expect, int update) {
    return unsafe.compareAndSwapInt(this, valueOffset, expect, update);
}

// unsafe.compareAndSwapInt最终调用CPU提供的原子指令
// 如x86的CMPXCHG</code></pre>
                                </div>
                            </div>
                        </div>
                        
                        <p class="text-gray-300">Java中的原子类通过<code>sun.misc.Unsafe</code>类提供的底层CAS方法来实现原子操作。这些方法最终会被编译为相应平台的原子指令，如x86架构上的<code>CMPXCHG</code>指令。</p>
                    </div>
                    
                    <!-- ABA问题 -->
                    <div class="bg-gray-800 bg-opacity-30 p-6 rounded-xl border border-gray-700 card-hover">
                        <h4 class="text-lg font-medium mb-4 text-yellow-400">CAS的ABA问题</h4>
                        
                        <p class="mb-4">ABA问题是CAS操作中的一个常见问题：如果一个值从A变成B，再变回A，CAS操作将无法检测到这个变化。</p>
                        
                        <div class="bg-gray-900 bg-opacity-60 p-4 rounded-lg mb-4">
                            <h5 class="font-medium mb-2 text-blue-300">ABA问题示例场景：</h5>
                            <ol class="list-decimal pl-5 space-y-1 text-gray-300">
                                <li>线程1读取内存位置X的值为A</li>
                                <li>线程1被阻塞</li>
                                <li>线程2将X的值从A修改为B，然后再修改回A</li>
                                <li>线程1恢复执行，发现X的值仍为A，CAS操作成功</li>
                                <li>但实际上值已经经历了A→B→A的变化</li>
                            </ol>
                        </div>
                        
                        <p class="mb-2">ABA问题的解决方案将在下一节的无锁编程基础中详细介绍。</p>
                    </div>
                </div>
                
                <!-- 2.2 无锁编程基础 - 在下一次更新中添加 -->
                <div>
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 mb-8 card-hover">
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <h4 class="text-xl font-medium mb-4 text-yellow-400">无锁编程的概念</h4>
                                <p class="mb-4">无锁编程是一种并发编程范式，它不使用传统的锁机制来保护共享资源，而是通过原子操作（如CAS）来实现线程安全。</p>
                                <p class="mb-4">无锁编程的核心思想是：</p>
                                <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                    <li>避免使用互斥锁，减少线程阻塞</li>
                                    <li>利用硬件级别的原子操作</li>
                                    <li>以乐观并发控制代替悲观并发控制</li>
                                    <li>让所有线程都能不断向前执行，不会被其他线程阻塞</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="text-xl font-medium mb-4 text-yellow-400">无锁并发的分类</h4>
                                <div class="space-y-4">
                                    <div class="bg-gray-900 bg-opacity-60 p-4 rounded-lg">
                                        <h5 class="font-medium mb-2 text-blue-300">无等待 (Wait-Free)</h5>
                                        <p class="text-gray-300 text-sm">所有线程的操作都能在有限步骤内完成，不会因为其他线程而被无限延迟。这是最强的保证，实现最困难。</p>
                                    </div>
                                    <div class="bg-gray-900 bg-opacity-60 p-4 rounded-lg">
                                        <h5 class="font-medium mb-2 text-blue-300">无锁 (Lock-Free)</h5>
                                        <p class="text-gray-300 text-sm">系统整体一直在向前执行，即使某些线程可能被延迟。保证至少有一个线程能够取得进展。</p>
                                    </div>
                                    <div class="bg-gray-900 bg-opacity-60 p-4 rounded-lg">
                                        <h5 class="font-medium mb-2 text-blue-300">无障碍 (Obstruction-Free)</h5>
                                        <p class="text-gray-300 text-sm">最弱的保证，如果一个线程在没有其他线程干扰的情况下独立执行，那么它最终能完成操作。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 无锁队列的实现原理 -->
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 mb-8 card-hover">
                        <h4 class="text-xl font-medium mb-4 text-blue-300">无锁队列的实现原理</h4>
                        
                        <p class="mb-6">无锁队列是无锁编程的经典应用，它通过原子操作实现了线程安全的入队和出队操作，无需使用互斥锁。</p>
                        
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <h5 class="font-medium mb-3 text-yellow-400">Michael-Scott无锁队列</h5>
                                <p class="mb-3 text-gray-300">一种经典的无锁队列实现，基于链表结构和CAS操作：</p>
                                <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                    <li>队列维护head和tail指针</li>
                                    <li>入队通过CAS操作更新tail.next和tail</li>
                                    <li>出队通过CAS操作更新head</li>
                                    <li>使用"哨兵节点"简化边界情况处理</li>
                                </ul>
                                
                                <div class="mt-4 p-3 bg-gray-900 rounded-lg border border-gray-700">
                                    <p class="text-sm text-gray-300"><span class="text-yellow-400">注意：</span>无锁队列的实现通常比锁实现更复杂，需要考虑更多并发场景和边界情况。</p>
                                </div>
                            </div>
                            
                            <div>
                                <div class="bg-gray-900 p-4 rounded-lg overflow-hidden text-sm">
                                    <div class="code-title">简化版无锁队列入队操作</div>
                                    <pre><code class="language-java">public void enqueue(T item) {
    Node<T> newNode = new Node<>(item, null);
    while (true) {
        Node<T> currentTail = tail.get();
        Node<T> tailNext = currentTail.next.get();
        
        if (currentTail == tail.get()) { // 检查tail是否稳定
            if (tailNext != null) {
                // 尾指针落后了，推进尾指针
                tail.compareAndSet(currentTail, tailNext);
            } else {
                // 尝试附加新节点
                if (currentTail.next.compareAndSet(null, newNode)) {
                    // 尝试推进尾指针
                    tail.compareAndSet(currentTail, newNode);
                    return; // 成功添加节点
                }
            }
        }
    }
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                             
                    <!-- ABA问题的解决方案 -->
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-xl backdrop-filter backdrop-blur-sm border border-gray-700 card-hover">
                        <h4 class="text-xl font-medium mb-6 text-blue-300">ABA问题的解决方案</h4>
                        
                        <div class="grid md:grid-cols-3 gap-8">
                            <!-- 版本号机制 -->
                            <div class="bg-gray-900 bg-opacity-40 p-6 rounded-xl border border-gray-700 card-hover">
                                <div class="text-yellow-400 text-3xl mb-4">
                                    <i class="ri-price-tag-3-line"></i>
                                </div>
                                <h5 class="text-lg font-medium mb-3 text-blue-300">版本号机制</h5>
                                <p class="text-gray-300 mb-3">为每次修改添加一个递增的版本号，检测时同时比较值和版本号。</p>
                                <div class="bg-gray-800 p-3 rounded-lg text-sm">
                                    <p class="text-blue-300">Java实现：<code>AtomicStampedReference</code></p>
                                    <p class="mt-2 text-gray-400">同时比较引用和时间戳，确保引用在此期间没有被修改过。</p>
                                </div>
                            </div>
                            
                            <!-- 双重CAS -->
                            <div class="bg-gray-900 bg-opacity-40 p-6 rounded-xl border border-gray-700 card-hover">
                                <div class="text-yellow-400 text-3xl mb-4">
                                    <i class="ri-stack-line"></i>
                                </div>
                                <h5 class="text-lg font-medium mb-3 text-blue-300">双重CAS</h5>
                                <p class="text-gray-300 mb-3">使用一个更宽的CAS操作，原子地更新两个相关值（如对象引用和版本号）。</p>
                                <div class="bg-gray-800 p-3 rounded-lg text-sm">
                                    <p class="text-blue-300">Java实现：<code>AtomicMarkableReference</code></p>
                                    <p class="mt-2 text-gray-400">维护一个boolean标记和引用，可用于标记节点的逻辑删除状态。</p>
                                </div>
                            </div>
                            
                            <!-- 内存回收策略 -->
                            <div class="bg-gray-900 bg-opacity-40 p-6 rounded-xl border border-gray-700 card-hover">
                                <div class="text-yellow-400 text-3xl mb-4">
                                    <i class="ri-recycle-line"></i>
                                </div>
                                <h5 class="text-lg font-medium mb-3 text-blue-300">内存回收策略</h5>
                                <p class="text-gray-300 mb-3">使用特殊的内存管理机制，确保被回收的内存不会立即被重用。</p>
                                <div class="bg-gray-800 p-3 rounded-lg text-sm">
                                    <p class="text-blue-300">典型实现：危险指针(Hazard Pointers)</p>
                                    <p class="mt-2 text-gray-400">允许线程标记它正在使用的指针，防止这些指针被其他线程释放。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 p-4 bg-blue-900 bg-opacity-20 rounded-lg border border-blue-700">
                            <h5 class="font-medium mb-2 flex items-center text-blue-300">
                                <i class="ri-lightbulb-line mr-2"></i>
                                <span>最佳实践提示</span>
                            </h5>
                            <p class="text-gray-300">在实际应用中，选择解决ABA问题的方案应根据具体场景考虑：</p>
                            <ul class="list-disc pl-5 mt-2 space-y-1 text-gray-300">
                                <li>如果对象生命周期可控，版本号机制通常是最简单有效的方案</li>
                                <li>对于复杂的无锁数据结构，考虑结合使用标记和危险指针</li>
                                <li>某些情况下可通过调整算法逻辑来规避ABA问题</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section> 