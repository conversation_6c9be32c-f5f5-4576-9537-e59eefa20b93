/* 自定义CSS样式 */
:root {
    --primary: #3b82f6;
    --primary-dark: #2563eb;
    --secondary: #a855f7;
    --dark: #111827;
    --darker: #0b0f1a;
    --light: #f3f4f6;
    --gray: #6b7280;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: var(--dark);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-dark);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

/* 顺滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 代码样式 */
code {
    font-family: 'Fira Code', monospace;
    background: rgba(0,0,0,0.2);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 0.9em;
}

pre {
    background: rgba(0,0,0,0.3);
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1.5rem 0;
    border-left: 4px solid var(--primary);
    position: relative;
}

/* 代码行号 */
pre.has-line-numbers {
    padding-left: 3.5rem;
}

.line-numbers {
    position: absolute;
    top: 0;
    left: 0;
    padding: 1rem 0.5rem;
    border-right: 1px solid rgba(75, 85, 99, 0.4);
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--gray);
    text-align: right;
    font-family: 'Fira Code', monospace;
    font-size: 0.9em;
    line-height: 1.5;
    user-select: none;
}

.line-number {
    display: block;
}

/* 卡片悬停效果 */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1);
}

/* 动画延迟 */
.delay-1 {
    animation-delay: 0.1s;
}
.delay-2 {
    animation-delay: 0.2s;
}
.delay-3 {
    animation-delay: 0.3s;
}
.delay-4 {
    animation-delay: 0.4s;
}

/* 粒子背景 */
#particles-js {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
}

/* 图表容器 */
.chart-container {
    background: rgba(17, 24, 39, 0.6);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(75, 85, 99, 0.4);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .mobile-menu {
        display: block;
    }
    
    h1 {
        font-size: 1.8rem !important;
    }
    
    h2 {
        font-size: 1.5rem !important;
    }
    
    h3 {
        font-size: 1.3rem !important;
    }
    
    pre {
        font-size: 0.8em;
    }
    
    .line-numbers {
        display: none;
    }
    
    pre.has-line-numbers {
        padding-left: 1rem;
    }
}

/* 主题切换动画 */
.theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 高亮重点内容 */
.highlight {
    position: relative;
    color: var(--primary);
    font-weight: 600;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary);
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.highlight:hover::after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

/* 特殊标记 */
.tag {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary);
    border-radius: 4px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* 进度指示器 */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    z-index: 1000;
    width: 0%;
}

/* 代码块标题 */
.code-title {
    background: rgba(0,0,0,0.4);
    color: var(--gray);
    padding: 0.5rem 1rem;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-size: 0.8rem;
    font-family: 'Fira Code', monospace;
    border-bottom: 1px solid rgba(75, 85, 99, 0.4);
}

.code-title + pre {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    margin-top: 0;
}

/* 导航栏动画 */
nav {
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

nav.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
}

/* 图表动画效果 */
.chart-container polyline {
    transition: stroke-dashoffset 2s ease;
}

/* 响应式SVG图表 */
svg {
    max-width: 100%;
    height: auto;
}

/* 移动菜单动画 */
.mobile-menu {
    transition: transform 0.3s ease-in-out;
}

/* 交互式卡片 */
.interactive-card {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.interactive-card::before {
    content: '';
    position: absolute;
    top: -100%;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: all 0.6s ease;
}

.interactive-card:hover::before {
    top: 100%;
    left: 100%;
}

.interactive-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.4);
}

/* 强调标题 */
.emphasis-title {
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
}

/* 动态下划线 */
.dynamic-underline {
    position: relative;
    display: inline-block;
}

.dynamic-underline::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.dynamic-underline:hover::after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

/* 反向强调文本 */
.inverse-emphasis {
    display: inline-block;
    position: relative;
    padding: 0 0.2em;
}

.inverse-emphasis::before {
    content: '';
    position: absolute;
    z-index: -1;
    top: 0;
    bottom: 0;
    left: -0.1em;
    right: -0.1em;
    background-color: var(--primary);
    transform: scaleY(0.3);
    transform-origin: bottom;
    transition: transform 0.3s ease;
}

.inverse-emphasis:hover::before {
    transform: scaleY(1);
}

/* 呼吸效果 */
.breathing-effect {
    animation: breathing 5s ease-in-out infinite;
}

@keyframes breathing {
    0%, 100% {
        box-shadow: 0 0 15px 0 rgba(59, 130, 246, 0.1);
    }
    50% {
        box-shadow: 0 0 30px 0 rgba(59, 130, 246, 0.3);
    }
}

/* 浮动元素 */
.floating {
    animation: floating 5s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* 代码块复制按钮 */
.copy-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(59, 130, 246, 0.2);
    color: var(--primary);
    border: none;
    border-radius: 4px;
    padding: 0.2rem 0.5rem;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0;
}

pre:hover .copy-button {
    opacity: 1;
}

.copy-button:hover {
    background-color: rgba(59, 130, 246, 0.4);
}
