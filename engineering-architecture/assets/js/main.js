document.addEventListener('DOMContentLoaded', () => {
    // 初始化粒子效果
    initParticles();
    
    // 初始化进度条
    initProgressBar();
});

// 粒子背景效果
function initParticles() {
    // 检查页面中是否有粒子容器
    if(document.getElementById('particles-js')) {
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 100,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#3b82f6"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    },
                    "polygon": {
                        "nb_sides": 5
                    }
                },
                "opacity": {
                    "value": 0.3,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 0.5,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": false,
                        "speed": 40,
                        "size_min": 0.1,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#3b82f6",
                    "opacity": 0.2,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 2,
                    "direction": "none",
                    "random": false,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "grab"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 140,
                        "line_linked": {
                            "opacity": 0.6
                        }
                    },
                    "bubble": {
                        "distance": 400,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        });
    }
}

// 滚动进度条
function initProgressBar() {
    // 创建进度条元素
    const progressBar = document.createElement('div');
    progressBar.className = 'progress-bar';
    document.body.appendChild(progressBar);
    
    // 更新进度条宽度
    window.addEventListener('scroll', () => {
        const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
        const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrolled = (winScroll / height) * 100;
        progressBar.style.width = scrolled + "%";
    });
}

// Function to initialize features dependent on dynamically loaded content
export function initializeDynamicContentFeatures() {
    console.log("Initializing features for dynamic content...");
    initScrollAnimations();
    highlightCode(); // Includes adding line numbers
    initInteractions();
    console.log("Dynamic content features initialized.");
}

// 滚动动画
function initScrollAnimations() {
    // 注册 GSAP 的 ScrollTrigger 插件
    gsap.registerPlugin(ScrollTrigger);
    
    // 为每个章节添加滚动动画
    document.querySelectorAll('section').forEach(section => {
        gsap.fromTo(section, 
            { opacity: 0, y: 50 }, 
            { 
                opacity: 1, 
                y: 0, 
                duration: 0.8, 
                scrollTrigger: {
                    trigger: section,
                    start: "top 80%",
                    end: "bottom 20%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });
    
    // 为卡片元素添加动画
    document.querySelectorAll('.card-hover').forEach(card => {
        gsap.fromTo(card, 
            { opacity: 0, y: 30 }, 
            { 
                opacity: 1, 
                y: 0, 
                duration: 0.5, 
                scrollTrigger: {
                    trigger: card,
                    start: "top 90%",
                    end: "bottom 20%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });
    
    // 为代码块添加动画
    document.querySelectorAll('pre code').forEach(codeBlock => {
        gsap.fromTo(codeBlock.parentElement, 
            { opacity: 0, x: -20 }, 
            { 
                opacity: 1, 
                x: 0, 
                duration: 0.6, 
                scrollTrigger: {
                    trigger: codeBlock.parentElement,
                    start: "top 90%",
                    end: "bottom 20%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });
}

// 代码高亮
function highlightCode() {
    // 如果页面上有代码块，使用highlight.js进行高亮
    if (typeof hljs !== 'undefined') {
        hljs.highlightAll();
        
        // 为所有代码块添加行号
        document.querySelectorAll('pre code').forEach(block => {
            const lines = block.innerHTML.split('\n').length;
            let lineNumbers = '';
            for (let i = 1; i < lines; i++) {
                lineNumbers += `<span class="line-number">${i}</span>\n`;
            }
            const lineNumbersDiv = document.createElement('div');
            lineNumbersDiv.className = 'line-numbers';
            lineNumbersDiv.innerHTML = lineNumbers;
            block.parentElement.classList.add('has-line-numbers');
            block.parentElement.insertBefore(lineNumbersDiv, block);
        });
    }
}

// 初始化交互效果
function initInteractions() {
    // 导航栏滚动效果
    const navbar = document.querySelector('nav');
    if (navbar) {
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                navbar.classList.add('bg-opacity-90');
                navbar.classList.add('shadow-lg');
            } else {
                navbar.classList.remove('bg-opacity-90');
                navbar.classList.remove('shadow-lg');
            }
        });
    }
    
    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 70, // 70px 是导航栏的高度
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // 移动端菜单
    const menuButton = document.querySelector('button.md\\:hidden');
    const mobileMenu = document.createElement('div');
    mobileMenu.className = 'mobile-menu fixed top-0 right-0 h-screen w-64 bg-gray-800 transform translate-x-full transition-transform duration-300 ease-in-out z-50 shadow-2xl';
    mobileMenu.innerHTML = `
        <div class="p-4 border-b border-gray-700">
            <button class="close-menu text-gray-300 hover:text-blue-400 float-right">
                <i class="ri-close-line text-xl"></i>
            </button>
            <div class="clear-both"></div>
        </div>
        <div class="p-4">
            <div class="flex flex-col space-y-4">
                <a href="#intro" class="text-gray-300 hover:text-blue-400 transition py-2">简介</a>
                <a href="#theory" class="text-gray-300 hover:text-blue-400 transition py-2">理论基础</a>
                <a href="#cases" class="text-gray-300 hover:text-blue-400 transition py-2">实践案例</a>
                <a href="#practices" class="text-gray-300 hover:text-blue-400 transition py-2">最佳实践</a>
                <a href="#discussion" class="text-gray-300 hover:text-blue-400 transition py-2">互动讨论</a>
            </div>
        </div>
    `;
    document.body.appendChild(mobileMenu);
    
    if (menuButton) {
        menuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('translate-x-full');
        });
        
        document.querySelector('.close-menu').addEventListener('click', () => {
            mobileMenu.classList.add('translate-x-full');
        });
        
        mobileMenu.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.add('translate-x-full');
            });
        });
    }
    
    // 添加响应式图表动画
    const charts = document.querySelectorAll('.chart-container');
    charts.forEach(chart => {
        ScrollTrigger.create({
            trigger: chart,
            start: "top 80%",
            onEnter: () => animateChart(chart)
        });
    });
}

// 图表动画效果
function animateChart(chartContainer) {
    // 为直方图添加进入动画
    chartContainer.querySelectorAll('[style*="height"]').forEach(bar => {
        const height = bar.style.height;
        gsap.fromTo(bar, 
            { height: 0 }, 
            { height: height, duration: 1, ease: "power2.out" }
        );
    });
    
    // 为折线图添加动画
    chartContainer.querySelectorAll('polyline').forEach(line => {
        const length = line.getTotalLength();
        
        line.style.strokeDasharray = length;
        line.style.strokeDashoffset = length;
        
        gsap.to(line, {
            strokeDashoffset: 0,
            duration: 2,
            ease: "power2.out"
        });
    });
    
    // 为散点添加动画
    chartContainer.querySelectorAll('circle').forEach((circle, index) => {
        gsap.fromTo(circle, 
            { opacity: 0, scale: 0 }, 
            { 
                opacity: 1, 
                scale: 1, 
                duration: 0.5,
                delay: index * 0.1,
                ease: "back.out(1.7)" 
            }
        );
    });
}

// 节流函数，用于优化滚动等高频事件
function throttle(func, delay) {
    let lastCall = 0;
    return function(...args) {
        const now = new Date().getTime();
        if (now - lastCall < delay) {
            return;
        }
        lastCall = now;
        return func(...args);
    };
}