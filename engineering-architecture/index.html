<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高并发场景下的线程安全与性能平衡</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <!-- 引入现代UI库 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- 引入动画库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <!-- 引入图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
 <!-- 代码高亮 -->
 <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
</head>
<body class="bg-gray-900 text-white">
    <div class="min-h-screen">
   <!-- 粒子背景 -->
   <div id="particles-js" class="fixed top-0 left-0 w-full h-full z-0"></div>
    
   <div class="min-h-screen relative z-10">
       <!-- 导航栏 -->
        <nav class="fixed top-0 w-full bg-gray-800 bg-opacity-80 backdrop-filter backdrop-blur-lg z-50 border-b border-gray-700">
            <div class="container mx-auto px-4 py-3 flex justify-between items-center">
                <div class="text-xl font-bold text-blue-400">
                    <i class="ri-code-s-slash-line mr-2"></i>技术分享
                </div>
                <div class="hidden md:flex space-x-6">
                    <a href="#intro" class="text-gray-300 hover:text-blue-400 transition">简介</a>
                    <a href="#theory" class="text-gray-300 hover:text-blue-400 transition">理论基础</a>
                    <a href="#cases" class="text-gray-300 hover:text-blue-400 transition">实践案例</a>
                    <a href="#practices" class="text-gray-300 hover:text-blue-400 transition">最佳实践</a>
                    <a href="#discussion" class="text-gray-300 hover:text-blue-400 transition">互动讨论</a>
                </div>
                  <!-- 移动端菜单按钮 -->
                  <button class="md:hidden text-gray-300 hover:text-blue-400">
                    <i class="ri-menu-line text-xl"></i>
                </button>
            </div>
        </nav>

        <!-- 头部/标题区 -->
        <header class="pt-32 pb-20 px-4 relative overflow-hidden">
            <div class="container mx-auto">
                <h1 class="text-4xl md:text-5xl font-bold mb-6 text-blue-400 tracking-tight animate__animated animate__fadeInUp">高并发场景下的线程安全与性能平衡</h1>
                <p class="text-lg md:text-xl text-gray-300 max-w-3xl animate__animated animate__fadeInUp animate__delay-1s">一种优雅的解决方案——探索如何在高并发系统中平衡线程安全与性能的艺术</p>
            </div>
            
            <!-- 背景装饰 -->
            <div class="absolute -bottom-10 -right-10 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate__animated animate__pulse animate__infinite animate__slow"></div>
            <div class="absolute top-20 -left-20 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate__animated animate__pulse animate__infinite animate__slow"></div>
        </header>

        <!-- 内容区域 -->
        <main class="container mx-auto px-4 py-12 relative z-10">
            <div id="intro-container"></div>
            <div id="theory-container"></div>
            <div id="cases-container"></div>
            <div id="practices-container"></div>
            <div id="discussion-container"></div>
            <div id="references-container"></div>
        </main>

        <!-- 页脚 -->
        <footer class="bg-gray-800 py-8 border-t border-gray-700 relative z-10">
            <div class="container mx-auto px-4 text-gray-400 text-center">
                <p>© 2023 技术分享 | 高并发场景下的线程安全与性能平衡</p>
            </div>
        </footer>
    </div>

    <!-- 引入JS -->
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script type="module">
        // Import the function from main.js
        import { initializeDynamicContentFeatures } from './assets/js/main.js';

        document.addEventListener('DOMContentLoaded', () => {
            const sections = [
                { id: 'intro-container', path: 'sections/intro.html' },
                { id: 'theory-container', path: 'sections/theory.html' },
                { id: 'cases-container', path: 'sections/cases.html' },
                { id: 'practices-container', path: 'sections/practices.html' },
                { id: 'discussion-container', path: 'sections/discussion.html' },
                { id: 'references-container', path: 'sections/references.html' }
            ];

            const fetchPromises = sections.map(section => 
                fetch(section.path)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status} for ${section.path}`);
                        }
                        return response.text();
                    })
                    .then(html => {
                        const container = document.getElementById(section.id);
                        if (container) {
                            container.innerHTML = html;
                        } else {
                            console.error(`Container with id ${section.id} not found.`);
                        }
                    })
                    .catch(error => {
                        console.error(`Failed to load section ${section.path}:`, error);
                        const container = document.getElementById(section.id);
                        if (container) {
                            container.innerHTML = `<p class="text-red-500">Error loading content for ${section.path}.</p>`;
                        }
                    })
            );

            Promise.all(fetchPromises)
                .then(() => {
                    console.log("All sections loaded.");
                    // Initialize highlighting after all content is loaded
                    if (typeof hljs !== 'undefined') {
                        hljs.highlightAll(); 
                        console.log("Highlighting applied.");
                    } else {
                        console.warn("highlight.js not loaded, skipping highlighting.");
                    }
                    
                    // Initialize features from main.js that depend on the loaded content
                    initializeDynamicContentFeatures(); 

                })
                .catch(error => {
                    console.error("Error loading one or more sections:", error);
                });
        });
    </script>
    <script src="assets/js/main.js" type="module"></script>
</body>
</html>