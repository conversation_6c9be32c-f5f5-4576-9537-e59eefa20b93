import http.server
import socketserver
import logging
import urllib.parse
import json

# 配置日志记录器
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class LogRequestHandler(http.server.SimpleHTTPRequestHandler):
    def log_request_details(self):
        logging.info(f"Request Method: {self.command}")
        logging.info(f"Request Path: {self.path}")

        # 记录请求头
        logging.info("Request Headers:")
        for header, value in self.headers.items():
            logging.info(f"  {header}: {value}")

        # 解析 URL 参数 (仅针对 GET 请求)
        if self.command == 'GET':
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            if query_params:
                logging.info("Request Param¡eters:")
                for key, values in query_params.items():
                    logging.info(f"  {key}: {values}")

        # 读取请求体 (针对 POST, PUT 等请求)
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            request_body = self.rfile.read(content_length).decode('utf-8')
            try:
                # 尝试解析 JSON body，如果不是 JSON 则直接记录
                body_json = json.loads(request_body)
                logging.info("Request Body (JSON):")
                logging.info(json.dumps(body_json, indent=2)) # 格式化 JSON 输出
            except json.JSONDecodeError:
                logging.info("Request Body (Text):")
                logging.info(request_body)


    def do_GET(self):
        self.log_request_details()
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b"Request logged")

    def do_POST(self):
        self.log_request_details()
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b"Request logged")

    def do_PUT(self):
        self.log_request_details()
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b"Request logged")

    def do_DELETE(self):
        self.log_request_details()
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b"Request logged")

def run_server(port=8000):
    Handler = LogRequestHandler
    with socketserver.TCPServer(("", port), Handler) as httpd:
        print(f"Serving at port {port}")
        logging.info(f"Server started at port {port}")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            pass
        httpd.server_close()
        logging.info("Server stopped.")

if __name__ == "__main__":
    run_server()