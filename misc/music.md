
## 给我一些适合写代码时候听的音乐 , 能帮助人专注 激发创造

以下是一些适合编程时听的音乐推荐：

### 1. 电子音乐
- **Lo-fi Hip Hop**
  ```
  推荐频道：
  - Lofi Girl
  - Nujabes
  - ChilledCow
  ```

- **环境音乐（Ambient）**
  ```
  艺术家：
  - <PERSON>
  - <PERSON>
  - <PERSON>
  ```

### 2. 游戏原声带（OST）
```
推荐作品：
├── Minecraft - C418
├── FEZ - Disasterpeace
├── Journey - Austin Wintory
└── Cyberpunk 2077 - Various Artists
```

### 3. 古典音乐
```
推荐曲目：
├── 巴赫 - 哥德堡变奏曲
├── 莫扎特 - 钢琴奏鸣曲
└── 肖邦 - 夜曲系列
```

### 4. 白噪音/自然声音
```
环境音：
├── 雨声
├── 咖啡馆环境音
├── 海浪声
└── 森林环境音
```

### 5. 数学摇滚/后摇
```
推荐乐队：
├── 65daysofstatic
├── Mogwai
└── God Is An Astronaut
```

### 6. 极简音乐
```
艺术家：
├── Philip Glass
├── Steve Reich
└── Max Richter
```

### 平台推荐
1. **音乐平台**
```
├── Spotify
├── SoundCloud
├── Brain.fm
└── Focus@Will
```

2. **白噪音平台**
```
├── Noisli
├── MyNoise
└── A Soft Murmur
```

### 使用建议

1. **根据任务调整**
```
任务类型：
├── 复杂算法 -> 极简音乐
├── 日常编码 -> Lo-fi
├── Debug -> 轻音乐
└── 创意设计 -> 环境音乐
```

2. **音量控制**
- 保持在背景音量水平
- 避免歌词干扰
- 使用噪音消除耳机

3. **创建播放列表**
```
分类方式：
├── 按编程语言
├── 按任务类型
└── 按时间段
```

4. **注意事项**
- 避免过于熟悉的音乐
- 定期更新播放列表
- 注意听觉疲劳

这些音乐类型都经过实践证明能够提高编程效率和创造力。建议您尝试不同类型，找到最适合自己的组合。
