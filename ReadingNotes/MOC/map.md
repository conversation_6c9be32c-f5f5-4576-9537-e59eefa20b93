  

**基础模型**

scaling law 任然有效

大模型还会提升

多模态理解同意；多模态生成是长期趋势

模型结构和训练范式的持续创新

逻辑推理、复杂代码能力大幅提升

大小模型协同进化

  

数据

1. 数据价值的挖掘
2. 合成数据
3. 数据飞轮

  

智能体

1. 自主规划
2. 多智能体协同
3. 智能体应用

  

多模态

1. 多模态生成
2. 数字人结合

  

  

业务支持

  

1. 大模型使用逐渐深入

  

  

技术

产品系统层、模型层、数据生产、数据加工数据应用

  

业务数据

内容、需求（用户query、需求创意）、行为反馈（面对结果的行为）、偏好反馈（多个结果中选择）

数据建设

筛选、加工、标注、AI合成

训练数据

场景数据、

模型优化

  

自动评估

  

  

25年展望

> 数据标注
1. 任务难度持续提升
2. 专业性要求持续提高
3. 业务支持模式多样
4. 快速迭代
5. 认知不断提升，存量数据优化

> 数据工程（提效）
1. 数据工程平台：数据、工具、信息 **中心化**
2. 数据飞轮：数据、生产、标注、上车、评估 **全流程**
3. 重点场景：理解业务、解决业务 **痛点**
4. 使用门槛：新人、新策略、新场景 **易用性**