from PyPDF2 import PdfReader, PdfWriter

def split_pdf(input_path, output_path, pages):
    """
    切分PDF文件，只保留指定页码
    
    参数:
        input_path: 输入PDF文件路径
        output_path: 输出PDF文件路径
        pages: 需要保留的页码列表 (从1开始)
    """
    # 创建 PDF 读取器
    reader = PdfReader(input_path)
    
    # 创建 PDF 写入器
    writer = PdfWriter()
    
    # 添加指定页码的页面
    for page_num in pages:
        # PyPDF2的页码从0开始，所以需要减1
        if 0 <= page_num-1 < len(reader.pages):
            writer.add_page(reader.pages[page_num-1])
    
    # 保存新的PDF文件
    with open(output_path, 'wb') as output_file:
        writer.write(output_file)

# 使用示例
path = "/Users/<USER>/Desktop/_my/qwq调研/"
input_file = path + "2412.15115.pdf"
output_file = path + "2412.15115_1.pdf"
# 指定要保留的页码，比如保留第1、2、5页

# pages_to_keep = list(range(1, 4)) + list(range(7, 10))  # [1, 2, 3, 7, 8, 9]

# pages_to_keep = [1, 2, 5]

pages_to_keep = list(range(1, 20))
split_pdf(input_file, output_file, pages_to_keep)