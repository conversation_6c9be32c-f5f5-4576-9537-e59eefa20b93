# 详细讲解一下mha、mqa、gqa的区别实现的异同
![alt text](image.png)
> https://verticalserve.medium.com/group-query-attention-58283b337c65

让我为您详细分析MHA (Multi-Head Attention)、MQA (Multi-Query Attention)和GQA (Grouped-Query Attention)的区别：

### 1. MHA (Multi-Head Attention)
```python
class MultiHeadAttention:
    def __init__(self, num_heads, d_model):
        self.num_heads = num_heads
        self.d_model = d_model
        self.d_head = d_model // num_heads
        
        # 每个头都有独立的Q、K、V投影矩阵
        self.W_q = nn.Linear(d_model, d_model)  # [h × (d_model × d_head)]
        self.W_k = nn.Linear(d_model, d_model)  # [h × (d_model × d_head)]
        self.W_v = nn.Linear(d_model, d_model)  # [h × (d_model × d_head)]
```

特点：
- 每个注意力头都有独立的Q、K、V参数
- 参数量：`3 × d_model × d_model × num_heads`
- 内存占用最大，但表达能力最强

### 2. MQA (Multi-Query Attention)
```python
class MultiQueryAttention:
    def __init__(self, num_heads, d_model):
        self.num_heads = num_heads
        self.d_model = d_model
        self.d_head = d_model // num_heads
        
        # Q有多个头，但K、V只有一个
        self.W_q = nn.Linear(d_model, d_model)      # [h × (d_model × d_head)]
        self.W_k = nn.Linear(d_model, self.d_head)  # [d_model × d_head]
        self.W_v = nn.Linear(d_model, self.d_head)  # [d_model × d_head]
```

特点：
- Q保持多头，K和V共享一个头
- 参数量：`d_model × d_model + 2 × d_model × (d_model/num_heads)`
- 显著减少参数量和内存占用

### 3. GQA (Grouped-Query Attention)
```python
class GroupedQueryAttention:
    def __init__(self, num_heads, num_kv_heads, d_model):
        self.num_heads = num_heads
        self.num_kv_heads = num_kv_heads  # num_kv_heads < num_heads
        self.d_model = d_model
        self.d_head = d_model // num_heads
        
        # Q有多个头，K、V有较少的头数
        self.W_q = nn.Linear(d_model, d_model)                    # [h × (d_model × d_head)]
        self.W_k = nn.Linear(d_model, d_model // (num_heads/num_kv_heads))  # [g × (d_model × d_head)]
        self.W_v = nn.Linear(d_model, d_model // (num_heads/num_kv_heads))  # [g × (d_model × d_head)]
```

特点：
- Q保持原有头数，K和V使用较少的头数
- 参数量介于MHA和MQA之间
- 平衡了计算效率和模型表达能力

### 主要区别对比：

1. **参数效率**：
   - MHA: 参数量最大
   - MQA: 参数量最小
   - GQA: 参数量适中

2. **计算复杂度**：
```python
# MHA
attention_scores = torch.matmul(Q, K.transpose(-2, -1)) * scaling  # 每个头独立计算
attention_probs = F.softmax(attention_scores, dim=-1)
output = torch.matmul(attention_probs, V)

# MQA/GQA
# K、V被多个Q头共享，减少计算量
attention_scores = torch.matmul(Q, K.transpose(-2, -1).expand(...)) * scaling
attention_probs = F.softmax(attention_scores, dim=-1)
output = torch.matmul(attention_probs, V.expand(...))
```

3. **内存使用**：
```python
# 假设序列长度为L，批次大小为B
# MHA: O(B × L × L × num_heads)
# MQA: O(B × L × L)
# GQA: O(B × L × L × num_kv_heads)
```

4. **应用场景**：
- MHA：适用于需要强大表达能力的场景
- MQA：适用于资源受限、需要高效推理的场景
- GQA：适用于需要在性能和效率间平衡的场景

### 实现示例：

```python
def attention_pattern(q, k, v, num_heads, num_kv_heads=None):
    """
    统一的注意力计算框架
    """
    B, L, D = q.shape
    H = num_heads
    KV_H = num_kv_heads if num_kv_heads else num_heads
    
    # 根据不同模式重塑K、V
    if num_kv_heads is None:  # MHA
        k = k.view(B, L, H, -1)
        v = v.view(B, L, H, -1)
    elif num_kv_heads == 1:   # MQA
        k = k.view(B, L, 1, -1).expand(B, L, H, -1)
        v = v.view(B, L, 1, -1).expand(B, L, H, -1)
    else:                     # GQA
        k = k.view(B, L, KV_H, -1).repeat_interleave(H // KV_H, dim=2)
        v = v.view(B, L, KV_H, -1).repeat_interleave(H // KV_H, dim=2)
    
    # 注意力计算
    q = q.view(B, L, H, -1)
    scores = torch.matmul(q, k.transpose(-2, -1))
    attn = F.softmax(scores, dim=-1)
    output = torch.matmul(attn, v)
    
    return output.view(B, L, -1)
```

这三种注意力机制各有优势，选择哪种取决于具体的应用场景和资源限制。
