https://arxiv.org/abs/2412.17596

# Introduction
虽然大型语言模型（LLMs）在科学任务中表现出了非凡的能力，但现有的评估框架主要使用丰富的上下文输入来评估它们的表现，而忽略了它们从最少的信息中产生新想法的能力。我们介绍LiveIdeaBench，这是一个综合基准，使用单个关键字提示评估法学硕士的科学创造力和发散思维能力。借鉴吉尔福德的创造力理论，我们的框架采用了一个由最先进的法学硕士组成的动态小组，从四个关键维度评估产生的想法：原创性、可行性、流畅性和灵活性。通过对跨越18个科学领域的1180个关键词的20个领先模型进行广泛实验，我们发现科学创造能力与一般智力指标显示出不同的模式。值得注意的是，我们的结果表明，尽管QwQ-32B预览等模型的一般智力得分存在显著差距，但它们的创意表现与o1预览等顶级模型相当。这些发现强调了专业评估框架对科学创造力的重要性，并表明法学硕士的创造力发展可能遵循与传统问题解决能力不同的轨迹。

# 开篇总结
### 研究背景与动机
1. **现有评估框架的局限性**：
   - 目前的LLM评估主要基于丰富的上下文输入
   - 忽视了模型从最小信息生成新想法的能力
   - 这种局限性与科学发现的本质（常常源于有限信息的创造性突破）不符

2. **科学创新的特点**：
   - 科学进步依赖于创造性思维和新假设的产生
   - 许多重大科学发现源于对有限信息的创造性联想

### LiveIdeaBench框架创新
1. **评估方法**：
   - 使用单关键词提示来测试模型
   - 基于Guilford创造力理论
   - 评估四个维度：原创性、可行性、流畅度和灵活性

2. **实验规模**：
   - 测试了20个主流语言模型
   - 涵盖1,180个关键词
   - 跨越18个科学领域

### 主要发现
1. **模型表现**：
   - 科学创造力与一般智能指标显示出不同的模式
   - QwQ-32B-preview等模型在创造力方面可以与顶级模型（如AI-preview）相媲美
   - 即使一些模型在通用智能测试中得分较低，其创造力表现仍可能出色

### 研究意义
1. **理论价值**：
   - 揭示了LLM创造能力的发展可能遵循不同于传统问题解决能力的轨迹
   - 为评估AI科学创造力提供了新的框架

2. **实践意义**：
   - 有助于更好地理解和评估AI在科学创新中的潜力
   - 为开发更具创造力的AI系统提供了新思路

这项研究来自中国人民大学高瓴人工智能学院和浙江大学，为评估AI模型的科学创造力提供了一个新的重要视角。


# 评估步骤分析

![alt text](image.png)


让我为您详细解析LiveIdeaBench的评估流程：

### 1. 输入阶段
- 使用1000+个科学关键词作为输入
- 这些关键词来自不同科学领域（图中显示如天气预报、实验器材等）

### 2. 想法生成阶段
- **Idea LLM处理**：
  - 将单个关键词输入到模型中
  - 模型通过发散思维生成多个相关想法（A、B、C等）
  - 强调从最小信息生成创新性想法的能力

### 3. 评估阶段
- **Judge LLMs评估**：
  - 使用专门的评判模型对生成的想法进行评估
  - 评估三个核心维度：
    1. 原创性（Originality）
    2. 可行性（Feasibility）
    3. 流畅度（Fluency）：通过比较同一关键词生成的不同想法来评估

### 4. 打分系统
- 每个想法获得具体分数：
  - Orig.（原创性分数）
  - Feas.（可行性分数）
  - Flue.（流畅度分数）
  - Avg.（平均分）

### 5. 灵活性评估
- 计算方式：取30%百分位数的平均值
- 示例中显示灵活性得分为6.11

### 6. 结果汇总
- **两个主要输出**：
  1. LiveIdeaBench排行榜
     - 展示不同模型的综合表现
     - 目前评估了20+个主流模型
  2. LiveIdeaBench数据集
     - 收集所有生成的想法
     - 包含关键词、评分等详细信息

### 7. 特色亮点
- 强调发散思维而非收敛思维
- 使用最小上下文（单关键词）进行评估
- 采用多模型评判机制
- 评估框架完整且系统化

### 8. 数据获取
- 数据集发布在HuggingFace
- 排行榜可在LiveIdeaBench官网查看

这个评估框架的设计非常系统化，通过多维度评估来全面衡量AI模型在科学创新方面的能力，特别强调了在最小信息输入条件下的创造力表现。
