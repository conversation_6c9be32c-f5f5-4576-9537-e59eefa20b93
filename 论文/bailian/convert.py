import pandas as pd
import json

def convert_jsonl(input_file, output_file):
    data = []
    with open(input_file, 'r') as f:
        for line in f:
            try:
                data.append(json.loads(line))
            except json.JSONDecodeError:
                print(f"Error decoding JSON: {line}")
                continue

    df = pd.DataFrame(data)
    
    output_data = []
    with open(output_file, 'w') as f:
        for index, row in df.iterrows():
            msg={
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant"},
                    {"role": "user", "content": row['question']},
                    {"role": "assistant", "content": row['combined_text']}
                ]
            }
            f.write(json.dumps(msg) + '\n')

  

if __name__ == "__main__":
    convert_jsonl('bailian/public_long_form_thought_data_5k.jsonl', 'bailian/out_1.jsonl')