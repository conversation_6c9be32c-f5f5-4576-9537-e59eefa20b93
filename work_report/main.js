document.addEventListener('DOMContentLoaded', () => {
  // 初始化AOS动画库
  AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: false
  });

  // 工作报告数据
  const reportData = {
    title: "工作进展汇报",
    subtitle: "2025年第一季度",
    author: {
      name: "张博文",
      position: "后端开发工程师"
    },
    progress: [
      {
        title: "统一存储开发",
        period: "近三个月",
        completionRate: 95,
        items: [
          "文件目录存储系统的开发",
          "数据集相关功能的开发",
          "数据集市分享&权限功能开发"
        ]
      },
      {
        title: "易用性方面的升级调整",
        period: "近三个月",
        completionRate: 85,
        items: []
      },
      {
        title: "千帆非旗舰日志的引入与数据分析",
        period: "近三个月",
        completionRate: 90,
        items: [
          "数据分布挖掘分析"
        ]
      }
    ],
    challenges: [
      {
        title: "多种不同数据源的存储方案",
        description: "需要统一处理各种不同格式和来源的数据",
        severity: "高"
      },
      {
        title: "大文件的diff方案",
        description: "实现高效的大文件对比和版本控制",
        severity: "中"
      },
      {
        title: "平台统一问题",
        description: "包括消息队列、接口网关（链路跟踪）等系统的统一",
        severity: "中"
      },
    ],
    support: [
      {
        title: "服务器监控/日志采集组件",
        description: "目前监控有Prometheus, 日志采集bls要申请(收费,价格很低)",
        status: "待申请"
      }
    ],
    career: [
      {
        title: "专注编码工作",
        description: "编码对我来说比较有价值感"
      },
      {
        title: "参与一言项目",
        description: "看好一言, 帮助一言迭代越来越好"
      }
    ],
    suggestions: [
      {
        title: "技术栈优化",
        description: "后端工程开发感觉尽量还是不要用python, 性能差是一方面, 还特别容易写出来面条代码又细又长, 扩展性可读性都不好,很难维护"
      },
      {
        title: "UI开发流程改进",
        description: "个人感觉一定程度上可以不需要ui或者ui只参与部分图标或者少量的样式设计. 主体的交互有大量现成案例可以借鉴, AI工具可以生成."
      }
    ]
  };

  // 渲染页面内容
  renderApp(reportData);

  // 初始化图表
  initCharts(reportData);

  // 添加滚动效果
  addScrollEffects();
});

// 渲染整个应用内容
function renderApp(data) {
  const appDiv = document.getElementById('app');
  
  appDiv.innerHTML = `
    ${renderHeader(data)}
    ${renderProgress(data.progress)}
    ${renderChallenges(data.challenges)}
    ${renderSupport(data.support)}
    ${renderCareerAndSuggestions(data.career, data.suggestions)}
  `;
}

// 渲染页面头部
function renderHeader(data) {
  return `
    <header class="mb-20 text-center animate__animated animate__fadeIn">
      <div class="animate-float">
        <div class="inline-block p-3 rounded-full bg-gradient-to-r from-purple-500 to-indigo-500 mb-8">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
      </div>
      <h1 class="text-5xl font-bold mb-4 text-gradient">${data.title}</h1>
      <p class="text-2xl text-indigo-200 mb-6">${data.subtitle}</p>
      <div class="inline-block px-6 py-3 rounded-lg bg-glass mb-4">
        <p class="text-xl">${data.author.name} | ${data.author.position}</p>
      </div>
    </header>
  `;
}

// 渲染工作进展部分
function renderProgress(progressItems) {
  return `
    <section class="mb-20" data-aos="fade-up">
      <h2 class="text-3xl font-bold mb-8 border-l-4 border-purple-500 pl-4">工作进展</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
        ${progressItems.map(item => `
          <div class="card p-6">
            <h3 class="text-xl font-bold mb-3">${item.title}</h3>
            <p class="text-indigo-200 mb-4">${item.period}</p>
            <div class="mb-3">
              <div class="flex justify-between mb-1">
                <span>完成度</span>
                <span>${item.completionRate}%</span>
              </div>
              <div class="w-full bg-gray-700 rounded-full h-2">
                <div class="progress-bar h-2 rounded-full" style="width: ${item.completionRate}%"></div>
              </div>
            </div>
            ${item.items.length ? `
              <ul class="mt-4 space-y-2">
                ${item.items.map(subItem => `
                  <li class="flex items-start">
                    <svg class="h-5 w-5 text-green-400 mr-2 mt-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>${subItem}</span>
                  </li>
                `).join('')}
              </ul>
            ` : ''}
          </div>
        `).join('')}
      </div>
      <div class="relative pl-10">
        <div class="absolute left-0 top-0 bottom-0 w-1 bg-indigo-900"></div>
        ${progressItems.map((item, index) => `
          <div class="timeline-item relative pl-8 pb-10 mb-6" data-aos="fade-right" data-aos-delay="${index * 100}">
            <h3 class="text-xl font-bold mb-2">${item.title}</h3>
            <p class="text-indigo-200">${item.period}</p>
            ${item.items.length ? `
              <ul class="mt-4 space-y-2">
                ${item.items.map(subItem => `
                  <li class="text-gray-300">${subItem}</li>
                `).join('')}
              </ul>
            ` : ''}
          </div>
        `).join('')}
      </div>
    </section>
  `;
}

// 渲染挑战部分
function renderChallenges(challenges) {
  return `
    <section class="mb-20" data-aos="fade-up">
      <h2 class="text-3xl font-bold mb-8 border-l-4 border-purple-500 pl-4">遇到的问题和挑战</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
        ${challenges.map((challenge, index) => `
          <div class="card p-6" data-aos="zoom-in" data-aos-delay="${index * 100}">
            <div class="flex items-center mb-4">
              <div class="p-3 rounded-full mr-4 ${getSeverityColor(challenge.severity)}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold">${challenge.title}</h3>
            </div>
            <p class="text-gray-300 mb-3">${challenge.description}</p>
            <span class="inline-block px-3 py-1 rounded-full text-sm ${getSeverityBadgeColor(challenge.severity)}">
              优先级: ${challenge.severity}
            </span>
          </div>
        `).join('')}
      </div>
      <div class="bg-glass rounded-xl p-6">
        <h3 class="text-xl font-bold mb-4">挑战分布</h3>
        <canvas id="challengesChart" height="200"></canvas>
      </div>
    </section>
  `;
}

// 渲染支持和资源部分
function renderSupport(supportItems) {
  return `
    <section class="mb-20" data-aos="fade-up">
      <h2 class="text-3xl font-bold mb-8 border-l-4 border-purple-500 pl-4">我需要的支持和资源</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        ${supportItems.map(item => `
          <div class="card p-6 flex">
            <div class="p-3 rounded-full mr-4 bg-amber-500 self-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-2">${item.title}</h3>
              <p class="text-gray-300 mb-3">${item.description}</p>
              <span class="inline-block px-3 py-1 rounded-full text-sm bg-amber-900 text-amber-200">
                状态: ${item.status}
              </span>
            </div>
          </div>
        `).join('')}
      </div>
    </section>
  `;
}

// 渲染职业发展和建议部分
function renderCareerAndSuggestions(career, suggestions) {
  return `
    <section class="grid grid-cols-1 md:grid-cols-2 gap-10 mb-20">
      <div data-aos="fade-right">
        <h2 class="text-3xl font-bold mb-8 border-l-4 border-purple-500 pl-4">职业发展想法</h2>
        <div class="space-y-6">
          ${career.map((item, index) => `
            <div class="card p-6" data-aos="fade-up" data-aos-delay="${index * 100}">
              <h3 class="text-xl font-bold mb-3">${item.title}</h3>
              <p class="text-gray-300">${item.description}</p>
            </div>
          `).join('')}
        </div>
      </div>
      <div data-aos="fade-left">
        <h2 class="text-3xl font-bold mb-8 border-l-4 border-purple-500 pl-4">对团队的建议</h2>
        <div class="space-y-6">
          ${suggestions.map((item, index) => `
            <div class="card p-6" data-aos="fade-up" data-aos-delay="${index * 100}">
              <h3 class="text-xl font-bold mb-3">${item.title}</h3>
              <p class="text-gray-300">${item.description}</p>
            </div>
          `).join('')}
        </div>
      </div>
    </section>
  `;
}

// 初始化图表
function initCharts(data) {
  setTimeout(() => {
    // 挑战分布图表
    const challengesCtx = document.getElementById('challengesChart').getContext('2d');
    
    // 计算不同优先级的挑战数量
    const severityCounts = {
      '高': 0,
      '中': 0,
      '低': 0
    };
    
    data.challenges.forEach(challenge => {
      severityCounts[challenge.severity]++;
    });
    
    new Chart(challengesCtx, {
      type: 'polarArea',
      data: {
        labels: ['高优先级', '中优先级', '低优先级'],
        datasets: [{
          label: '挑战分布',
          data: [severityCounts['高'], severityCounts['中'], severityCounts['低']],
          backgroundColor: [
            'rgba(239, 68, 68, 0.7)',
            'rgba(245, 158, 11, 0.7)',
            'rgba(16, 185, 129, 0.7)'
          ],
          borderColor: [
            'rgba(239, 68, 68, 1)',
            'rgba(245, 158, 11, 1)',
            'rgba(16, 185, 129, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'right',
            labels: {
              color: 'white'
            }
          }
        }
      }
    });
  }, 500);
}

// 添加滚动效果
function addScrollEffects() {
  const cards = document.querySelectorAll('.card');
  
  window.addEventListener('scroll', () => {
    cards.forEach(card => {
      const cardTop = card.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;
      
      if (cardTop < windowHeight * 0.8) {
        card.classList.add('animate__animated', 'animate__fadeInUp');
      }
    });
  });
}

// 获取严重性对应的颜色
function getSeverityColor(severity) {
  switch (severity) {
    case '高':
      return 'bg-red-500';
    case '中':
      return 'bg-amber-500';
    case '低':
      return 'bg-green-500';
    default:
      return 'bg-blue-500';
  }
}

// 获取严重性对应的徽章颜色
function getSeverityBadgeColor(severity) {
  switch (severity) {
    case '高':
      return 'bg-red-900 text-red-200';
    case '中':
      return 'bg-amber-900 text-amber-200';
    case '低':
      return 'bg-green-900 text-green-200';
    default:
      return 'bg-blue-900 text-blue-200';
  }
} 