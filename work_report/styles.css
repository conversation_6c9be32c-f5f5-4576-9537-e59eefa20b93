:root {
  --primary: #3b82f6;
  --secondary: #10b981;
  --accent: #6366f1;
  --success: #22c55e;
  --warning: #f59e0b;
  --danger: #ef4444;
}

body {
  font-family: 'Noto Sans SC', sans-serif;
  overflow-x: hidden;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.progress-bar {
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(90deg, var(--primary), var(--accent));
  transition: width 1s ease-in-out;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -32px;
  top: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent);
  border: 3px solid rgba(255, 255, 255, 0.5);
  z-index: 1;
}

.timeline-item::after {
  content: '';
  position: absolute;
  left: -23px;
  top: 20px;
  bottom: -20px;
  width: 2px;
  background: rgba(255, 255, 255, 0.2);
  z-index: 0;
}

.timeline-item:last-child::after {
  display: none;
}

.bg-glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.skill-bar {
  height: 10px;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  border-radius: 5px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
}

.text-gradient {
  background: linear-gradient(90deg, #f0abfc, #818cf8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
} 