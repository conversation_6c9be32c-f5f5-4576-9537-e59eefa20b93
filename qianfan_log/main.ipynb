{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Chattype 分布情况:\n", "chat_type\n", "9       22390\n", "6        6442\n", "2004     2658\n", "3          84\n", "7          77\n", "2002       32\n", "8          28\n", "10         19\n", "4          17\n", "2001       14\n", "1          10\n", "2003        9\n", "5           1\n", "Name: count, dtype: int64\n", "\n", "Chattype 分布百分比:\n", "chat_type\n", "9       70.450898\n", "6       20.269973\n", "2004     8.363488\n", "3        0.264309\n", "7        0.242283\n", "2002     0.100689\n", "8        0.088103\n", "10       0.059784\n", "4        0.053491\n", "2001     0.044051\n", "1        0.031465\n", "2003     0.028319\n", "5        0.003147\n", "Name: count, dtype: float64 %\n", "\n", "行大小统计 (字节):\n", "平均大小: 8355.25 字节\n", "最大大小: 337114 字节\n", "最小大小: 347 字节\n", "\n", "行大小统计 (KB):\n", "平均大小: 8.16 KB\n", "最大大小: 329.21 KB\n", "最小大小: 0.34 KB\n", "\n", "行大小分布描述 (字节):\n", "count     31781.000000\n", "mean       8355.252824\n", "std        8980.410291\n", "min         347.000000\n", "25%        2427.000000\n", "50%        5281.000000\n", "75%       11963.000000\n", "max      337114.000000\n", "dtype: float64\n"]}], "source": ["import pandas as pd\n", "import json\n", "from datetime import datetime\n", "\n", "class DateTimeEncoder(json.JSONEncoder):\n", "    def default(self, obj):\n", "        if isinstance(obj, (datetime, pd.Timestamp)):\n", "            return obj.isoformat()\n", "        return super().default(obj)\n", "\n", "def analyze_jsonl(file_path):\n", "    df = pd.read_json(file_path, lines=True)\n", "    \n", "    # 分析 chattype 的分布\n", "    chattype_distribution = df['chat_type'].value_counts()\n", "    print(\"\\nChattype 分布情况:\")\n", "    print(chattype_distribution)\n", "    print(\"\\nChattype 分布百分比:\")\n", "    print(chattype_distribution / len(df) * 100, \"%\")\n", "    \n", "    # 计算每行的字节大小\n", "    def get_line_size(row):\n", "        json_str = json.dumps(row.to_dict(), cls=DateTimeEncoder)\n", "        return len(json_str.encode('utf-8'))  # 转换为字节计算大小\n", "    \n", "    line_sizes = df.apply(get_line_size, axis=1)\n", "    \n", "    print(\"\\n行大小统计 (字节):\")\n", "    print(f\"平均大小: {line_sizes.mean():.2f} 字节\")\n", "    print(f\"最大大小: {line_sizes.max()} 字节\")\n", "    print(f\"最小大小: {line_sizes.min()} 字节\")\n", "    \n", "    # 转换为 KB 的统计\n", "    print(\"\\n行大小统计 (KB):\")\n", "    print(f\"平均大小: {line_sizes.mean()/1024:.2f} KB\")\n", "    print(f\"最大大小: {line_sizes.max()/1024:.2f} KB\")\n", "    print(f\"最小大小: {line_sizes.min()/1024:.2f} KB\")\n", "    \n", "    print(\"\\n行大小分布描述 (字节):\")\n", "    print(line_sizes.describe())\n", "\n", "# 使用示例\n", "file_path = \"fedd9351-c631-488b-8e7a-8ff60a1cb945.jsonl\"\n", "analyze_jsonl(file_path)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Model 分布情况:\n", "model\n", "ERNIE-Lite-128K-0722             10860\n", "ERNIE-Lite-8K-0308               10658\n", "ERNIE-Tiny-8K                     9902\n", "ERNIE-Speed-8K                    2359\n", "ernie-4.0-turbo-8k                1117\n", "ERNIE-BLM-4K                       818\n", "completions_pro                    257\n", "ernie-lite-8k                      246\n", "ernie-4.0-turbo-8k-0927            166\n", "ernie_speed                        154\n", "ernie-tiny-8k                      125\n", "ERNIE-Lite-128K-0419                35\n", "ERNIE-Lite-32K                      35\n", "ernie-4.0-turbo-8k-lowlatency       31\n", "ernie-lite-pro-128k                 27\n", "ERNIE-Character-Fiction-8K          18\n", "ERNIE-Speed-128K                    14\n", "ERNIE-BLM-Tiny-8K                   12\n", "ernie-4.0-turbo-8k-0628              7\n", "ERNIE-BLM-Pro-8K                     4\n", "ernie-speed-pro-128k                 4\n", "ERNIE-3.5-8K                         3\n", "ERNIE-Character-8K-0321              3\n", "completions_yiyan_eb4                3\n", "ai_apaas                             2\n", "qianfan-agent-speed-8k               1\n", "ernie-4.0-turbo-8k-stable            1\n", "ERNIE-BLM-8K                         1\n", "ERNIE-Lite-8K-0725                   1\n", "ernie-4.0-8k-0613                    1\n", "Name: count, dtype: int64\n", "\n", "Model 分布百分比:\n", "model\n", "ERNIE-Lite-128K-0722             29.458836\n", "ERNIE-Lite-8K-0308               28.910891\n", "ERNIE-Tiny-8K                    26.860165\n", "ERNIE-Speed-8K                    6.399023\n", "ernie-4.0-turbo-8k                3.029974\n", "ERNIE-BLM-4K                      2.218907\n", "completions_pro                   0.697138\n", "ernie-lite-8k                     0.667300\n", "ernie-4.0-turbo-8k-0927           0.450292\n", "ernie_speed                       0.417740\n", "ernie-tiny-8k                     0.339075\n", "ERNIE-Lite-128K-0419              0.094941\n", "ERNIE-Lite-32K                    0.094941\n", "ernie-4.0-turbo-8k-lowlatency     0.084091\n", "ernie-lite-pro-128k               0.073240\n", "ERNIE-Character-Fiction-8K        0.048827\n", "ERNIE-Speed-128K                  0.037976\n", "ERNIE-BLM-Tiny-8K                 0.032551\n", "ernie-4.0-turbo-8k-0628           0.018988\n", "ERNIE-BLM-Pro-8K                  0.010850\n", "ernie-speed-pro-128k              0.010850\n", "ERNIE-3.5-8K                      0.008138\n", "ERNIE-Character-8K-0321           0.008138\n", "completions_yiyan_eb4             0.008138\n", "ai_apaas                          0.005425\n", "qianfan-agent-speed-8k            0.002713\n", "ernie-4.0-turbo-8k-stable         0.002713\n", "ERNIE-BLM-8K                      0.002713\n", "ERNIE-Lite-8K-0725                0.002713\n", "ernie-4.0-8k-0613                 0.002713\n", "Name: count, dtype: float64 %\n"]}], "source": ["import pandas as pd\n", "import json\n", "from datetime import datetime\n", "\n", "class DateTimeEncoder(json.JSONEncoder):\n", "    def default(self, obj):\n", "        if isinstance(obj, (datetime, pd.Timestamp)):\n", "            return obj.isoformat()\n", "        return super().default(obj)\n", "\n", "def analyze_jsonl(file_path):\n", "    df = pd.read_json(file_path, lines=True)\n", "    \n", "    # 统计并分析 不同 model 出现次数\n", "    model_distribution = df['model'].value_counts()\n", "    print(\"\\nModel 分布情况:\")\n", "    print(model_distribution)\n", "    print(\"\\nModel 分布百分比:\")\n", "    print(model_distribution / len(df) * 100, \"%\")\n", "\n", "\n", "# 使用示例\n", "file_path = \"part-00996-91dd47b0-28dc-450c-8c67-1d7929c8ac3d-c000.txt\"\n", "analyze_jsonl(file_path)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 统计信息 ===\n", "总记录数: 4431\n", "包含system的记录数: 0\n", "\n", "System值分布:\n", "Series([], Name: count, dtype: int64)\n", "==================================================\n", "\n", "原始记录已导出到: system_records_20241224_160857.txt\n", "\n", "调试信息:\n", "system_df 的前几条记录:\n", "Empty DataFrame\n", "Columns: [system]\n", "Index: []\n"]}], "source": ["import pandas as pd\n", "import json\n", "from datetime import datetime\n", "\n", "def analyze_and_export_system():\n", "    # 读取日志文件并解析JSON\n", "    records = []\n", "    with open('output-340556067-1.txt', 'r', encoding='utf-8') as file:\n", "        for line in file:\n", "            try:\n", "                records.append(json.loads(line))\n", "            except:\n", "                continue\n", "    \n", "    # 转换为DataFrame\n", "    df = pd.DataFrame(records)\n", "    \n", "    # 提取 request.body.system\n", "    df['system'] = df['request'].apply(lambda x: x.get('body', {}).get('system', None) if isinstance(x, dict) else None)\n", "    \n", "    # 筛选有system值的记录\n", "    system_df = df[df['system'].notna()]\n", "    \n", "    # 控制台输出统计信息\n", "    print(f\"\\n=== 统计信息 ===\")\n", "    print(f\"总记录数: {len(df)}\")\n", "    print(f\"包含system的记录数: {len(system_df)}\")\n", "    print(\"\\nSystem值分布:\")\n", "    print(system_df['system'].value_counts())\n", "    print(\"=\" * 50)\n", "    \n", "    # 导出记录到文件\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_file = f'system_records_{timestamp}.txt'\n", "    \n", "    # 将原始记录写入文件\n", "    with open(output_file, 'w', encoding='utf-8') as f:\n", "        for _, row in system_df.iterrows():\n", "            # 获取原始记录\n", "            original_record = row.to_dict()\n", "            # 确保只保留需要的字段\n", "            output_record = {\n", "                'timestamp': original_record.get('timestamp'),\n", "                'request': original_record.get('request'),\n", "                'response': original_record.get('response')\n", "            }\n", "            json.dump(original_record, f, ensure_ascii=False)\n", "            f.write('\\n')\n", "    \n", "    print(f\"\\n原始记录已导出到: {output_file}\")\n", "    \n", "    # 调试信息\n", "    print(f\"\\n调试信息:\")\n", "    print(f\"system_df 的前几条记录:\")\n", "    print(system_df[['system']].head())\n", "    \n", "    return system_df\n", "\n", "# 运行分析\n", "df = analyze_and_export_system()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'total_lines': 47530, 'lines_with_appid': 42, 'filling_rate': '0.09%'}\n"]}], "source": ["# bos原始数据分析\n", "import json\n", "def analyze_appid_filling_rate(file_path):\n", "    total_lines = 0\n", "    lines_with_appid = 0\n", "    \n", "    with open(file_path, 'r') as f:\n", "        for line in f:\n", "            total_lines += 1\n", "            try:\n", "                data = json.loads(line)\n", "                # if 'appid' in data and data['appid']:  # 检查appid字段是否存在且非空\n", "                if 'app_id' in data['request']['body'] and data['request']['body']['app_id']:  # 检查appid字段是否存在且非空\n", "                    lines_with_appid += 1\n", "            except json.JSONDecodeError:\n", "                continue\n", "    \n", "    filling_rate = (lines_with_appid / total_lines * 100) if total_lines > 0 else 0\n", "    return {\n", "        'total_lines': total_lines,\n", "        'lines_with_appid': lines_with_appid,\n", "        'filling_rate': f\"{filling_rate:.2f}%\"\n", "    }\n", "    \n", "\n", "file_path = \"/Users/<USER>/Desktop/_my/tmp/user-data/reflow/20241219/output-330174563-0.txt\"\n", "result = analyze_appid_filling_rate(file_path)\n", "print(result)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'total_lines': 425270, 'unique_appids': 242, 'distribution': [{'appid': 116109671, 'count': 117903, 'percentage': '27.72%'}, {'appid': 56653635, 'count': 97307, 'percentage': '22.88%'}, {'appid': 33864701, 'count': 48970, 'percentage': '11.52%'}, {'appid': 116125692, 'count': 22323, 'percentage': '5.25%'}, {'appid': 68602567, 'count': 15186, 'percentage': '3.57%'}, {'appid': 41226587, 'count': 14217, 'percentage': '3.34%'}, {'appid': 73075492, 'count': 12849, 'percentage': '3.02%'}, {'appid': 116136222, 'count': 11174, 'percentage': '2.63%'}, {'appid': 31512728, 'count': 10587, 'percentage': '2.49%'}, {'appid': 112080989, 'count': 6445, 'percentage': '1.52%'}, {'appid': 69223842, 'count': 6154, 'percentage': '1.45%'}, {'appid': 96364157, 'count': 5630, 'percentage': '1.32%'}, {'appid': 62122847, 'count': 4600, 'percentage': '1.08%'}, {'appid': 48462468, 'count': 4593, 'percentage': '1.08%'}, {'appid': 74348419, 'count': 4420, 'percentage': '1.04%'}, {'appid': 47344820, 'count': 3627, 'percentage': '0.85%'}, {'appid': 46787808, 'count': 2854, 'percentage': '0.67%'}, {'appid': 116113110, 'count': 2817, 'percentage': '0.66%'}, {'appid': 106650576, 'count': 2792, 'percentage': '0.66%'}, {'appid': 45037774, 'count': 2568, 'percentage': '0.60%'}, {'appid': 62129735, 'count': 2321, 'percentage': '0.55%'}, {'appid': 97543553, 'count': 2124, 'percentage': '0.50%'}, {'appid': 39954566, 'count': 1783, 'percentage': '0.42%'}, {'appid': 101078323, 'count': 1528, 'percentage': '0.36%'}, {'appid': 116544764, 'count': 1420, 'percentage': '0.33%'}, {'appid': 82307474, 'count': 1302, 'percentage': '0.31%'}, {'appid': 79653283, 'count': 1235, 'percentage': '0.29%'}, {'appid': 47344828, 'count': 1010, 'percentage': '0.24%'}, {'appid': 37159800, 'count': 992, 'percentage': '0.23%'}, {'appid': 93784710, 'count': 962, 'percentage': '0.23%'}, {'appid': 115953995, 'count': 949, 'percentage': '0.22%'}, {'appid': 115640789, 'count': 930, 'percentage': '0.22%'}, {'appid': 115930216, 'count': 859, 'percentage': '0.20%'}, {'appid': 116224413, 'count': 855, 'percentage': '0.20%'}, {'appid': 115516443, 'count': 765, 'percentage': '0.18%'}, {'appid': 83616519, 'count': 741, 'percentage': '0.17%'}, {'appid': 62263451, 'count': 616, 'percentage': '0.14%'}, {'appid': 47437766, 'count': 517, 'percentage': '0.12%'}, {'appid': 64064106, 'count': 490, 'percentage': '0.12%'}, {'appid': 58325115, 'count': 405, 'percentage': '0.10%'}, {'appid': 116691462, 'count': 360, 'percentage': '0.08%'}, {'appid': 56324772, 'count': 356, 'percentage': '0.08%'}, {'appid': 31710413, 'count': 351, 'percentage': '0.08%'}, {'appid': 46591760, 'count': 283, 'percentage': '0.07%'}, {'appid': 46250888, 'count': 283, 'percentage': '0.07%'}, {'appid': 37684756, 'count': 229, 'percentage': '0.05%'}, {'appid': 44879984, 'count': 214, 'percentage': '0.05%'}, {'appid': 116836517, 'count': 195, 'percentage': '0.05%'}, {'appid': 104263627, 'count': 182, 'percentage': '0.04%'}, {'appid': 45898348, 'count': 179, 'percentage': '0.04%'}, {'appid': 35465805, 'count': 172, 'percentage': '0.04%'}, {'appid': 110912460, 'count': 165, 'percentage': '0.04%'}, {'appid': 57072684, 'count': 161, 'percentage': '0.04%'}, {'appid': 99133996, 'count': 158, 'percentage': '0.04%'}, {'appid': 116113068, 'count': 151, 'percentage': '0.04%'}, {'appid': 68626086, 'count': 143, 'percentage': '0.03%'}, {'appid': 68916158, 'count': 139, 'percentage': '0.03%'}, {'appid': 115563398, 'count': 137, 'percentage': '0.03%'}, {'appid': 112098398, 'count': 120, 'percentage': '0.03%'}, {'appid': 55378089, 'count': 113, 'percentage': '0.03%'}, {'appid': 84927627, 'count': 113, 'percentage': '0.03%'}, {'appid': 43428606, 'count': 109, 'percentage': '0.03%'}, {'appid': 115640071, 'count': 94, 'percentage': '0.02%'}, {'appid': 57072468, 'count': 88, 'percentage': '0.02%'}, {'appid': 116078930, 'count': 85, 'percentage': '0.02%'}, {'appid': 31576989, 'count': 75, 'percentage': '0.02%'}, {'appid': 47134629, 'count': 73, 'percentage': '0.02%'}, {'appid': 38931567, 'count': 66, 'percentage': '0.02%'}, {'appid': 97383269, 'count': 66, 'percentage': '0.02%'}, {'appid': 58420576, 'count': 63, 'percentage': '0.01%'}, {'appid': 52349111, 'count': 58, 'percentage': '0.01%'}, {'appid': 34726682, 'count': 56, 'percentage': '0.01%'}, {'appid': 34815538, 'count': 49, 'percentage': '0.01%'}, {'appid': 60403427, 'count': 47, 'percentage': '0.01%'}, {'appid': 115704800, 'count': 46, 'percentage': '0.01%'}, {'appid': 116102605, 'count': 44, 'percentage': '0.01%'}, {'appid': 41715578, 'count': 42, 'percentage': '0.01%'}, {'appid': 84876255, 'count': 40, 'percentage': '0.01%'}, {'appid': 49185205, 'count': 38, 'percentage': '0.01%'}, {'appid': 62037691, 'count': 38, 'percentage': '0.01%'}, {'appid': '115860386', 'count': 37, 'percentage': '0.01%'}, {'appid': '55253133', 'count': 36, 'percentage': '0.01%'}, {'appid': 57056034, 'count': 35, 'percentage': '0.01%'}, {'appid': 47749270, 'count': 32, 'percentage': '0.01%'}, {'appid': 78875967, 'count': 32, 'percentage': '0.01%'}, {'appid': 116165509, 'count': 30, 'percentage': '0.01%'}, {'appid': 116343665, 'count': 29, 'percentage': '0.01%'}, {'appid': 42685563, 'count': 29, 'percentage': '0.01%'}, {'appid': 31575770, 'count': 26, 'percentage': '0.01%'}, {'appid': 115840918, 'count': 26, 'percentage': '0.01%'}, {'appid': 37415261, 'count': 23, 'percentage': '0.01%'}, {'appid': 63380397, 'count': 23, 'percentage': '0.01%'}, {'appid': 116321493, 'count': 23, 'percentage': '0.01%'}, {'appid': 32401878, 'count': 23, 'percentage': '0.01%'}, {'appid': 115878564, 'count': 20, 'percentage': '0.00%'}, {'appid': 62731250, 'count': 20, 'percentage': '0.00%'}, {'appid': 32805172, 'count': 20, 'percentage': '0.00%'}, {'appid': 62248850, 'count': 19, 'percentage': '0.00%'}, {'appid': 108030787, 'count': 18, 'percentage': '0.00%'}, {'appid': 95114951, 'count': 18, 'percentage': '0.00%'}, {'appid': 39617197, 'count': 18, 'percentage': '0.00%'}, {'appid': 45357210, 'count': 18, 'percentage': '0.00%'}, {'appid': 39617152, 'count': 15, 'percentage': '0.00%'}, {'appid': 44879554, 'count': 15, 'percentage': '0.00%'}, {'appid': 116337359, 'count': 14, 'percentage': '0.00%'}, {'appid': 39342389, 'count': 14, 'percentage': '0.00%'}, {'appid': 34137482, 'count': 12, 'percentage': '0.00%'}, {'appid': '39752075', 'count': 11, 'percentage': '0.00%'}, {'appid': 116669195, 'count': 11, 'percentage': '0.00%'}, {'appid': 60743405, 'count': 11, 'percentage': '0.00%'}, {'appid': 116320605, 'count': 11, 'percentage': '0.00%'}, {'appid': 116662961, 'count': 11, 'percentage': '0.00%'}, {'appid': 87467506, 'count': 11, 'percentage': '0.00%'}, {'appid': 51986819, 'count': 10, 'percentage': '0.00%'}, {'appid': 40882708, 'count': 10, 'percentage': '0.00%'}, {'appid': 86593325, 'count': 9, 'percentage': '0.00%'}, {'appid': 38198955, 'count': 9, 'percentage': '0.00%'}, {'appid': 39617262, 'count': 8, 'percentage': '0.00%'}, {'appid': 76464912, 'count': 8, 'percentage': '0.00%'}, {'appid': 73925778, 'count': 8, 'percentage': '0.00%'}, {'appid': 43518051, 'count': 8, 'percentage': '0.00%'}, {'appid': 116497079, 'count': 8, 'percentage': '0.00%'}, {'appid': 43516939, 'count': 8, 'percentage': '0.00%'}, {'appid': 116040824, 'count': 8, 'percentage': '0.00%'}, {'appid': 47124372, 'count': 7, 'percentage': '0.00%'}, {'appid': 35751033, 'count': 7, 'percentage': '0.00%'}, {'appid': 36764496, 'count': 7, 'percentage': '0.00%'}, {'appid': 43518479, 'count': 7, 'percentage': '0.00%'}, {'appid': 95562719, 'count': 7, 'percentage': '0.00%'}, {'appid': 37715652, 'count': 6, 'percentage': '0.00%'}, {'appid': 43921888, 'count': 6, 'percentage': '0.00%'}, {'appid': 46870833, 'count': 6, 'percentage': '0.00%'}, {'appid': 37318294, 'count': 5, 'percentage': '0.00%'}, {'appid': 76838073, 'count': 5, 'percentage': '0.00%'}, {'appid': 116083895, 'count': 5, 'percentage': '0.00%'}, {'appid': 35635940, 'count': 5, 'percentage': '0.00%'}, {'appid': 47737212, 'count': 5, 'percentage': '0.00%'}, {'appid': 59530711, 'count': 5, 'percentage': '0.00%'}, {'appid': 44289497, 'count': 5, 'percentage': '0.00%'}, {'appid': 48740332, 'count': 5, 'percentage': '0.00%'}, {'appid': 47742723, 'count': 4, 'percentage': '0.00%'}, {'appid': 60702740, 'count': 4, 'percentage': '0.00%'}, {'appid': 33932050, 'count': 4, 'percentage': '0.00%'}, {'appid': 36154513, 'count': 4, 'percentage': '0.00%'}, {'appid': 67923472, 'count': 4, 'percentage': '0.00%'}, {'appid': 42663083, 'count': 4, 'percentage': '0.00%'}, {'appid': 71289016, 'count': 4, 'percentage': '0.00%'}, {'appid': 60363889, 'count': 4, 'percentage': '0.00%'}, {'appid': 37522909, 'count': 4, 'percentage': '0.00%'}, {'appid': 32110615, 'count': 4, 'percentage': '0.00%'}, {'appid': 70020181, 'count': 4, 'percentage': '0.00%'}, {'appid': 59430696, 'count': 4, 'percentage': '0.00%'}, {'appid': 116306503, 'count': 4, 'percentage': '0.00%'}, {'appid': 33342711, 'count': 3, 'percentage': '0.00%'}, {'appid': 68255192, 'count': 3, 'percentage': '0.00%'}, {'appid': 94891882, 'count': 3, 'percentage': '0.00%'}, {'appid': 31998516, 'count': 3, 'percentage': '0.00%'}, {'appid': 46408262, 'count': 3, 'percentage': '0.00%'}, {'appid': 39274635, 'count': 3, 'percentage': '0.00%'}, {'appid': 78819578, 'count': 3, 'percentage': '0.00%'}, {'appid': 115976576, 'count': 3, 'percentage': '0.00%'}, {'appid': 100323685, 'count': 3, 'percentage': '0.00%'}, {'appid': 117018429, 'count': 3, 'percentage': '0.00%'}, {'appid': 100408833, 'count': 3, 'percentage': '0.00%'}, {'appid': 47210673, 'count': 3, 'percentage': '0.00%'}, {'appid': 116206243, 'count': 2, 'percentage': '0.00%'}, {'appid': 101128070, 'count': 2, 'percentage': '0.00%'}, {'appid': 36491030, 'count': 2, 'percentage': '0.00%'}, {'appid': 100760374, 'count': 2, 'percentage': '0.00%'}, {'appid': 101128440, 'count': 2, 'percentage': '0.00%'}, {'appid': 54214976, 'count': 2, 'percentage': '0.00%'}, {'appid': 45227513, 'count': 2, 'percentage': '0.00%'}, {'appid': 56047939, 'count': 2, 'percentage': '0.00%'}, {'appid': 116310647, 'count': 2, 'percentage': '0.00%'}, {'appid': 36343735, 'count': 2, 'percentage': '0.00%'}, {'appid': 116126399, 'count': 2, 'percentage': '0.00%'}, {'appid': 57223727, 'count': 2, 'percentage': '0.00%'}, {'appid': 47743043, 'count': 2, 'percentage': '0.00%'}, {'appid': 43519452, 'count': 2, 'percentage': '0.00%'}, {'appid': 116113091, 'count': 2, 'percentage': '0.00%'}, {'appid': 115976592, 'count': 2, 'percentage': '0.00%'}, {'appid': 116591579, 'count': 2, 'percentage': '0.00%'}, {'appid': 60702498, 'count': 2, 'percentage': '0.00%'}, {'appid': 37159658, 'count': 2, 'percentage': '0.00%'}, {'appid': 116937197, 'count': 2, 'percentage': '0.00%'}, {'appid': 115935877, 'count': 2, 'percentage': '0.00%'}, {'appid': 116195550, 'count': 2, 'percentage': '0.00%'}, {'appid': 78890073, 'count': 2, 'percentage': '0.00%'}, {'appid': 100760565, 'count': 2, 'percentage': '0.00%'}, {'appid': 116639521, 'count': 2, 'percentage': '0.00%'}, {'appid': 42155188, 'count': 2, 'percentage': '0.00%'}, {'appid': 115942660, 'count': 1, 'percentage': '0.00%'}, {'appid': 105469473, 'count': 1, 'percentage': '0.00%'}, {'appid': 53493842, 'count': 1, 'percentage': '0.00%'}, {'appid': 64703331, 'count': 1, 'percentage': '0.00%'}, {'appid': 31989690, 'count': 1, 'percentage': '0.00%'}, {'appid': 41138889, 'count': 1, 'percentage': '0.00%'}, {'appid': 115939075, 'count': 1, 'percentage': '0.00%'}, {'appid': 42538692, 'count': 1, 'percentage': '0.00%'}, {'appid': 57973316, 'count': 1, 'percentage': '0.00%'}, {'appid': 47268271, 'count': 1, 'percentage': '0.00%'}, {'appid': 69290240, 'count': 1, 'percentage': '0.00%'}, {'appid': 39618764, 'count': 1, 'percentage': '0.00%'}, {'appid': 69291881, 'count': 1, 'percentage': '0.00%'}, {'appid': 115935871, 'count': 1, 'percentage': '0.00%'}, {'appid': 111643556, 'count': 1, 'percentage': '0.00%'}, {'appid': 81158942, 'count': 1, 'percentage': '0.00%'}, {'appid': 70007133, 'count': 1, 'percentage': '0.00%'}, {'appid': 44827467, 'count': 1, 'percentage': '0.00%'}, {'appid': 43628174, 'count': 1, 'percentage': '0.00%'}, {'appid': 116084254, 'count': 1, 'percentage': '0.00%'}, {'appid': 43359547, 'count': 1, 'percentage': '0.00%'}, {'appid': 41782617, 'count': 1, 'percentage': '0.00%'}, {'appid': 32512721, 'count': 1, 'percentage': '0.00%'}, {'appid': 54164581, 'count': 1, 'percentage': '0.00%'}, {'appid': 33936376, 'count': 1, 'percentage': '0.00%'}, {'appid': 64459472, 'count': 1, 'percentage': '0.00%'}, {'appid': 85235742, 'count': 1, 'percentage': '0.00%'}, {'appid': 116825289, 'count': 1, 'percentage': '0.00%'}, {'appid': 90553044, 'count': 1, 'percentage': '0.00%'}, {'appid': 60702735, 'count': 1, 'percentage': '0.00%'}, {'appid': 83963391, 'count': 1, 'percentage': '0.00%'}, {'appid': 58451686, 'count': 1, 'percentage': '0.00%'}, {'appid': 115960133, 'count': 1, 'percentage': '0.00%'}, {'appid': 90907962, 'count': 1, 'percentage': '0.00%'}, {'appid': 112084036, 'count': 1, 'percentage': '0.00%'}, {'appid': 116243103, 'count': 1, 'percentage': '0.00%'}, {'appid': 63341368, 'count': 1, 'percentage': '0.00%'}, {'appid': 116546818, 'count': 1, 'percentage': '0.00%'}, {'appid': 39668801, 'count': 1, 'percentage': '0.00%'}, {'appid': 115987571, 'count': 1, 'percentage': '0.00%'}, {'appid': 112228171, 'count': 1, 'percentage': '0.00%'}, {'appid': 42113213, 'count': 1, 'percentage': '0.00%'}, {'appid': 100764058, 'count': 1, 'percentage': '0.00%'}, {'appid': 34724944, 'count': 1, 'percentage': '0.00%'}, {'appid': 34520750, 'count': 1, 'percentage': '0.00%'}, {'appid': 35378943, 'count': 1, 'percentage': '0.00%'}, {'appid': 57724999, 'count': 1, 'percentage': '0.00%'}, {'appid': 60224991, 'count': 1, 'percentage': '0.00%'}, {'appid': 115935879, 'count': 1, 'percentage': '0.00%'}, {'appid': 48006800, 'count': 1, 'percentage': '0.00%'}, {'appid': 45284004, 'count': 1, 'percentage': '0.00%'}]}\n"]}], "source": ["def analyze_appid_distribution(file_path):\n", "    appid_counts = {}\n", "    total_lines = 0\n", "    \n", "    # 读取文件统计每个appid的出现次数\n", "    with open(file_path, 'r') as f:\n", "        for line in f:\n", "            total_lines += 1\n", "            try:\n", "                data = json.loads(line)\n", "                appid = data.get('appid')\n", "                if appid:\n", "                    appid_counts[appid] = appid_counts.get(appid, 0) + 1\n", "            except json.JSONDecodeError:\n", "                continue\n", "    \n", "    # 计算每个appid的占比并排序\n", "    distribution = []\n", "    for appid, count in appid_counts.items():\n", "        percentage = (count / total_lines) * 100\n", "        distribution.append({\n", "            'appid': appid,\n", "            'count': count,\n", "            'percentage': f\"{percentage:.2f}%\"\n", "        })\n", "    \n", "    # 按出现次数降序排序\n", "    distribution.sort(key=lambda x: x['count'], reverse=True)\n", "    \n", "    return {\n", "        'total_lines': total_lines,\n", "        'unique_appids': len(appid_counts),\n", "        'distribution': distribution\n", "    }\n", "    \n", "\n", "file_path = \"/Users/<USER>/Desktop/_my/qianfan_log/bos_data_analysis/tmp/user-data/reflow/20250113/output-3762934634-0.txt\"\n", "result = analyze_appid_distribution(file_path)\n", "print(result)    "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}