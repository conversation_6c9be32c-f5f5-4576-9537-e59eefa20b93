import os
import json
from pathlib import Path
from typing import Generator, Dict, Any
import pandas as pd
from collections import Counter
from tqdm import tqdm

class FileProcessor:
    """文件处理基类"""
    def __init__(self, input_dir: str, output_dir: str):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        os.makedirs(output_dir, exist_ok=True)
    
    def yield_files(self) -> Generator[Path, None, None]:
        """遍历目录下所有文件"""
        for file in self.input_dir.rglob('*'):
            if file.is_file():
                yield file
    
    def yield_json_lines(self, file_path: Path) -> Generator[Dict[str, Any], None, None]:
        """遍历文件中的 JSON 行"""
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    yield json.loads(line.strip())
                except json.JSONDecodeError:
                    continue

class DataFilter(FileProcessor):
    """数据过滤处理"""
    def __init__(self, input_dir: str, output_dir: str, filter_conditions: Dict):
        super().__init__(input_dir, output_dir)
        self.filter_conditions = filter_conditions
    
    def check_conditions(self, data: Dict) -> bool:
        """检查数据是否满足过滤条件"""
        try:
            for key, condition in self.filter_conditions.items():
                if callable(condition):
                    if not condition(data):
                        return False
                elif key not in data or data[key] != condition:
                    return False
            return True
        except Exception:
            return False
    
    def process(self, output_file: str):
        """处理并过滤数据"""
        output_path = self.output_dir / output_file
        
        with open(output_path, 'w', encoding='utf-8') as f_out:
            for file in tqdm(list(self.yield_files()), desc="处理文件"):
                for data in self.yield_json_lines(file):
                    if self.check_conditions(data):
                        f_out.write(json.dumps(data, ensure_ascii=False) + '\n')

class DataAnalyzer:
    """数据分析类"""
    def __init__(self, input_file: str, output_dir: str):
        self.input_file = Path(input_file)
        self.output_dir = Path(output_dir)
        os.makedirs(output_dir, exist_ok=True)
        
    def analyze_field(self, field_path: list, name: str, top_n: int = 10) -> pd.DataFrame:
        """分析特定字段的分布"""
        counter = Counter()
        
        def get_nested_value(data: Dict, path: list) -> Any:
            """获取嵌套字典中的值"""
            for key in path:
                if isinstance(data, dict):
                    data = data.get(key, {})
                else:
                    return None
            return data
        
        # 统计数据
        with open(self.input_file, 'r') as f:
            for line in tqdm(f, desc=f"分析 {name}"):
                try:
                    data = json.loads(line)
                    value = get_nested_value(data, field_path)
                    if value:
                        counter[value] += 1
                except json.JSONDecodeError:
                    continue
        
        # 创建DataFrame
        total = sum(counter.values())
        df = pd.DataFrame({
            name: list(counter.keys()),
            '出现次数': list(counter.values()),
            '占比(%)': [round(count/total*100, 2) for count in counter.values()]
        }).sort_values('出现次数', ascending=False)
        
        # 保存结果
        output_file = self.output_dir / f'{name}_stats.csv'
        if len(df) > top_n:
            df.head(top_n).to_csv(output_file, index=False, encoding='utf-8')
        else:
            df.to_csv(output_file, index=False, encoding='utf-8')
        
        return df

def main():
    # 配置参数
    input_dir = "/path/to/input"
    output_dir = "/path/to/output"
    temp_file = "filtered_data.jsonl"
    
    # 定义过滤条件
    filter_conditions = {
        'system_label': lambda x: 'system_label' in x,
        'model': lambda x: 'char' in x.get('model', '').lower()
    }
    
    # 1. 过滤数据
    filter_processor = DataFilter(input_dir, output_dir, filter_conditions)
    filter_processor.process(temp_file)
    
    # 2. 分析数据
    analyzer = DataAnalyzer(
        input_file=str(Path(output_dir) / temp_file),
        output_dir=output_dir
    )
    
    # 分析不同字段
    fields_to_analyze = [
        (['response', 'body', 'object'], 'object类型'),
        (['response', 'body', 'id'], 'id分布')
    ]
    
    for field_path, name in fields_to_analyze:
        df = analyzer.analyze_field(field_path, name)
        print(f"\n{name}统计结果:")
        print(df.head().to_string())

if __name__ == '__main__':
    # 配置参数
    config = {
        'input_dir': './input_data',
        'output_dir': './output_data',
        'filter_conditions': {
            'system_label': lambda x: 'system_label' in x,
            'model': lambda x: 'char' in x.get('model', '').lower()
        },
        'fields_to_analyze': [
            (['response', 'body', 'object'], 'object类型'),
            (['response', 'body', 'id'], 'id分布')
        ]
    }

    # 运行分析
    main()