

import os
import sys
import datetime
import time
import logging
import shutil
import json
import traceback
from dataeng_sdk import common_tool
from dataeng_sdk.operator_base import OperatorBase
from baidubce.services.bos.bos_client import BosClient
from baidubce.bce_client_configuration import BceClientConfiguration
from baidubce.auth.bce_credentials import BceCredentials

logger = common_tool.get_logger(__name__)
logging.getLogger("requests").setLevel(logging.WARNING)
logging.getLogger("baidubce").setLevel(logging.WARNING)

# bos config
online_config = {
    "ak": "ALTAK9ncPWSmPBMVFXCQeN9YZB",
    "sk": "6140ac0d451349c28dcb4aff92392343",
    "endpoint": "bj.bcebos.com",
    "bucket": "dataeng-qianfan-data"
}
dev_config = {
    "ak": "ALTAKRLgf9bnOBxitAro3HVv9e",
    "sk": "4a5d87948de34d4a995772026e043da2",
    "endpoint": "bj.bcebos.com",
    "bucket": "dataeng-pdc-test"
}


class Operator(OperatorBase):
    """
       算子: 数据清洗算子
    """

    def _setup(self):
        """
           启动函数
        """

        config = online_config
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path)
        ak = config.get("ak")
        sk = config.get("sk")
        endpoint = config.get("endpoint")
        self._bucket = config.get("bucket")
        self.source_prefix = self.params.get("source_prefix")
        self._date_path = self.params.get("date_path", "")
        # 如果date_path为空，则使用当前日期的前一天作为日期路径
        if self._date_path == "":
            self._date_path = datetime.datetime.strftime(
                datetime.datetime.now() - datetime.timedelta(days=1), "%Y/%m/%d")
            logger.info("date_path is empty, use yesterday's date as path of the day")
        self.source_path = os.path.join(self.source_prefix, self._date_path)
        self._bos_client = BosClient(BceClientConfiguration(credentials=BceCredentials(ak, sk), endpoint=endpoint))
        logger.info("now exec task {}".format(self.source_path))

        self._download_path = os.path.join("./tmp/" + self.source_path)
        os.makedirs(self._download_path) if not os.path.exists(self._download_path) else None
        # 重试参数
        self._retry_interval_time = 10
        self._max_retry_time = 3
        self._delete_temp_file = self.params.get("delete_temp_file", True)

        self._running_mode = self.params.get("check_or_process", "process")

    def _run(self):
        """
           运行
        """
        if self.mock_mode:
            self._setup()
        logger.info("operator start..., source path:{}".format(self.source_path))

        if self._running_mode == "check":
            # 判断文件是否可以下载
            marker = f"{self.source_path}"
            response = self._bos_list_objects(marker)
            # 如果response中有"_SUCCESS"文件，则表示数据存在
            for obj in response:
                logger.info("file: {}".format(obj))
                if obj.split("/")[-1] == "_SUCCESS":
                    return None
            logger.error("data not exist")
            raise ValueError("data not exist")

        elif self._running_mode == "process":
            #文件下载&处理文件
            marker = f"{self.source_path}"
            response = self._bos_list_objects(marker)
            num_partitions = self.params.get("num_partitions")
            partition = int(self.params.get("partition"))
            files_to_process = []
            for key in response:
                #print("========="+ key)
                #continue
                if key.endswith("/"):
                    continue
                elif "_SUCCESS" in key or "success" in key:
                    continue
                elif "mapping.json" in key:
                    mapping_file = self._fetch_bos_object_to_file(key, "")
                    continue
                files_to_process.append(key)

            if os.path.exists(mapping_file):
                mapping_dict = self._read_mapping_file(mapping_file)
            else:
                logger.error("mapping.json not exist")
                raise ValueError("mapping.json not exist")

            # 对文件列表进行排序
            files_to_process.sort()
            files_downloaded = []
            for i, key in enumerate(files_to_process):
                if i % num_partitions != partition:
                    continue
                files_downloaded.append(key)
            
            # 清洗
            for filename in files_downloaded:
                logger.info("downloading file: {}".format(filename))
                response = self._fetch_bos_object(filename)
                qianfan_output = []
                save_path = os.path.join(self.output_local_path, filename.split('/')[-1])
                with open(save_path, "w", encoding='utf-8') as fp:
                    for line in response.data:
                        output_data = {}
                        input_data = json.loads(line)
                        request_data = self._extract_request(input_data)
                        response_data = self._extract_response(input_data)

                        output_data["as-id"] = input_data.get("asid")
                        output_data["model"] = mapping_dict.get(input_data.get("url").split("/")[-1])
                        output_data["create_time"] = input_data.get("timestamp")
                        output_data["temperature"] = request_data.get("temperature")
                        output_data["source"] = request_data.get("source")
                        output_data["event_product"] = request_data.get("event_product")
                        output_data["query"] = request_data.get("message")
                        output_data["answer"] = response_data.get("result")
                        output_data["content"] = response_data.get("content")
                        #qianfan_output.append()
                        fp.write(json.dumps(output_data, ensure_ascii=False) + '\n')
                    # 删除临时文件
                    # os.remove(qianfan_download_path)
                response.data.close()
                    
                logger.info("saving file at: {}".format(save_path))
                        

        return None

    def _teardown(self):
        """
           任务执行完成
        """
        logger.info("task success")
        return None
   
    def _extract_request(self, data):
        """从JSON数据中提取HTTP请求的关键信息"""
        source_mapping = {'200': '千帆'}
        request = data.get('request', {})
        body = request.get('body', {})
        url = data.get('url', 'N/A')
        # 提取请求中的重要字段
        temperature = float(body.get('temperature', 0))
        source = body.get('source', '')

        messages = body.get('messages', [])
        if messages:
            for message in reversed(messages):
                if message.get("role") == "user":
                    user_message = message.get('content', '')
                    break
        else:
            user_message = ""
        
        return {
            'url': url,
            'body': body,
            'message': user_message,
            'content': messages,
            'temperature': temperature,
            'source': source,
            'event_product': source_mapping.get(source, 'N/A'),
        }

    def _extract_response(self, data):
        """从JSON数据中提取HTTP响应的关键信息"""
        response = data.get('response', {})
        body = response.get('body', {})

        # 提取响应中的重要字段
        result = body.get('result', '')
        usage = body.get('usage', {})
        
        return {
            'result': result,
            'usage': usage
        }

    def _read_mapping_file(self, file_path):
        """读取映射文件并解析为Python字典"""
        mapping = {}
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            for result in data["results"]:
                mapping[result["uri"]] = result["baseModelType"]
        return mapping

    def _bos_list_objects(self, prefix):
        """
            bos_list_objects
        """
        is_truncated = True
        max_keys = 1
        marker = None
        while is_truncated:
            response = self._bos_client.list_objects(self._bucket, max_keys=max_keys, marker=marker, prefix=prefix)
            for obj in response.contents:
                yield obj.key
            is_truncated = response.is_truncated
            marker = getattr(response, 'next_marker', None)

    def _exist_bos_object(self, key: str, max_retry_time: int):
        """
           判读BOS文件是否存在
        """
        retry_cnt = 0
        while True:
            try:
                print(self._bucket)
                print(key)
                respose = self._bos_client.get_object_as_string(self._bucket, key)
                logger.info("bos data is ready")
                return True
            except Exception as e:
                print(e)
                retry_cnt += self._retry_interval_time
                if retry_cnt < max_retry_time:
                    logger.warning("bos wait exceed retry limit:{} current_time:{}"
                        .format(self._retry_interval_time * retry_cnt, datetime.datetime.now()))
                    time.sleep(self._retry_interval_time)
                    continue
                logger.error("pull from bos exception has occured current_time:{} info:{}"
                            .format(datetime.datetime.now(), e))
        return False

    def _fetch_bos_object_to_file(self, key: str, fileprefix=""):
        """
           从BOS下载文件, 并记录下载文件信息到set
           Return local file path, icode file path
        """
        retry = 0
        while True:
            try:
                filename = "{}/{}{}".format(self._download_path, fileprefix, key.split('/')[-1])
                logger.info("try fetch {}, {}".format(key, filename))
                self._bos_client.get_object_to_file(self._bucket, key, filename)
                logger.info("fetch bos data success")
                return filename
            except Exception as e:
                retry += 1
                if retry < 3:
                    logger.warning("fetch failed, key:{} ex:{}".format(key, e))
                    continue
                logger.error("fetch faild, ex:{}".format(e))
                raise e
        return ""
    def _fetch_bos_object(self, key: str, fileprefix=""):
        """
           从BOS下载文件, 并记录下载文件信息到set
           Return local file path, icode file path
        """
        retry = 0
        while True:
            try:
                filename = "{}/{}{}".format(self._download_path, fileprefix, key.split('/')[-1])
                logger.info("try fetch {}, {}".format(key, filename))
                self._bos_client.get_object_to_file(self._bucket, key, filename)
                response = self._bos_client.get_object(self._bucket, key)
                return response
            except Exception as e:
                retry += 1
                if retry < 3:
                    logger.warning("fetch failed, key:{} ex:{}".format(key, e))
                    continue
                logger.error("fetch faild, ex:{}".format(e))
                raise e
        return ""

def main(argv):
    """
    主函数，用于解析命令行参数并执行相应操作。
    
    Args:
        argv (list[str]): 包含命令行参数的列表，第一个元素是程序名称。
    
    Returns:
        None: 无返回值，直接在函数内部执行相应操作。
    """
    try:
        alg = Operator(argv)
        alg.execute()
    except Exception as e:
        print("alg operator error  ...,{}".format(e))
        traceback.print_exc()
        sys.exit(-1)
    sys.exit(0)


def test(argv):
    """
    测试函数，用于执行单元测试和集成测试。
    
    Args:
        argv (list[str]): 包含参数的列表，可以是单元测试或集成测试。
    
    Returns:
        None: 无返回值，直接在命令行中打印结果。
    """

    os.environ['AIRFLOW_DATASTORE_URL'] = './temp'
    alg = Operator(argv, True)
    alg.set_mock_params({ 
        "source_prefix": "user-data/reflow",
        "date_path": "20241219",
        "check_or_process": "process",
        "num_partitions": 1,
        "partition": 0
    })
    alg.set_mock_output_path("./output")
    alg.execute()


if __name__ == "__main__":
    test(sys.argv)