
import os
import sys
import datetime
import time
import logging
import json
import traceback
from dataeng_sdk import common_tool
from dataeng_sdk.operator_base import OperatorBase
from baidubce.services.bos.bos_client import BosClient
from baidubce.bce_client_configuration import BceClientConfiguration
from baidubce.auth.bce_credentials import BceCredentials

from multi_process_processor import MultiProcessProcessor

from concurrent.futures import ThreadPoolExecutor, as_completed

logger = common_tool.get_logger(__name__)
logging.getLogger("requests").setLevel(logging.WARNING)
logging.getLogger("baidubce").setLevel(logging.WARNING)

# bos config
online_config = {
    "ak": "ALTAK9ncPWSmPBMVFXCQeN9YZB",
    "sk": "6140ac0d451349c28dcb4aff92392343",
    "endpoint": "bj.bcebos.com",
    "bucket": "dataeng-qianfan-data",
    # "output_path": "/home/<USER>/case-detect-taihu/test/qianfan_log_etl",
    # "output_path": "/home/<USER>/case-detect-taihu/online/event/20241110/qianfan_log",
    "output_path": "./tmp_output"
}


class Operator(OperatorBase):
    """
       算子: 数据清洗算子
    """

    def _setup(self):
        """
           启动函数
        """

        config = online_config
        ak = config.get("ak")
        sk = config.get("sk")
        endpoint = config.get("endpoint")
        self._bucket = config.get("bucket")
        self._output_path = config.get("output_path")
        # 打印服务启动参数
        logger.info("setup executing... 2159")
        # 下面打印一些算子的信息，业务逻辑中可直接使用
        logger.info("self.task_info: %s", self.task_info)  # 算子的基本信息
        logger.info("self.params: %s", self.params)  # 启动时配置的参数
        self.source_prefix = self.params.get(
            "source_prefix", "user-data/reflow")

        self._date_path = self.params.get("date_path")
        # 如果date_path为空，则使用当前日期的前一天作为日期路径
        if len(self._date_path) == 0:
            yesterday = datetime.datetime.strftime(
                datetime.datetime.now() - datetime.timedelta(days=1), "%Y%m%d")
            self._date_path = yesterday
        self.source_path = os.path.join(self.source_prefix, self._date_path)
        self._bos_client = BosClient(
            BceClientConfiguration(
                credentials=BceCredentials(ak, sk),
                endpoint=endpoint
            )
        )
        logger.info("now exec task {}".format(self.source_path))
        # 重试参数
        self._retry_interval_time = 10
        self._max_retry_time = 3
        self._delete_temp_file = self.params.get("delete_temp_file", True)
        self.file_process_parallelism = self.params.get(
            "file_process_parallelism")
        self._running_mode = self.params.get("check_or_process", "process")
        self.mapping_dict = {}

    def _run(self):
        """
           运行
        """
        if self.mock_mode:
            self._setup()
        logger.info(
            "operator start..., source path:{}".format(self.source_path))

        if self._running_mode == "check":
            # 判断文件是否可以下载
            marker = f"{self.source_path}"
            response = self._bos_list_objects(marker)
            # 如果response中有"_SUCCESS"文件，则表示数据存在
            success = False
            files_from_bos = []
            for key in response:
                logger.info("file: {}".format(key))
                if key.endswith("/"):
                    continue
                elif "_SUCCESS" in key or "success" in key:
                    success = True
                elif "mapping.json" in key:
                    continue
                else:
                    files_from_bos.append(key)
            logger.info(f"source from [{marker}] num -> {len(files_from_bos)}")
            logger.info(f"source file paths: ->" +
                        '\n'.join(file for file in files_from_bos))
            if success:
                logger.info("data exists")
                return None
            else:
                logger.error("data not exist")
                raise ValueError("data not exist")

        elif self._running_mode == "process":
            # 文件下载&处理文件
            marker = f"{self.source_path}"
            response = self._bos_list_objects(marker)
            num_partitions = self.params.get("num_partitions", 1)
            partition = int(self.params.get("partition", 0))
            files_from_bos = []
            for key in response:
                # print("========="+ key)
                # continue
                if key.endswith("/"):
                    continue
                elif "_SUCCESS" in key or "success" in key:
                    continue
                elif "mapping.json" in key:
                    self.mapping_dict = self._fetch_and_fill_mapping(
                        key, f"{partition}")
                    continue
                else:
                    files_from_bos.append(key)
            if not self.mapping_dict:
                logger.error("mapping.json not exist")
                raise ValueError("mapping.json not exist")
            # 对文件列表进行排序
            files_from_bos.sort()
            files_to_process = []
            for i, key in enumerate(files_from_bos):
                if i % num_partitions != partition:
                    continue
                files_to_process.append(key)
            # 并行下载+处理+写入文件
            logger.info("file to process -> \n" +
                        '\n'.join(file for file in files_to_process))
            start_time = time.time()
            max_workers = self.file_process_parallelism  # 动态调整线程数
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_file = {
                    executor.submit(
                        self.process_single_file,
                        file_path,
                        self._output_path,
                        self.mapping_dict,
                        self
                    ): file_path
                    for file_path in files_to_process
                }
                # 处理完成的任务
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    try:
                        future.result()
                        print(f"文件 {file_path} 处理完成")
                    except Exception as e:
                        print(f"文件 {file_path} 处理失败: {e}")
            print(f"处理完成，耗时: {time.time() - start_time:.2f}秒")
        return None

    def process_single_file(self, file_path, output_dir, mapping_dict, operator):
        """单个文件处理函数"""
        try:
            file_name = os.path.basename(file_path)
            output_file = os.path.join(output_dir, file_name)
            processor = MultiProcessProcessor(
                operator=operator,
                file_path=file_path,
                output_file=output_file,
                mapping_dict=mapping_dict,
                num_line_processors=4,
                batch_size=2 << 10
            )
            return processor.run()
        except Exception as e:
            print(f"处理文件 {file_path} 失败: {e}")
            raise

    def _teardown(self):
        """
           任务执行完成
        """
        logger.info("task success")
        return None

    def _line_process(self, line: str):
        """单行json核心处理业务"""
        output_data = {}
        input_data = json.loads(line)
        request_data = self._extract_request(input_data)
        response_data = self._extract_response(input_data)
        output_data["as-id"] = input_data.get("asid")
        model_last_id: str = input_data.get("url").split("/")[-1]
        model = self.mapping_dict.get(model_last_id)
        output_data["model"] = model if model else model_last_id.upper()
        output_data["create_time"] = datetime.datetime.fromtimestamp(
            input_data.get("timestamp")).strftime("%Y-%m-%d %H:%M:%S")
        output_data["source"] = request_data.get("source")
        output_data["event_product"] = request_data.get("event_product")
        output_data["query"] = request_data.get("message")
        output_data["answer"] = response_data.get("result")
        return json.dumps(output_data, ensure_ascii=False)

    # _extract_request 和 _extract_response 方法保持不变
    def _extract_request(self, data):
        """从JSON数据中提取HTTP请求的关键信息"""
        source_mapping = {'200': 'qianfan'}
        request = data.get('request', {})
        body = request.get('body', {})
        url = data.get('url', 'N/A')
        temperature = float(body.get('temperature', 0))
        source = body.get('source', '')
        user_message = ""
        messages = body.get('messages', [])
        content = []
        size = len(messages)
        if messages:
            for i in range(size):
                if i == size - 1:
                    user_message = messages[i].get('content', '')
                else:
                    content.append(messages[i].get('content', ''))
        return {
            'url': url,
            'body': body,
            'message': user_message,
            'content': content,
            'temperature': temperature,
            'source': source,
            'event_product': "qianfan",
        }

    def _extract_response(self, data):
        """从JSON数据中提取HTTP响应的关键信息"""
        response = data.get('response', {})
        body = response.get('body', {})
        result = body.get('result', '')
        usage = body.get('usage', {})
        return {
            'result': result,
            'usage': usage
        }

    def _bos_list_objects(self, prefix):
        """
            bos_list_objects
        """
        is_truncated = True
        max_keys = 1
        marker = None
        while is_truncated:
            response = self._bos_client.list_objects(
                self._bucket, max_keys=max_keys, marker=marker, prefix=prefix)
            for obj in response.contents:
                yield obj.key
            is_truncated = response.is_truncated
            marker = getattr(response, 'next_marker', None)

    def _exist_bos_object(self, key: str, max_retry_time: int):
        """
           判读BOS文件是否存在
        """
        retry_cnt = 0
        while True:
            try:
                print(self._bucket)
                print(key)
                respose = self._bos_client.get_object_as_string(
                    self._bucket, key)
                logger.info("bos data is ready")
                return True
            except Exception as e:
                print(e)
                retry_cnt += self._retry_interval_time
                if retry_cnt < max_retry_time:
                    logger.warning("bos wait exceed retry limit:{} current_time:{}"
                                   .format(self._retry_interval_time * retry_cnt, datetime.datetime.now()))
                    time.sleep(self._retry_interval_time)
                    continue
                logger.error("pull from bos exception has occured current_time:{} info:{}"
                             .format(datetime.datetime.now(), e))
        return False

    def _fetch_and_fill_mapping(self, key: str, fileprefix=""):
        """
           从BOS下载文件, 并记录下载文件信息到set
           Return local file path, icode file path
        """
        retry = 0
        download_path = os.path.join(
            self._output_path, "mapping", self._date_path)
        os.makedirs(download_path) if not os.path.exists(
            download_path) else None
        while True:
            try:
                filename = "{}/{}/{}".format(download_path,
                                             fileprefix, key.split('/')[-1])
                if not os.path.exists(filename):
                    os.makedirs(os.path.dirname(filename), exist_ok=True)
                    self._bos_client.get_object_to_file(
                        self._bucket, key, filename)
                logger.info("fetch mapping file {} success".format(key))
                mapping = {}
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for result in data["results"]:
                        mapping[result["uri"]] = result["baseModelType"]
                logger.info("mapping dic lenth : {}".format(len(mapping)))
                return mapping
            except Exception as e:
                retry += 1
                if retry < 3:
                    logger.warning("fetch failed, key:{} ex:{}".format(key, e))
                    continue
                logger.error("fetch faild, ex:{}".format(e))
                raise e

    def _fetch_bos_object(self, key: str, fileprefix=""):
        """
           从BOS下载文件, 并记录下载文件信息到set
           Return local file path, icode file path
        """
        retry = 0
        while True:
            try:
                logger.info("try fetch {}".format(key))
                response = self._bos_client.get_object(self._bucket, key)
                return response
            except Exception as e:
                retry += 1
                if retry < 3:
                    logger.warning("fetch failed, key:{} ex:{}".format(key, e))
                    continue
                logger.error("fetch faild, ex:{}".format(e))
                raise e


def main(argv):
    """
    主函数，用于解析命令行参数并执行相应操作。

    Args:
        argv (list[str]): 包含命令行参数的列表，第一个元素是程序名称。

    Returns:
        None: 无返回值，直接在函数内部执行相应操作。
    """
    try:
        alg = Operator(argv)
        alg.execute()
    except Exception as e:
        print("alg operator error  ...,{}".format(e))
        traceback.print_exc()
        sys.exit(-1)
    sys.exit(0)


def test(argv):
    """
    测试函数，用于执行单元测试和集成测试。

    Args:
        argv (list[str]): 包含参数的列表，可以是单元测试或集成测试。

    Returns:
        None: 无返回值，直接在命令行中打印结果。
    """
    os.environ['AIRFLOW_DATASTORE_URL'] = './tmp_output'
    P = os.getenv('P')
    if P is None:
        P = 0
    print(P)
    alg: Operator = Operator(['self_define_operator.py'], mock_mode=True)
    # alg: Operator = Operator(argv, mock_mode=True)
    # alg.set_mock_input_files(["temp/a.json", "temp/b.json"])
    alg.set_mock_output_path("./tmp_output")
    alg.set_mock_params({
        "source_prefix": "user-data/reflow",
        "date_path": "20241113",
        "check_or_process": "process",
        "num_partitions": 1,
        "file_process_parallelism": 8,
        "partition": 0
    })
    alg.execute()


if __name__ == "__main__":
    test(sys.argv)
    # main(sys.argv)