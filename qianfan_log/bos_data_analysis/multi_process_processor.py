
import io
from multiprocessing import Pool, Manager
import json
from queue import Empty
import shutil
from typing import List
import time


class MultiProcessProcessor:
    """
    多进程处理器类，用于并发处理多个文件，并将处理结果写入输出文件。

    Attributes:
        mapping_dict (dict): 映射字典，用于数据转换。
        output_file (str): 输出文件路径。
        file_paths (List[str]): 待处理的文件路径列表。
        operator (callable): 数据处理操作符, 应提供_line_process方法用于处理单行数据。
        num_workers (int): 工作进程数, 默认为4。
        batch_size (int): 批次大小, 即每个批次包含的行数, 默认为100。
        data_queue (Queue): 数据队列，用于存储待处理的数据批次。
        result_queue (Queue): 结果队列，用于存储已处理的数据批次。
        finished (Value): 共享的完成标志，用于指示处理是否已完成。
        num_downloaders (int): 下载任务线程数, 默认为2。
    """

    def __init__(self, mapping_dict: dict, output_file: str,
                 file_path: str, operator=None, input_file: str = None,
                 num_line_processors: int = 4, batch_size: int = 100):
        """初始化MultiProcessProcessor类的实例"""
        # 使用 Manager 来创建进程间共享的队列
        manager = Manager()
        self.output_file = output_file
        self.fail_file = f'{output_file}.fail'
        self.num_line_processors = num_line_processors
        self.batch_size = batch_size
        self.operator = operator
        self.file_path = file_path
        self.data_queue = manager.Queue(maxsize=2 << 5)  # 数据队列
        self.result_queue = manager.Queue(maxsize=2 << 5)  # 结果队列
        self.finished = manager.Value('b', False)  # 共享的完成标志
        self.mapping_dict = mapping_dict

    def read(self):
        """读取
        """
        try:
            bos_file = self.operator._fetch_bos_object(self.file_path)
            batch = []
            for line in bos_file.data:
                batch.append(line)
                if len(batch) >= self.batch_size:
                    self._put_batch_to_queue(batch)
                    batch = []

            # 处理剩余数据
            if batch:
                self._put_batch_to_queue(batch)
        except Exception as e:
            print(f"下载文件 {self.file_path} 时发生错误: {e}")
            raise
        finally:
            if hasattr(bos_file, 'data'):
                bos_file.data.close()

    def _put_batch_to_queue(self, batch, max_retries=3, timeout=1):
        """将批次数据放入队列"""
        for attempt in range(max_retries):
            try:
                self.data_queue.put(batch, block=True)
                return
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                time.sleep(0.1 * (2 ** attempt))

    def process(self):
        """处理"""
        while not self.is_processing_complete():
            try:
                # 获取一批数据
                batch = self.data_queue.get_nowait()
                # 批量处理数据
                processed_batch = []
                for line in batch:
                    try:
                        processed_data = self.operator._line_process(
                            line.strip())
                        processed_batch.append(processed_data)
                    except Exception as e:
                        print(f"\n处理数据出错: {e}")
                        with open(self.fail_file, 'a', encoding='utf-8') as failFile:
                            failFile.write(line + "\n")
                # 批量放入结果队列
                if processed_batch:
                    while True:
                        try:
                            self.result_queue.put(processed_batch, block=True)
                            break
                        except:
                            time.sleep(0.01)
            except Empty:
                time.sleep(0.01)
            except Exception as e:
                print(f"\n处理批次出错: {e}")
                time.sleep(0.01)
        print("\nprocess done")

    def write(self):
        """
        批量写入结果到文件
        """
        WRITE_BATCH_SIZE = self.batch_size * 2
        MAX_WRITE_INTERVAL = 1  # 最大写入间隔(秒)
        MAX_RETRIES = 3  # 最大重试次数
        buf_size = 128 * 1024

        def should_flush(buffer, last_write):
            """判断是否需要刷新缓冲区"""
            if not buffer:
                return False
            return (len(buffer) >= WRITE_BATCH_SIZE or
                    time.time() - last_write > MAX_WRITE_INTERVAL)

        def flush_buffer(buffer, file_handle):
            """将缓冲区数据写入文件"""
            if not buffer:
                return
            try:
                source = io.BytesIO()
                source.write(('\n'.join(buffer) + '\n').encode('utf-8'))
                # 写入文件
                source.seek(0)
                shutil.copyfileobj(source, file_handle, buf_size)
                buffer.clear()
            except Exception as e:
                print(f"写入文件失败: {e}")
                raise

        def process_queue(buffer, last_write, file_handle):
            """处理队列中的数据"""
            try:
                batch = self.result_queue.get_nowait()
                buffer.extend(batch)

                if should_flush(buffer, last_write):
                    flush_buffer(buffer, file_handle)
                    return time.time()
                return last_write

            except Empty:
                if should_flush(buffer, last_write):
                    flush_buffer(buffer, file_handle)
                    return time.time()
                time.sleep(0.01)
                return last_write
            except Exception as e:
                print(f"处理队列数据失败: {e}")
                raise

        # 主处理逻辑
        buffer = []
        last_write = time.time()
        retry_count = 0
        try:
            # 主循环处理数据
            with open(self.output_file, 'wb', buffering=buf_size) as f:
                while not self.is_processing_complete() or buffer:
                    try:
                        last_write = process_queue(buffer, last_write, f)
                        retry_count = 0  # 成功处理后重置重试计数
                    except Exception as e:
                        retry_count += 1
                        if retry_count >= MAX_RETRIES:
                            print(f"达到最大重试次数，退出处理: {e}")
                        print(f"处理失败，进行第{retry_count}次重试: {e}")
                        time.sleep(0.1 * retry_count)  # 指数退避
            # 确保最后的数据被写入
            if buffer:
                flush_buffer(buffer, f)

        except Exception as e:
            print(f"写入结果过程中发生错误: {e}")
            raise
        else:
            print("\nwrite done")

    def monitor(self):
        """监控队列/下载状态"""
        while not self.is_processing_complete():
            current_data_size = self.data_queue.qsize()
            current_result_size = self.result_queue.qsize()
            status_summary = (
                f"\r数据队列: {current_data_size}, "
                f"结果队列: {current_result_size}, "
            )

            print(status_summary, end='\n')
            time.sleep(1)
        print("\nmonitor done")

    def is_processing_complete(self):
        """
        检查处理是否完成

        Returns:
            bool: 如果所有处理都已完成则返回True, 否则返回False
        """
        MAX_CHECK_ATTEMPTS = 4  # 最大检查次数
        BASE_WAIT_TIME = 0.5    # 基础等待时间

        def check_completeed():
            """检查队列状态"""
            return (self.finished.value and
                    self.result_queue.empty() and
                    self.data_queue.empty())
        # 初始检查
        if not check_completeed():
            return False

        # 多次确认以防止假完成
        for attempt in range(MAX_CHECK_ATTEMPTS):
            if not check_completeed():
                return False
            # 使用渐进式等待时间
            wait_time = BASE_WAIT_TIME * (attempt + 1)
            time.sleep(wait_time)

        # 最终确认
        return check_completeed()

    def run(self):
        """启动处理流程"""
        processes = []
        print("start processing...", self.file_path)
        with Pool(processes=self.num_line_processors + 3) as pool:
            pool.apply_async(self.read)
            pool.apply_async(self.write)
            pool.apply_async(self.monitor)
            for _ in range(self.num_line_processors):
                pool.apply_async(self.process)
            pool.close()
            pool.join()
            print("all finished...", self.file_path)