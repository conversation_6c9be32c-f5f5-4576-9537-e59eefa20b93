# 列出目录内容
hadoop dfs -ls /path/to/dir          # 列出目录
hadoop dfs -ls -R /path/to/dir       # 递归列出目录及子目录

# 删除文件/目录
hadoop dfs -rm /path/to/file         # 删除文件
hadoop dfs -rm -r /path/to/dir       # 递归删除目录
hadoop dfs -rm -skipTrash /path/*    # 删除并跳过回收站

# 复制文件
hadoop dfs -cp /source/path /dest/path           # hadoop内部复制
hadoop dfs -copyFromLocal local/path hadoop/path   # 从本地复制到hadoop
hadoop dfs -copyToLocal hadoop/path local/path     # 从hadoop复制到本地
hadoop dfs -get hadoop/path local/path            # 等同于copyToLocal
hadoop dfs -put local/path hadoop/path            # 等同于copyFromLocal

# 其他常用命令
hadoop dfs -mkdir /path/to/dir       # 创建目录
hadoop dfs -cat /path/to/file        # 查看文件内容
hadoop dfs -tail /path/to/file       # 查看文件末尾
hadoop dfs -du -h /path/to/dir       # 查看目录大小

hadoop dfs -rmr /user/udw-idsg-exchange/dataflow/temp_qianfan_log_classify/20241223
