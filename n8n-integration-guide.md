# n8n与MCP集成实施指南

## 快速开始

### 1. 安装n8n

```bash
# 方式1: 使用Docker (推荐)
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -v ~/.n8n:/home/<USER>/.n8n \
  n8nio/n8n

# 方式2: 使用npm
npm install n8n -g
n8n start
```

### 2. 创建MCP连接器节点

基于您现有的mcp-bench项目，我们需要创建自定义n8n节点来连接MCP服务器。

#### 2.1 MCP客户端节点结构

```typescript
// nodes/McpClient/McpClient.node.ts
import {
    IExecuteFunctions,
    INodeExecutionData,
    INodeType,
    INodeTypeDescription,
    NodeOperationError,
} from 'n8n-workflow';

export class McpClient implements INodeType {
    description: INodeTypeDescription = {
        displayName: 'MCP Client',
        name: 'mcpClient',
        group: ['transform'],
        version: 1,
        description: 'Connect to MCP servers and execute tools',
        defaults: {
            name: 'MCP Client',
        },
        inputs: ['main'],
        outputs: ['main'],
        properties: [
            {
                displayName: 'Server Name',
                name: 'serverName',
                type: 'string',
                default: 'filesystem',
                description: 'Name of the MCP server to connect to',
            },
            {
                displayName: 'Tool Name',
                name: 'toolName',
                type: 'string',
                default: 'read_file',
                description: 'Name of the tool to execute',
            },
            {
                displayName: 'Parameters',
                name: 'parameters',
                type: 'json',
                default: '{}',
                description: 'Parameters to pass to the tool',
            },
        ],
    };

    async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
        const items = this.getInputData();
        const returnData: INodeExecutionData[] = [];

        for (let i = 0; i < items.length; i++) {
            try {
                const serverName = this.getNodeParameter('serverName', i) as string;
                const toolName = this.getNodeParameter('toolName', i) as string;
                const parameters = this.getNodeParameter('parameters', i) as object;

                // 这里集成您的MCP客户端逻辑
                const result = await this.executeMcpTool(serverName, toolName, parameters);

                returnData.push({
                    json: {
                        serverName,
                        toolName,
                        parameters,
                        result,
                        timestamp: new Date().toISOString(),
                    },
                });
            } catch (error) {
                if (this.continueOnFail()) {
                    returnData.push({
                        json: {
                            error: error.message,
                        },
                    });
                    continue;
                }
                throw error;
            }
        }

        return [returnData];
    }

    private async executeMcpTool(serverName: string, toolName: string, parameters: object) {
        // 集成您现有的MCP客户端代码
        // 参考 mcp-bench/src/engines/evaluation_engine.py 中的逻辑
        
        // 示例实现
        const mcpConfig = await this.loadMcpConfig();
        const client = await this.createMcpClient(serverName, mcpConfig);
        
        return await client.callTool(toolName, parameters);
    }
}
```

### 3. 数据合成工作流模板

#### 3.1 基础数据合成流程

```json
{
  "name": "Data Synthesis Workflow",
  "nodes": [
    {
      "parameters": {
        "path": "/data/seed_data.json"
      },
      "name": "Load Seed Data",
      "type": "n8n-nodes-base.readFile",
      "position": [250, 300]
    },
    {
      "parameters": {
        "serverName": "filesystem",
        "toolName": "read_file",
        "parameters": "={{ JSON.stringify({path: $json.filePath}) }}"
      },
      "name": "MCP File Reader",
      "type": "mcpClient",
      "position": [450, 300]
    },
    {
      "parameters": {
        "model": "claude-3-5-sonnet",
        "prompt": "Based on this seed data: {{ $json.content }}, generate 5 similar training examples with variations.",
        "temperature": 0.7
      },
      "name": "LLM Data Generation",
      "type": "n8n-nodes-base.openAi",
      "position": [650, 300]
    },
    {
      "parameters": {
        "functionCode": "// 数据质量检查和过滤\nconst generatedData = $input.first().json.choices[0].message.content;\nconst parsed = JSON.parse(generatedData);\n\n// 质量检查逻辑\nconst qualityScore = checkDataQuality(parsed);\nif (qualityScore > 0.8) {\n  return [{ json: { data: parsed, quality: qualityScore, status: 'approved' } }];\n} else {\n  return [{ json: { data: parsed, quality: qualityScore, status: 'rejected' } }];\n}\n\nfunction checkDataQuality(data) {\n  // 实现质量检查逻辑\n  let score = 1.0;\n  \n  // 检查数据完整性\n  if (!data.instruction || !data.response) {\n    score -= 0.3;\n  }\n  \n  // 检查长度合理性\n  if (data.instruction.length < 10 || data.response.length < 20) {\n    score -= 0.2;\n  }\n  \n  // 检查重复性\n  // ... 更多检查逻辑\n  \n  return Math.max(0, score);\n}"
      },
      "name": "Quality Control",
      "type": "n8n-nodes-base.function",
      "position": [850, 300]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{ $json.status }}",
              "operation": "equal",
              "value2": "approved"
            }
          ]
        }
      },
      "name": "Quality Filter",
      "type": "n8n-nodes-base.if",
      "position": [1050, 300]
    },
    {
      "parameters": {
        "serverName": "filesystem",
        "toolName": "write_file",
        "parameters": "={{ JSON.stringify({path: '/output/generated_data_' + Date.now() + '.json', content: JSON.stringify($json.data)}) }}"
      },
      "name": "Save Approved Data",
      "type": "mcpClient",
      "position": [1250, 250]
    }
  ],
  "connections": {
    "Load Seed Data": {
      "main": [
        [
          {
            "node": "MCP File Reader",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "MCP File Reader": {
      "main": [
        [
          {
            "node": "LLM Data Generation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "LLM Data Generation": {
      "main": [
        [
          {
            "node": "Quality Control",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Quality Control": {
      "main": [
        [
          {
            "node": "Quality Filter",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Quality Filter": {
      "main": [
        [
          {
            "node": "Save Approved Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
```

### 4. Benchmark评估工作流

#### 4.1 评估任务编排

```json
{
  "name": "Benchmark Evaluation Workflow",
  "nodes": [
    {
      "parameters": {
        "serverName": "filesystem",
        "toolName": "read_file",
        "parameters": "={{ JSON.stringify({path: '/config/evaluation_config.json'}) }}"
      },
      "name": "Load Evaluation Config",
      "type": "mcpClient",
      "position": [250, 300]
    },
    {
      "parameters": {
        "functionCode": "// 解析评估配置并创建任务列表\nconst config = JSON.parse($json.result.content);\nconst tasks = [];\n\nfor (const task of config.tasks) {\n  for (const model of config.models) {\n    tasks.push({\n      taskId: `${task.task_id}_${model.name}`,\n      task: task,\n      model: model,\n      config: config\n    });\n  }\n}\n\nreturn tasks.map(task => ({ json: task }));"
      },
      "name": "Create Task Matrix",
      "type": "n8n-nodes-base.function",
      "position": [450, 300]
    },
    {
      "parameters": {
        "functionCode": "// 执行单个评估任务\nconst { task, model, config } = $json;\n\n// 调用您现有的评估引擎\nconst evaluationResult = await executeEvaluation(task, model, config);\n\nreturn [{ json: {\n  taskId: $json.taskId,\n  task: task,\n  model: model,\n  result: evaluationResult,\n  timestamp: new Date().toISOString()\n}}];\n\nasync function executeEvaluation(task, model, config) {\n  // 这里集成您的 evaluation_engine.py 逻辑\n  // 可以通过HTTP API或直接调用Python脚本\n  \n  const response = await fetch('http://localhost:8000/evaluate', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({ task, model, config })\n  });\n  \n  return await response.json();\n}"
      },
      "name": "Execute Evaluation",
      "type": "n8n-nodes-base.function",
      "position": [650, 300]
    },
    {
      "parameters": {
        "functionCode": "// 聚合所有评估结果\nconst allResults = $input.all();\nconst summary = {\n  totalTasks: allResults.length,\n  successfulTasks: allResults.filter(r => r.json.result.success).length,\n  averageScore: allResults.reduce((sum, r) => sum + r.json.result.score, 0) / allResults.length,\n  results: allResults.map(r => r.json),\n  timestamp: new Date().toISOString()\n};\n\nreturn [{ json: summary }];"
      },
      "name": "Aggregate Results",
      "type": "n8n-nodes-base.function",
      "position": [850, 300]
    },
    {
      "parameters": {
        "serverName": "filesystem",
        "toolName": "write_file",
        "parameters": "={{ JSON.stringify({path: '/results/evaluation_report_' + Date.now() + '.json', content: JSON.stringify($json, null, 2)}) }}"
      },
      "name": "Save Report",
      "type": "mcpClient",
      "position": [1050, 300]
    }
  ]
}
```

### 5. 集成现有MCP基础设施

#### 5.1 适配器实现

```python
# n8n_mcp_adapter.py
import asyncio
import json
from typing import Dict, Any
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

class N8nMcpAdapter:
    """n8n与MCP服务器的适配器"""
    
    def __init__(self, config_path: str = "mcpservers_all.json"):
        self.config_path = config_path
        self.servers = {}
        self.sessions = {}
    
    async def load_config(self) -> Dict[str, Any]:
        """加载MCP服务器配置"""
        with open(self.config_path, 'r') as f:
            return json.load(f)
    
    async def connect_server(self, server_name: str) -> bool:
        """连接到指定的MCP服务器"""
        try:
            config = await self.load_config()
            server_config = config['mcpServers'][server_name]
            
            # 创建服务器参数
            server_params = StdioServerParameters(
                command=server_config['command'],
                args=server_config.get('args', []),
                env=server_config.get('env', {})
            )
            
            # 建立连接
            stdio_transport = await stdio_client(server_params)
            session = ClientSession(stdio_transport[0], stdio_transport[1])
            
            await session.initialize()
            
            self.sessions[server_name] = session
            return True
            
        except Exception as e:
            print(f"连接服务器 {server_name} 失败: {e}")
            return False
    
    async def call_tool(self, server_name: str, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            if server_name not in self.sessions:
                if not await self.connect_server(server_name):
                    raise Exception(f"无法连接到服务器 {server_name}")
            
            session = self.sessions[server_name]
            result = await session.call_tool(tool_name, parameters)
            
            return {
                "success": True,
                "result": result,
                "server": server_name,
                "tool": tool_name
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "server": server_name,
                "tool": tool_name
            }
    
    async def cleanup(self):
        """清理连接"""
        for session in self.sessions.values():
            await session.close()
        self.sessions.clear()

# Flask API服务器，供n8n调用
from flask import Flask, request, jsonify

app = Flask(__name__)
adapter = N8nMcpAdapter()

@app.route('/mcp/call', methods=['POST'])
async def call_mcp_tool():
    """n8n调用MCP工具的API端点"""
    data = request.json
    
    result = await adapter.call_tool(
        data['server_name'],
        data['tool_name'],
        data['parameters']
    )
    
    return jsonify(result)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8001)
```

### 6. 下一步实施建议

1. **立即开始**：
   - 安装n8n并熟悉界面
   - 创建第一个简单的MCP连接测试

2. **第一周目标**：
   - 实现基础的MCP适配器
   - 创建第一个数据合成工作流原型

3. **第一个月目标**：
   - 完成完整的数据合成和评估工作流
   - 集成现有的mcp-bench项目

您希望我先帮您实现哪个部分？我可以提供更详细的代码实现和配置指导。
