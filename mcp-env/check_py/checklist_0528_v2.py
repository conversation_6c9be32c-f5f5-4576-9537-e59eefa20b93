import json
import sys
# sys.path.append("../../")
from utils import request_llm_with_retry
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import pandas as pd
import re
import os
from bs4 import BeautifulSoup

# import io
# from contextlib import redirect_stdout



inputfile_content_template = """
****文件名****
<<<文件名>>>
****文件名****

****文件内容****
<<<文件内容>>>
****文件内容****

"""

llm_template = """
****检查需求****
<<<检查需求>>>
****检查需求****

****待检查文件内容****
<<<待检查文件内容>>>
****待检查文件内容****

"""
llm_judge_template = """你是一个资深的Python工程师，你精通各种常见的文件处理，你在工作中非常擅长以细心、负责的态度去处理各种繁琐的问题。接下来我会有一些文件检查内容，你需要适当结合文件的特点（从后缀名可以大致看出来）、根据检查要求，逐项判断文件内容是否符合要求。
我可能会给你多个任务，多个任务之间会有编号区分，你逐项认真判断并严格按顺序给出检查结论即可。
待检查内容是一个会操作文件系统的大语言模型生产的结果，我会把原始文件内容以及大模型生成的结果都发给你，你按检查项的要求，逐项按顺序检查大模型的结果是否符合要求。

**********原始文件内容**********
<<<原始文件内容>>>
**********原始文件内容**********

**********待检查内容**********
<<<待检查的内容>>>
**********待检查内容**********


阅读完以上待检查内容后，你严格按给定的顺序依次给出各检查内容是否合格的结论，以html表格的形式给出：
表格要包含一个表头和两行数据。表头内容依次是各检查项，顺序严格按我给你的顺序排列；第一行数据是对该检查项的分析过程，要认真、严格判断文件内容是否符合要求。如果你认为某个文件内容不合格，你需要有理有据，以严肃的态度给出检查不合格的理由；文件后缀名仅供你参考，因此不必纠结后缀名写法是否准确。
第二行数据则是对该检查项的检查结果，能填写的字仅限于以下两种之一，禁止填写其他任何内容：1）检查合格；2）检查不合格。
html表格格式具体如下，具体你生成时待检查内容中每一个待检查项对应表格的一列。

<table>
    <thead>
        <tr>
            <th>第1个待检查项</th>
            <th>第2个待检查项</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>第1个待检查项分析过程</td>
            <td>第2个待检查项分析过程</td>
        </tr>
    </tbody>
    <tbody>
        <tr>
            <td>第1个待检查项分析结论（只能填：【检查合格】或【检查不合格】）</td>
            <td>第2个待检查项分析结论（只能填：【检查合格】或【检查不合格】）</td>
        </tr>
    </tbody>
</table>

"""


def checklist_v21(sample):
    sample["check_version"] = "0528"
    # updated_check_list = eval(sample["extra_info"]["updated_check_list"])
    try:
        updated_check_list = json.loads(sample["extra_info"]["updated_check_list"])
    except:
        try:
            updated_check_list = eval(sample["extra_info"]["updated_check_list"])
        except:
            sample["评估失败原因"] = "Checklist格式无法读取"            
            return sample
    # llm_rules = []
    prompt_all = ""
    idx = 1
    check_result_all = {}
    
    
    prompt_check_file_list = []
    prompt_check_idx_list = []
    
    
    for i in range(len(updated_check_list)):
        check = updated_check_list[i]
        
        target = check["target"]
        if target.startswith("/"):
            target = "." + target
        # if target.startswith("file_system_mock_data") or target.startswith("./file_system_mock_data"):
        #     target = "./mock_file_system_data/" + target
            
        if check["type"] == "check_directory_exist":
            res = os.path.exists(target) and os.path.isdir(target)
            if not res:
                check_result_all[f"检查项{i + 1}"] = {"检查结论": "不合格", "原因": "缺失目录", "详情": f"缺失 {target}文件夹"}
            else:
                check_result_all[f"检查项{i + 1}"] = {"检查结论": "合格", "原因": "存在目录", "详情": f"存在 {target}文件夹"}
                # sample["总判断结论"] = "Failure"
                # sample["检查详细情况"] = f"缺失 {target}文件夹"
                # sample["错误归因"] = f"缺失文件夹"
                # return sample
        elif check["type"] == "check_file_exist":
            res = os.path.exists(target) and os.path.isfile(target)
            if not res:
                check_result_all[f"检查项{i + 1}"] = {"检查结论": "不合格", "原因": "缺失文件", "详情": f"缺失 {target}文件"}
            else:
                check_result_all[f"检查项{i + 1}"] = {"检查结论": "合格", "原因": "存在文件", "详情": f"存在 {target}文件"}
                # sample["总判断结论"] = "Failure"
                # sample["错误归因"] = f"缺失文件"
                # sample["检查详细情况"] = f"缺失 {target}文件"
                # return sample
        elif check["type"] == "check_file_not_exist":
            res = not (os.path.exists(target) and os.path.isfile(target))
            if not res:
                check_result_all[f"检查项{i + 1}"] = {"检查结论": "不合格", "原因": "存在多余文件", "详情": f"存在 {target}文件"}
            else:
                check_result_all[f"检查项{i + 1}"] = {"检查结论": "合格", "原因": "不存在文件", "详情": f"不存在 {target}文件"}
                # sample["总判断结论"] = "Failure"
                # sample["错误归因"] = f"存在多余文件"
                # sample["检查详细情况"] = f"存在 {target}文件"
                # return sample
        elif check["type"] == "check_response":
            check_result_all[f"检查项{i + 1}"] = {"检查结论": "跳过", "原因": "", "详情": "暂时跳过check_response项"}
            
        elif check["type"] == "check_file_content":
            res = os.path.exists(target) and os.path.isfile(target)
            if not res:
                
                check_result_all[f"检查项{i + 1}"] = {"检查结论": "不合格", "原因": "需要检查文件内容，但缺失具体文件", "详情": f"缺失 {target}文件"}
                continue
                # sample["总判断结论"] = "Failure"
                # sample["错误归因"] = f"需要检查文件内容，但缺失具体文件"
                # sample["检查详细情况"] = f"缺失 {target}文件"
                # return sample
            
            # if target not in prompt_check_file_list:
            #     prompt_check_file_list.append(target)
            
            with open(target, 'r') as fr:
                file_content = fr.read()
            rule =  check["value"]

            current_prompt = llm_template.replace("<<<检查需求>>>", rule).replace("<<<待检查文件内容>>>", file_content)
            prompt_all += f"第{idx}个待检查项：\n文件名：{os.path.basename(target)}\n{current_prompt}"
            idx += 1
            prompt_check_idx_list.append(i + 1)
            
        elif check["type"] == "check_tool_call_list":
            check_result_all[f"检查项{i + 1}"] = {"检查结论": "跳过", "原因": "", "详情": "暂时跳过check_tool_call_list项"}
        
    # if not prompt_all:
    #     sample["总判断结论"] = "Success"
    #     sample["错误归因"] = ""
    #     sample["检查详细情况"] = "未发现错误"
    #     return sample
    if prompt_all:
        
        inputfile_contents = "输入原始文件名（相对路径）以及文件完整内容，依次如下：\n"
        # input_file = sample["input_file"]
        # try:
        #     input_file_list = eval(input_file)
        #     for iptf in input_file_list:
        #         for k, v in iptf.items():
        #             current_content = inputfile_content_template.replace("<<<文件名>>>", k).replace("<<<文件内容>>>", v)
        #             inputfile_contents += current_content            
        # except:
        #     input_file_list = json.loads(input_file)
            
        #     for input_f in input_file:
        #         path = input_f["path"]
        #         content = input_f["content"]
        #         if content != None:
        #             current_content = inputfile_content_template.replace("<<<文件名>>>", path).replace("<<<文件内容>>>", content)
        #             inputfile_contents += current_content
        environment_dependency = sample["extra_info"]["environment_dependency"]
        environment_dependency = json.loads(environment_dependency)
        for ed in environment_dependency:
            if ed['type'] == 'file':
                fp = ed['path']
                fcontent = ed['content']
                if fcontent:
                    current_content = inputfile_content_template.replace("<<<文件名>>>", fp).replace("<<<文件内容>>>", fcontent)
                    inputfile_contents += current_content
                
        output_text = llm_judge_template.replace("<<<待检查的内容>>>", prompt_all).replace("<<<原始文件内容>>>", inputfile_contents)
        
        
        for try_time in range(5):
            try:
                res_str, res_content = request_llm_with_retry([{"role": "user", "utterance": output_text}], verify_model_name)
                eval_result = res_content[1]["utterance"]
            
                sample["eval_prompt"] = output_text
                sample["eval_prompt_result"] = eval_result
                sample["prompt_check_idx_list"] = prompt_check_idx_list
                
                eval_result_pos = eval_result.find("<table>")
                eval_result = eval_result[eval_result_pos:]
                
                soup = BeautifulSoup(eval_result, 'html.parser')
                all_rows = [tr for tbody in soup.find_all('tbody') for tr in tbody.find_all('tr')]
                analysis_row = all_rows[0]
                res_row = all_rows[-1]
                analysis = [td.get_text(strip=True) for td in analysis_row.find_all('td')]
                cells = [td.get_text(strip=True) for td in res_row.find_all('td')]
                assert len(analysis) == len(cells) == len(prompt_check_idx_list)
                
                
                for l in range(len(cells)):
                    check_idx = prompt_check_idx_list[l]
                    result = cells[l]
                    conclusion = "不合格" if "不合格" in result else "合格"
                    current_analysis = analysis[l]
                    check_result_all[f"检查项{check_idx}"] = {"检查结论": conclusion, "原因": "LLM检验", "详情": current_analysis}
                    
                
                
                break
            except:
                sample["评估失败原因"] = "基于LLM的结果解析出错"
                continue
        

        
    sample["check_detail_result"] = check_result_all
    
    final_result = "Success"
    for res in check_result_all:
        if check_result_all[res]["检查结论"] == "不合格":
            final_result = "Failure"
            break
    if "评估失败原因" in sample:
        final_result = "unsure"
    sample["总判断结论"] = final_result
    
        
    
    # result = eval_result.split("**你的分析结论**")[-1].strip().split("\n")[-1]
    # if "检查不合格" in result:
    #     sample["总判断结论"] = "Failure"
    #     sample["错误归因"] = result
    #     sample["检查详细情况"] = result
    # else:
    #     sample["总判断结论"] = "Success"
    #     sample["错误归因"] = ""
    #     sample["检查详细情况"] = "未发现错误"
    
    
    return sample



def process_checklist(line):
    sample = json.loads(line)

    updated_check_list = sample["extra_info"]["updated_check_list"]

    sample = checklist_v21(sample)
        
    
    with open(outputfilename, 'a') as fa:
        fa.write(json.dumps(sample, ensure_ascii=False) + "\n")
    return sample
    

if __name__ == "__main__":


    verify_model_name = "EB_SPV4_32K_DNI_eb45t_dpo_0411v2_dpo0412v1p0416v1_ema"

    max_threads = 25


    inputfilename = "./file_system_100_20250528_checked_anquan_deepseek-r1-0528_results.jsonl"
    outputfilename = inpufilename[:-5] + ".eval_resul.jsonl"
    output_excelfilename = inpufilename[:-5] + ".eval_resul.xlsx"

    # inputfilename = "../wencan_0520_labeled.jsonl"
    # outputfilename = "wencan_0520_labled_res.v8.jsonl"
    # output_excelfilename = "wencan_0520_labled_res.v8.xlsx"    

    with open(inputfilename, 'r') as fr:
        lines = [line.strip() for line in fr if line.strip()]
        
        
    # for line in lines:
    #     result = process_checklist(line)
    
    with ThreadPoolExecutor(max_workers=min(max_threads, len(lines))) as executor:
        progress_bar = tqdm(total=len(lines), desc="checklist")
        def update_progress_bar(result):
            progress_bar.update()
            return result
        # results = list(executor.map(process_checklist, lines))
        results = executor.map(lambda x: update_progress_bar(process_checklist(x)), lines)
    progress_bar.close()
    
    df = pd.DataFrame(results)
    df.to_excel(output_excelfilename, index=False)
    
