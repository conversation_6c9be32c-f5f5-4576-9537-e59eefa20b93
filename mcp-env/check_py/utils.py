import json, re
import requests
import time
import random
import openai
import os






def request_eb_ic(context, model_id):

    userids = [

    ]
    userId = random.choice(userids)
    assert len(context) % 2 == 1
    query = context[-1]["utterance"]
    history = []
    if len(context) > 1:
        context = context[:-1]
        for i in range(0, len(context), 2):
            assert context[i]["role"] == "user"
            assert context[i+1]["role"] == "bot"
            history.append([context[i]["utterance"], context[i+1]["utterance"]])
        history = list(reversed(history))


    base_url = "http://10.11.204.53:8005/debug/eb/chat/new"
    random_str = str(int(time.time() * 1000)) + "".join(random.sample('zyxwvutsrqponmlkjihgfedcba',5))
    # if len(history) == 0 and "<mask:" in query:
    if len(history) == 0 and ("<mask:" in query or "<|prefixoftext|>" in query or "<|middleoftext|>" in query):
    # if len(history) == 0 and ("<mask:2>" in query or "<|prefixoftext|>" in query):
    #     query = query.replace("<mask:2>", "<|prefixoftext|>")
    #     query = query.replace("<mask:3>", "<|middleoftext|>")

        data = {
            "text_after_process": query,
            "model_id": model_id,
            "session_id": random_str,
            "userId": userId,
            "temperature": 0.6,
            "topp": 0.95,
            "penalty_score": 1.0,
        }
    else:
        data = {
            "text": query,
            "eb_version":"main","eda_version":"main","session_id": random_str,
            "model_id": model_id,
            "temperature": 0.6,
            "topp": 0.95,
            "penalty_score": 1.0,
            "aorura_close":True,
            "kg_close":True,
            "compute_close":True,
            "safe_close": True,
            "vilg_close":True,
            "prompt_close":True,
            "memory_close":True,
            "code_close":True,
            "cq_close":True,
            "memory_close": True,
            "eda_close": True,
            "intervene_close":True,
            "history": history,
            # "frequency_score": 0.1,
            "eb_model":{
                "version":"main",
                "router":{
                    "code_model_id": model_id,
                    "compute_model_id": model_id,
                    "response_model_id": model_id,
                    "responsereasoning_model_id": model_id
                }
            },
            "userId": userId  # lizhongli01 's user id, please change it to your own user id
        }
    r = requests.post(base_url, data=json.dumps(data), headers={"Content-Type": "application/json"})
    # print(r.json())
    result = r.json()["data"]["result"]
    return result


def request_llm(context, tag, G4_API_TOKEN=None):
    assert context[-1]["role"] == "user"
    rsp_text = None
    if rsp_text is None:

        if tag.startswith("paddle5_eb") or tag.startswith("EB"):
            rsp_text = request_eb_ic(context, tag)
        else:
            model = tag.split(":")[0]
            url = ":".join(tag.split(":")[1:])
            if model == "etp":
                rsp_text = request_etp_pdc(context, url)
            else:
                rsp_text = request_eb4_pdc(context, url)
        # with open(f"G4_buffer/{key}.json", "w", encoding="utf-8") as f:
        #     json.dump({"tag": tag, "context": context, "response": rsp_text}, f, ensure_ascii=False)
    context.append({"role": "bot", "utterance": rsp_text})
    return rsp_text, context


def request_llm_with_retry(context, tag, G4_API_TOKEN=None):
    max_retry = 5
    for i in range(max_retry):
        # print("try {}, {}, {}".format(i, tag, G4_API_TOKEN))
        try:
            rsp_text, context = request_llm(context, tag, G4_API_TOKEN)
            return rsp_text, context
        except:
            print("request_llm failed, retrying...")
            time.sleep(60)
    context.append({"role": "bot", "utterance": "请求失败，请稍后重试"})
    return None, context