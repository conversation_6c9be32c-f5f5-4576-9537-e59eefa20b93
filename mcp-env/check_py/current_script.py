import json
import os
import argparse
from typing import Any, Dict
from fastmcp import FastMCP

# 全局变量存储数据目录路径
DATA_FOLDER_PATH = None
# 全局变量存储加载的数据
data = None

mcp = FastMCP(name="My First MCP Server")

def load_data(folder_path: str) -> dict[str, Any]:
    """加载mock数据"""
    try:
        with open(os.path.join(folder_path, "mock_data.json"), "r", encoding="utf-8") as f:
            mock_data = json.load(f)
    except FileNotFoundError as e:
        raise FileNotFoundError(f"数据文件缺失: {e.filename}")
    
    return mock_data

@mcp.tool()
def find_user_id_by_email(email: str) -> str:
    """通过邮箱查找用户ID，未找到时返回错误信息"""
    if not data:
        return "Error: 数据未加载"
    
    user_email = data.get("email", "")
    normalized_email = email.strip().lower()
    normalized_user_email = user_email.strip().lower()
    
    if normalized_user_email == normalized_email:
        # 由于只有一个用户，可以返回一个标识符或者邮箱本身
        return user_email
    
    return "Error: user not found"

@mcp.tool()
def find_product_name(product_id: str) -> str:
    """通过产品ID查找产品名称，未找到时返回错误信息"""
    # 在mock数据中没有产品信息，但保留函数结构
    # 可以根据订单ID返回相关信息
    if not data:
        return "Error: 数据未加载"
    
    orders = data.get("orders", [])
    if product_id in orders:
        return f"订单: {product_id}"
    
    return "Error: product not found"

@mcp.tool()
def calculate_total_spending(user_id: str) -> float:
    """计算用户总消费金额，用户不存在时返回0.0"""
    if not data:
        return 0.0
    
    # 检查用户是否存在（通过邮箱匹配）
    user_email = data.get("email", "")
    if user_id != user_email and find_user_id_by_email(user_id) == "Error: user not found":
        return 0.0
    
    # 在mock数据中没有价格信息，返回订单数量作为示例
    # 实际应用中这里需要根据具体业务逻辑计算
    orders = data.get("orders", [])
    return float(len(orders) * 100)  # 假设每个订单100元


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="MCP服务器 - 处理用户mock数据")
    parser.add_argument(
        "data_path",
        nargs="?",
        default="./",
        help="数据文件目录路径 (默认: ./)"
    )
    return parser.parse_args()

def main():
    """主函数"""
    global DATA_FOLDER_PATH, data
    
    # 解析命令行参数
    args = parse_arguments()
    DATA_FOLDER_PATH = os.path.abspath(args.data_path)
    
    print(f"正在从目录加载数据: {DATA_FOLDER_PATH}")
    
    # 检查目录是否存在
    if not os.path.exists(DATA_FOLDER_PATH):
        print(f"错误: 指定的目录不存在: {DATA_FOLDER_PATH}")
        return
    
    # 检查必需的文件是否存在
    mock_data_file = os.path.join(DATA_FOLDER_PATH, "mock_data.json")
    if not os.path.exists(mock_data_file):
        print(f"错误: 必需的文件缺失: mock_data.json")
        return
    
    data = load_data(DATA_FOLDER_PATH)
      
    mcp.run()

# 示例用法
if __name__ == "__main__":
    main()