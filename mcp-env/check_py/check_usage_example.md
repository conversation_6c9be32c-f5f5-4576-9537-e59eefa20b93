# MCP工具调用结果检查脚本使用说明

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

```bash
python check.py \
  --bench /path/to/bench.json \
  --result /path/to/result.json \
  --model "gpt-3.5-turbo" \
  --base-url "https://api.openai.com/v1" \
  --api-key "your-api-key" \
  --output check_result.json \
  --work-dir /path/to/workspace
```

## 参数说明

- `--bench`: bench.json文件路径，包含测试用例和检查要求
- `--result`: result.json文件路径，包含MCP工具调用的执行结果
- `--model`: 用于内容检查的模型名称（OpenAI格式）
- `--base-url`: 模型API的base URL
- `--api-key`: 模型API密钥
- `--output`: 输出文件路径，默认为`check_result.json`
- `--work-dir`: 工作目录，相对于此目录进行文件检查，默认为当前目录

## 输入文件格式

### bench.json格式示例

```json
{
  "extra_info": {
    "updated_check_list": "[{\"type\":\"check_file_exist\",\"target\":\"/test.txt\"},{\"type\":\"check_file_content\",\"target\":\"/config.json\",\"value\":\"检查文件是否包含正确的JSON格式\"}]",
    "environment_dependency": "[{\"type\":\"file\",\"path\":\"input.txt\",\"content\":\"原始输入内容\"}]"
  }
}
```

### result.json格式示例

```json
{
  "ToolCallList": [
    {
      "Step": 1,
      "ToolName": "write_file",
      "Args": {"filename": "test.txt", "content": "Hello World"}
    }
  ],
  "TotalSteps": 1,
  "TotalTimeCostMs": 1500
}
```

## 输出格式

检查脚本会输出一个JSON文件，包含详细的检查结果：

```json
{
  "check_version": "0528_v2",
  "check_timestamp": 1640995200,
  "check_details": {
    "检查项1": {
      "检查结论": "合格",
      "原因": "存在文件",
      "详情": "存在 ./test.txt 文件"
    },
    "检查项2": {
      "检查结论": "不合格",
      "原因": "LLM检验",
      "详情": "文件内容不符合JSON格式要求"
    }
  },
  "overall_result": "Failure",
  "error_reason": "",
  "bench_file_info": {
    "updated_check_list_count": 2
  },
  "result_file_info": {
    "tool_call_count": 1,
    "total_steps": 1,
    "total_time_ms": 1500
  }
}
```

## 支持的检查类型

1. **check_directory_exist**: 检查目录是否存在
2. **check_file_exist**: 检查文件是否存在
3. **check_file_not_exist**: 检查文件不存在
4. **check_file_content**: 检查文件内容（使用LLM分析）
5. **check_response**: 暂时跳过
6. **check_tool_call_list**: 暂时跳过

## 使用示例

### 使用OpenAI模型

```bash
python check.py \
  --bench test_case.json \
  --result execution_result.json \
  --model "gpt-3.5-turbo" \
  --base-url "https://api.openai.com/v1" \
  --api-key "sk-xxxxxxxxxxxx" \
  --work-dir ./workspace
```

### 使用本地模型服务

```bash
python check.py \
  --bench test_case.json \
  --result execution_result.json \
  --model "local-llm" \
  --base-url "http://localhost:8000/v1" \
  --api-key "dummy-key" \
  --work-dir ./workspace
```

### 输出结果解释

- **Success**: 所有检查项都通过
- **Failure**: 有检查项未通过
- **Error**: 检查过程中出现错误
- **Unsure**: 部分检查项结果不确定（通常是LLM评估失败） 