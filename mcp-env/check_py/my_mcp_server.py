from fastmcp import FastMCP

mcp = FastMCP(name="My First MCP Server")

@mcp.tool()
def add(a: int, b: int) -> int:
    """Adds two integer numbers together."""
    return a + b
@mcp.prompt()
def add_prompt(a: int, b: int) -> str:
    """Adds two integer numbers together."""
    return f"The sum of {a} and {b} is {a + b}."

@mcp.resource("resource://config")
def get_config() -> dict:
    """Provides the application's configuration."""
    return {"version": "1.0", "author": "MyTeam"}

@mcp.resource("greetings://{name}")
def personalized_greeting(name: str) -> str:
    """Generates a personalized greeting for the given name."""
    return f"Hello, {name}! Welcome to the MCP server."

if __name__ == "__main__":
    mcp.run()