# MCP 失败重试功能使用说明

## 功能概述

失败重试功能允许你重新执行之前失败的 MCP 任务，而无需重新执行所有任务。这在处理大批量任务时特别有用，可以节省时间和计算资源。

## 使用方法

### 1. 基本用法

在算子参数中设置 `retry_failed_only: true`：

```json
{
  "models": ["uni_crawl:deepseek-v3"],
  "max_workers": 4,
  "retry_failed_only": true
}
```

### 2. 输入文件要求

输入的 JSONL 文件必须是之前执行过的结果文件，包含 `execution_result` 字段。例如：

```jsonl
{"data_id": 1, "extra_info": {...}, "execution_result": {"success": true, ...}}
{"data_id": 2, "extra_info": {...}, "execution_result": {"success": false, "error": "..."}}
{"data_id": 3, "extra_info": {...}, "execution_result": {"success": true, ...}}
```

### 3. 处理逻辑

当 `retry_failed_only: true` 时：

1. **筛选失败记录**：只处理 `execution_result.success = false` 的记录
2. **清理旧结果**：删除失败记录的 `bench_result`、`check_result`、`execution_result` 等字段
3. **重新执行**：使用原始的 `extra_info` 配置重新执行任务
4. **合并结果**：将新的执行结果与原始文件合并，生成完整的结果文件

### 4. 输出文件

- **正常模式**：`{input_file_stem}_{model}_results.jsonl`
- **重试模式**：`{input_file_stem}_{model}_retry_results.jsonl`

重试模式会生成新的文件，包含所有记录（成功的保持原样，失败的更新为新结果）。

## 使用场景

### 场景1：网络超时重试

```bash
# 原始执行时部分任务因网络问题失败
# 现在只重试失败的任务
{
  "models": ["uni_crawl:deepseek-v3"],
  "retry_failed_only": true,
  "max_workers": 2
}
```

### 场景2：资源不足重试

```bash
# K8s资源不足导致部分任务失败
# 降低并发数重试失败任务
{
  "models": ["uni_crawl:deepseek-v3"],
  "retry_failed_only": true,
  "max_workers": 1
}
```

### 场景3：配置错误修复后重试

```bash
# 修复了MCP服务器配置错误
# 重试之前失败的任务
{
  "models": ["uni_crawl:deepseek-v3"],
  "retry_failed_only": true,
  "check_model": "uni_crawl:deepseek-v3"
}
```

## 注意事项

1. **输入文件格式**：必须是包含 `execution_result` 的结果文件
2. **原始文件保留**：原始文件不会被修改，重试结果保存到新文件
3. **data_id 匹配**：基于 `data_id` 字段进行记录匹配和更新
4. **重试次数**：失败重试功能本身也有重试机制，会尝试多次执行失败的任务

## 日志示例

```
INFO - 失败重试模式: 从 100 条记录中筛选出 15 条失败记录进行重试
INFO - 失败重试模式: 更新了 12 条记录，最终输出 100 条记录
INFO - 生成输出文件: output/test_uni_crawl_deepseek-v3_retry_results.jsonl, 包含 100 条记录
```

这样就可以高效地重试失败的任务，而不影响已成功的结果。 