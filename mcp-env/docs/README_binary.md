# Binary 类型环境依赖

## 概述

`binary` 类型允许你通过 base64 编码的字符串来创建二进制文件。这对于在环境初始化中包含图片、可执行文件或其他二进制数据非常有用。

## 使用方法

### 基本语法

```json
{
  "path": "文件路径",
  "type": "binary", 
  "content": "base64编码的二进制数据"
}
```

### 示例

#### 1. 创建一个简单的二进制文件

```go
dep := EnvironmentDependency{
    Path:    "./data/test.bin",
    Type:    "binary",
    Content: "SGVsbG8gV29ybGQ=", // "Hello World" 的 base64 编码
}
```

#### 2. 创建PNG图片文件

```go
dep := EnvironmentDependency{
    Path:    "./images/logo.png",
    Type:    "binary",
    Content: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9YOMo3gAAAABJRU5ErkJggg==", // 1x1像素透明PNG
}
```

#### 3. 在环境管理器中使用

```go
manager := DefaultManager()

deps := []EnvironmentDependency{
    {
        Path: "./assets",
        Type: "directory",
    },
    {
        Path:    "./assets/icon.png",
        Type:    "binary",
        Content: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9YOMo3gAAAABJRU5ErkJggg==",
    },
}

results, err := manager.Initialize(context.Background(), deps)
```

## 如何获取base64编码

### 在命令行中

```bash
# 编码文件为base64
base64 -i input_file.bin

# 在Linux/Mac中
cat file.bin | base64

# 在Windows中使用PowerShell
[Convert]::ToBase64String([IO.File]::ReadAllBytes("file.bin"))
```

### 在Go代码中

```go
// 从文件读取并编码
data, err := os.ReadFile("file.bin")
if err != nil {
    log.Fatal(err)
}
encoded := base64.StdEncoding.EncodeToString(data)
```

### 在JavaScript中

```javascript
// 从文件对象获取base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result.split(',')[1]);
        reader.onerror = error => reject(error);
    });
}
```

## 特性

- ✅ 自动创建所需的目录结构
- ✅ 检查文件是否已存在（如存在则跳过创建）
- ✅ 验证base64格式的有效性
- ✅ 支持任意二进制文件格式
- ✅ 详细的错误信息和执行报告

## 错误处理

常见错误及解决方案：

- **Base64解码失败**: 检查content字段是否包含有效的base64编码
- **文件创建失败**: 检查目标路径的权限和磁盘空间
- **路径是目录**: 确保指定的路径不是一个已存在的目录

## 限制

- content字段必须是标准的base64编码（使用 `base64.StdEncoding`）
- 文件权限固定为 `0644`
- 如果目标文件已存在，不会覆盖

## 性能考虑

- base64编码会使数据大小增加约33%
- 对于大文件，建议考虑使用 `url` 类型从远程下载
- 内存中会同时存在编码和解码后的数据，注意内存使用
