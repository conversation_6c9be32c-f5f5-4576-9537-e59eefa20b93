# MCP评估平台架构方案详设

## 1. 项目概述

### 1.1 项目背景
MCP评估平台是一个专为策略工程设计的工程化自动化评估系统，基于Model Context Protocol（MCP）协议实现大模型Agent能力评估。该平台提供标准化的测试环境，能够初始化测试场景、记录模型行为、执行工具调用，并进行自动化结果验证。

### 1.2 设计目标
- **多模型支持**：支持OpenAI、Anthropic、Google、Baidu千帆、文心等多种大模型
- **标准化评估**：基于MCP协议提供统一的工具调用接口
- **自动化流程**：从环境初始化到结果验证的全自动化评估流程
- **容器化部署**：基于Kubernetes的云原生架构，支持水平扩展
- **可扩展性**：模块化设计，支持新模型和工具的快速接入

### 1.3 技术架构
- **容器化**：基于Docker和Kubernetes的云原生架构
- **微服务**：算子引擎、MCP主机、工具服务器分离
- **协议标准化**：基于MCP协议实现统一的工具调用接口
- **多语言支持**：Go语言实现核心服务，Python实现算子逻辑

## 2. 系统架构

### 2.1 整体架构
```mermaid
graph TB
    subgraph "评估系统"
        QM[query管理]
        SM[策略管理]
        IM[镜像管理]
        ER[评估报表]
        API[API Service]
    end

    subgraph "算子引擎"
        OP[Agent MCP-评估-pre-docker算子]
        QG[query list获取算子]
        RM[评估结果Merge]
    end

    subgraph "DataEng-K8S"
        subgraph "MCP-评估Docker"
            MC[MCP容器1]
            MC2[MCP容器2]
            MC3[MCP容器N]
        end
    end

    subgraph "LLM统一调度"
        LLM[LLM调度器]
        EBS[EBs]
        OBS[观测]
    end

    QM --> QG
    QG --> OP
    OP --> MC
    OP --> MC2
    OP --> MC3
    MC --> RM
    MC2 --> RM
    MC3 --> RM
    RM --> ER
    API --> ER
    MC --> LLM
    MC2 --> LLM
    MC3 --> LLM
    LLM --> EBS
    LLM --> OBS
```

### 2.2 容器内解决方案架构
```mermaid
graph TB
    subgraph "INPUTS"
        ENV[环境config<br/>Tool list MCP工具列表<br/>Inition info 初始化数据<br/>AK/SK 外部工具密钥]
        QUERY[评估query]
        CHECK[评估checklist]
        TRUTH[评估ground-truth<br/>optional]
    end

    subgraph "PROCESS"
        subgraph "启动MCP Server"
            LOCAL[mcp server local<br/>初始化]
            REMOTE[mcp server remote<br/>初始化]
            INIT1[start server<br/>Env Init]
            INIT2[Fetch AK/SK<br/>start server<br/>Env Init]
        end

        PROXY[LLM Proxy<br/>被评估大模型<br/>抓取结果]
        TOOLS[mcp server<br/>Tool Call<br/>调用工具并执行]
        SIG[In-Docker-Sig<br/>评估策略]
    end

    subgraph "MCP-localhost-process <CMD>"
        CMD[总控shell/Python <评估-CMD><br/>总控，流程管理]
    end

    ENV --> LOCAL
    ENV --> REMOTE
    LOCAL --> INIT1
    REMOTE --> INIT2
    INIT1 --> PROXY
    INIT2 --> PROXY
    QUERY --> PROXY
    PROXY --> TOOLS
    TOOLS --> SIG
    SIG --> CMD
    CMD --> SIG
    CHECK --> SIG
    TRUTH --> SIG
```

## 3. 核心组件设计

### 3.1 算子引擎（Operator）

#### 3.1.1 组件概述
位于`operator/`目录，基于DataEng-SDK实现的Kubernetes算子，负责批量任务的调度和管理。

#### 3.1.2 核心功能
- **配置管理**：解析模型配置、MCP服务器配置和测试配置
- **任务调度**：支持本地和K8s两种执行模式
- **并发控制**：通过ThreadPoolExecutor实现多任务并发执行
- **结果聚合**：收集各个评估任务的结果并生成汇总报告

#### 3.1.3 关键类设计
```python
class Operator(OperatorBase):
    """MCP配置生成器和批量执行器算子"""
    
    def _setup(self):
        """算子执行前准备工作"""
        # 解析参数
        # 创建工作目录
        # 验证输入
    
    def _run(self):
        """主要执行逻辑"""
        # 处理JSONL文件
        # 并发执行任务
        # 生成汇总报告
    
    def _execute_single_task_k8s(self, task_info):
        """K8s模式执行单个任务"""
        # 提交K8s作业
        # 监控作业状态
        # 收集结果
    
    def _execute_single_task_local(self, task_info):
        """本地模式执行单个任务"""
        # 调用mcphost可执行文件
        # 监控进程状态
        # 收集结果
```

#### 3.1.4 配置管理
支持三类配置文件：
- **模型配置**：不同LLM提供商的接入参数
- **MCP服务器配置**：工具服务器的启动参数
- **评估配置**：测试用例、期望结果、验证规则

### 3.2 MCP主机（MCPHost）

#### 3.2.1 组件概述
位于`mcphost/`目录，基于Go语言实现的MCP协议客户端，负责与大模型和工具服务器的交互。

#### 3.2.2 核心功能
- **多模型支持**：统一接口支持OpenAI、Anthropic、Google、百度等模型
- **MCP客户端**：实现MCP协议，管理与工具服务器的连接
- **工具调用**：处理模型的工具调用请求，转发给相应的MCP服务器
- **结果记录**：详细记录交互过程，生成结构化的评估结果

#### 3.2.3 架构设计
```go
// 核心接口定义
type Provider interface {
    Name() string
    Prompt(ctx context.Context, messages []Message, tools []Tool) (*PromptResult, error)
}

// 主要模块
- cmd/root.go          // 命令行入口和配置管理
- pkg/llm/            // LLM提供商抽象层
  ├── openai/         // OpenAI兼容接口
  ├── anthropic/      // Anthropic Claude接口  
  ├── google/         // Google Gemini接口
  ├── assistant_api/  // 百度文心/千帆接口
  └── ollama/         // Ollama本地模型接口
- pkg/environment/    // 环境初始化模块
- pkg/history/        // 对话历史管理
- utils/             // 通用工具函数
```

#### 3.2.4 流程控制
```go
func runBenchMode(ctx context.Context) error {
    // 1. 加载配置文件
    modelConfig := loadModelConfig()
    mcpConfig := loadMCPConfig()
    benchConfig := loadBenchConfig()
    
    // 2. 初始化MCP客户端
    mcpClients := initializeMCPClients(mcpConfig)
    
    // 3. 创建LLM提供者
    provider := createProvider(modelConfig)
    
    // 4. 执行评估任务
    result := executeEvaluation(provider, mcpClients, benchConfig)
    
    // 5. 保存结果
    saveResult(result)
}
```

### 3.3 工具生态系统

#### 3.3.1 MCP服务器支持
- **文件系统服务器**：@modelcontextprotocol/server-filesystem
- **数据库服务器**：mcp-server-sqlite
- **网页操作服务器**：@playwright/mcp
- **网页抓取服务器**：firecrawl-mcp
- **地图服务器**：amap-maps（SSE模式）

#### 3.3.2 协议支持
- **STDIO模式**：通过标准输入输出与子进程通信
- **SSE模式**：通过Server-Sent Events与HTTP服务通信

## 4. 数据流设计

### 4.1 评估任务数据流
```mermaid
sequenceDiagram
    participant UI as 评估系统UI
    participant OP as 算子引擎
    participant K8S as K8s集群
    participant MC as MCP容器
    participant LLM as LLM服务
    participant MCP as MCP服务器

    UI->>OP: 提交评估任务
    OP->>OP: 解析配置文件
    OP->>K8S: 创建Pod
    K8S->>MC: 启动容器
    MC->>MCP: 启动工具服务器
    MC->>LLM: 发送prompt
    LLM->>MC: 返回工具调用
    MC->>MCP: 执行工具调用
    MCP->>MC: 返回执行结果
    MC->>LLM: 提交工具结果
    LLM->>MC: 返回最终回答
    MC->>K8S: 输出评估结果
    K8S->>OP: 收集结果
    OP->>UI: 返回汇总报告
```

### 4.2 配置数据流
```mermaid
graph LR
    subgraph "输入配置"
        MC[模型配置]
        SC[服务器配置]
        BC[评估配置]
    end
    
    subgraph "算子处理"
        PP[参数解析]
        CG[配置生成]
        TV[模板验证]
    end
    
    subgraph "容器执行"
        ENV[环境初始化]
        EXEC[任务执行]
        RESULT[结果收集]
    end
    
    MC --> PP
    SC --> PP
    BC --> PP
    PP --> CG
    CG --> TV
    TV --> ENV
    ENV --> EXEC
    EXEC --> RESULT
```

## 5. 技术栈和依赖

### 5.1 编程语言和框架
- **Go 1.23+**：MCPHost核心服务
- **Python 3.11+**：算子引擎和MCP服务器
- **Node.js 20+**：部分MCP服务器依赖

### 5.2 核心依赖库

#### Go依赖
```go
// go.mod主要依赖
github.com/mark3labs/mcp-go            // MCP协议客户端
github.com/spf13/cobra                 // 命令行框架
github.com/charmbracelet/log           // 日志库
google.golang.org/ai/generativelanguage // Google AI
```

#### Python依赖
```python
# requirements.txt主要依赖
dataeng-sdk                    # DataEng算子SDK
requests                       # HTTP客户端
PyYAML                        # YAML配置解析
concurrent.futures            # 并发执行
```

### 5.3 外部服务依赖
- **Kubernetes**：容器编排平台
- **Docker Registry**：镜像仓库
- **各大模型API**：OpenAI、Anthropic、Google、百度等

## 6. 部署架构

### 6.1 容器化设计
```dockerfile
# 多阶段构建Dockerfile
FROM golang:1.23 AS go-builder
# 构建mcphost二进制文件

FROM node:20 AS node-builder  
# 安装Node.js依赖

FROM python:3.11-slim AS runtime
# 最终运行环境
# 安装Python依赖
# 复制go和node构建产物
# 设置启动脚本
```

### 6.2 K8s部署模式
```yaml
# K8s Job模板
apiVersion: batch/v1
kind: Job
metadata:
  name: mcp-evaluation-job
spec:
  template:
    spec:
      containers:
      - name: mcp-container
        image: mcp/mcp-env:latest
        command: ["/bin/bash", "/home/<USER>/boost.sh"]
        env:
        - name: DATAENG_INPUT_DATA_DIR
          value: "/home/<USER>/input"
        - name: DATAENG_OUTPUT_DIR
          value: "/home/<USER>/output"
        volumeMounts:
        - name: input-volume
          mountPath: /home/<USER>/input
        - name: output-volume
          mountPath: /home/<USER>/output
```

### 6.3 网络架构
- **Pod内通信**：localhost回环
- **Pod间通信**：K8s Service网络
- **外部API调用**：通过NodePort/LoadBalancer

## 7. 扩展性设计

### 7.1 模型提供商扩展
新增模型提供商只需实现Provider接口：
```go
type NewProvider struct {
    // 特定配置
}

func (p *NewProvider) Name() string {
    return "new-provider"
}

func (p *NewProvider) Prompt(ctx context.Context, messages []Message, tools []Tool) (*PromptResult, error) {
    // 实现具体的模型调用逻辑
}
```

### 7.2 工具服务器扩展
- **STDIO模式**：添加新的命令行工具服务器
- **SSE模式**：添加新的HTTP服务工具服务器
- **自定义协议**：扩展MCP客户端支持新协议

### 7.3 评估策略扩展
```python
class CustomEvaluator:
    def evaluate(self, expected, actual, context):
        # 自定义评估逻辑
        pass
```

## 8. 监控和运维

### 8.1 日志体系
- **结构化日志**：JSON格式，便于检索
- **分层日志**：DEBUG、INFO、WARN、ERROR
- **分布式追踪**：请求ID跟踪

### 8.2 监控指标
- **任务执行指标**：成功率、耗时、并发数
- **模型调用指标**：Token消耗、响应时间、错误率
- **资源使用指标**：CPU、内存、存储

### 8.3 错误处理
- **重试机制**：指数退避重试
- **熔断机制**：防止级联故障
- **优雅降级**：部分服务不可用时的备选方案

## 9. 安全设计

### 9.1 API密钥管理
- **环境变量**：敏感信息通过环境变量传递
- **K8s Secret**：利用Kubernetes原生秘钥管理
- **密钥轮换**：支持动态密钥更新

### 9.2 网络安全
- **容器隔离**：每个评估任务运行在独立容器中
- **网络策略**：限制Pod间不必要的网络访问
- **TLS加密**：外部API调用使用HTTPS

### 9.3 数据安全
- **数据脱敏**：敏感数据在日志中脱敏处理
- **临时存储**：评估数据在容器销毁后自动清理
- **访问控制**：基于RBAC的权限控制

## 10. 性能优化

### 10.1 并发优化
- **任务并发**：算子层面支持多任务并发
- **连接池**：HTTP客户端连接复用
- **异步处理**：非阻塞I/O操作

### 10.2 资源优化
- **容器资源限制**：合理设置CPU和内存限制
- **镜像优化**：多阶段构建减小镜像体积
- **缓存策略**：依赖包和配置文件缓存

### 10.3 网络优化
- **本地化部署**：减少网络延迟
- **CDN加速**：静态资源通过CDN分发
- **压缩传输**：HTTP响应压缩

## 11. 未来演进

### 11.1 技术演进方向
- **微服务化**：进一步拆分为更细粒度的微服务
- **事件驱动**：引入消息队列实现异步处理
- **AI增强**：智能化的测试用例生成和结果分析

### 11.2 功能扩展计划
- **可视化界面**：Web界面支持任务配置和结果查看
- **实时监控**：实时展示评估进度和系统状态
- **自动化报告**：AI生成评估报告和优化建议

### 11.3 生态建设
- **开源社区**：开放核心组件，建设开发者生态
- **标准制定**：参与MCP协议标准的制定和推广
- **平台化**：构建一站式AI评估平台

---

本架构方案详设为MCP评估平台的完整技术架构文档，涵盖了系统的各个方面，为后续的开发、部署和运维提供了详细的指导。该方案充分考虑了系统的可扩展性、可维护性和性能要求，同时保持了良好的技术前瞻性。