# MCP评估平台架构方案详设

## 1. 项目概述

### 1.1 项目背景
MCP评估平台是一个专为策略工程设计的工程化自动化评估系统，基于Model Context Protocol（MCP）协议实现大模型Agent能力评估。该平台提供标准化的测试环境，能够初始化测试场景、记录模型行为、执行工具调用，并进行自动化结果验证。

该平台解决了大模型Agent评估中的关键痛点：
- **环境一致性**：通过容器化确保评估环境的一致性和可重复性
- **工具标准化**：基于MCP协议统一不同工具的调用接口
- **评估自动化**：从任务分发到结果验证的全流程自动化
- **规模化部署**：支持大规模并发评估任务的调度和执行

### 1.2 设计目标
- **多模型支持**：支持OpenAI、Anthropic、Google、Baidu千帆、文心等多种大模型
- **标准化评估**：基于MCP协议提供统一的工具调用接口
- **自动化流程**：从环境初始化到结果验证的全自动化评估流程
- **容器化部署**：基于Kubernetes的云原生架构，支持水平扩展
- **可扩展性**：模块化设计，支持新模型和工具的快速接入
- **高可靠性**：完善的错误处理、重试机制和监控体系
- **安全性**：API密钥安全管理、容器隔离、数据脱敏

### 1.3 技术架构特点
- **容器化**：基于Docker和Kubernetes的云原生架构
- **微服务**：算子引擎、MCP主机、工具服务器分离
- **协议标准化**：基于MCP协议实现统一的工具调用接口
- **多语言支持**：Go语言实现核心服务，Python实现算子逻辑
- **混合部署**：支持本地开发和K8s生产环境的无缝切换
- **开源生态**：基于开源MCP工具生态，部分组件自研扩展

## 2. 系统架构

### 2.1 整体架构
```mermaid
graph TB
    subgraph "评估系统"
        QM[query管理]
        SM[策略管理]
        IM[镜像管理]
        ER[评估报表]
        API[API Service]
    end

    subgraph "算子引擎"
        OP[Agent MCP-评估-pre-docker算子]
        QG[query list获取算子]
        RM[评估结果Merge]
    end

    subgraph "DataEng-K8S"
        subgraph "MCP-评估Docker"
            MC[MCP容器1]
            MC2[MCP容器2]
            MC3[MCP容器N]
        end
    end

    subgraph "LLM统一调度"
        LLM[LLM调度器]
        EBS[EBs]
        OBS[观测]
    end

    QM --> QG
    QG --> OP
    OP --> MC
    OP --> MC2
    OP --> MC3
    MC --> RM
    MC2 --> RM
    MC3 --> RM
    RM --> ER
    API --> ER
    MC --> LLM
    MC2 --> LLM
    MC3 --> LLM
    LLM --> EBS
    LLM --> OBS
```

### 2.2 容器内解决方案架构
```mermaid
graph TB
    subgraph "INPUTS"
        ENV[环境config<br/>Tool list MCP工具列表<br/>Inition info 初始化数据<br/>AK/SK 外部工具密钥]
        QUERY[评估query]
        CHECK[评估checklist]
        TRUTH[评估ground-truth<br/>optional]
    end

    subgraph "PROCESS"
        subgraph "启动MCP Server"
            LOCAL[mcp server local<br/>初始化]
            REMOTE[mcp server remote<br/>初始化]
            INIT1[start server<br/>Env Init]
            INIT2[Fetch AK/SK<br/>start server<br/>Env Init]
        end

        PROXY[LLM Proxy<br/>被评估大模型<br/>抓取结果]
        TOOLS[mcp server<br/>Tool Call<br/>调用工具并执行]
        SIG[In-Docker-Sig<br/>评估策略]
    end

    subgraph "MCP-localhost-process <CMD>"
        CMD[总控shell/Python <评估-CMD><br/>总控，流程管理]
    end

    ENV --> LOCAL
    ENV --> REMOTE
    LOCAL --> INIT1
    REMOTE --> INIT2
    INIT1 --> PROXY
    INIT2 --> PROXY
    QUERY --> PROXY
    PROXY --> TOOLS
    TOOLS --> SIG
    SIG --> CMD
    CMD --> SIG
    CHECK --> SIG
    TRUTH --> SIG
```

## 3. 核心组件设计

### 3.1 算子引擎（Operator）

#### 3.1.1 组件概述
位于`operator/`目录，基于DataEng-SDK实现的Kubernetes算子，负责批量任务的调度和管理。

#### 3.1.2 核心功能
- **配置管理**：解析模型配置、MCP服务器配置和测试配置
- **任务调度**：支持本地和K8s两种执行模式
- **并发控制**：通过ThreadPoolExecutor实现多任务并发执行
- **结果聚合**：收集各个评估任务的结果并生成汇总报告

#### 3.1.3 关键类设计
```python
class Operator(OperatorBase):
    """MCP配置生成器和批量执行器算子"""
    
    def _setup(self):
        """算子执行前准备工作"""
        # 解析参数
        # 创建工作目录
        # 验证输入
    
    def _run(self):
        """主要执行逻辑"""
        # 处理JSONL文件
        # 并发执行任务
        # 生成汇总报告
    
    def _execute_single_task_k8s(self, task_info):
        """K8s模式执行单个任务"""
        # 提交K8s作业
        # 监控作业状态
        # 收集结果
    
    def _execute_single_task_local(self, task_info):
        """本地模式执行单个任务"""
        # 调用mcphost可执行文件
        # 监控进程状态
        # 收集结果
```

#### 3.1.4 配置管理
支持三类配置文件：
- **模型配置**：不同LLM提供商的接入参数
- **MCP服务器配置**：工具服务器的启动参数
- **评估配置**：测试用例、期望结果、验证规则

### 3.2 MCP主机（MCPHost）

#### 3.2.1 组件概述
位于`mcphost/`目录，基于Go语言实现的MCP协议客户端，负责与大模型和工具服务器的交互。

#### 3.2.2 核心功能
- **多模型支持**：统一接口支持OpenAI、Anthropic、Google、百度等模型
- **MCP客户端**：实现MCP协议，管理与工具服务器的连接
- **工具调用**：处理模型的工具调用请求，转发给相应的MCP服务器
- **结果记录**：详细记录交互过程，生成结构化的评估结果

#### 3.2.3 架构设计
```go
// 核心接口定义
type Provider interface {
    CreateMessage(ctx context.Context, prompt string, messages []Message, tools []Tool) (Message, error)
    SupportsTools() bool
    Name() string
}

// 消息接口
type Message interface {
    GetRole() string
    GetContent() string
    GetReasoningContent() string
    GetToolCalls() []ToolCall
    IsToolResponse() bool
    GetToolResponseID() string
    GetUsage() (input int, output int)
}

// 工具调用接口
type ToolCall interface {
    GetName() string
    GetArguments() map[string]interface{}
    GetID() string
}
```

#### 3.2.4 模块结构
```
mcphost/
├── cmd/
│   ├── root.go              // 命令行入口和主流程控制
│   └── mcp.go              // MCP客户端管理和配置解析
├── pkg/
│   ├── llm/                // LLM提供商抽象层
│   │   ├── provider.go     // 核心接口定义
│   │   ├── openai/         // OpenAI兼容接口实现
│   │   ├── anthropic/      // Anthropic Claude接口实现
│   │   ├── google/         // Google Gemini接口实现
│   │   ├── assistant_api/  // 百度文心/千帆接口实现
│   │   └── ollama/         // Ollama本地模型接口实现
│   ├── environment/        // 环境初始化模块
│   │   ├── types.go        // 环境依赖类型定义
│   │   ├── manager.go      // 环境管理器
│   │   ├── directory_strategy.go  // 目录创建策略
│   │   ├── file_strategy.go       // 文件创建策略
│   │   ├── database_strategy.go   // 数据库初始化策略
│   │   └── factory.go      // 策略工厂
│   ├── check/              // 结果验证模块
│   │   ├── checker.go      // 检查器管理
│   │   ├── rule_checker.go // 规则检查器
│   │   └── llm_checker.go  // LLM检查器
│   └── history/            // 对话历史管理
├── utils/                  // 通用工具函数
├── main.go                 // 程序入口
└── boost.sh               // 容器启动脚本
```

#### 3.2.5 详细执行流程
```go
func runBenchMode(ctx context.Context) error {
    // 1. 加载和验证配置文件
    benchConfig, err := loadBenchConfig()
    if err != nil {
        return fmt.Errorf("加载测试配置失败: %v", err)
    }

    modelConfig, err := loadModelConfig()
    if err != nil {
        return fmt.Errorf("加载模型配置失败: %v", err)
    }

    // 2. 环境初始化
    if len(benchConfig.ExtraInfo.EnvironmentDependency) > 0 {
        envManager := environment.DefaultManager()
        results, err := envManager.Initialize(ctx, benchConfig.ExtraInfo.EnvironmentDependency)
        if err != nil {
            return fmt.Errorf("环境初始化失败: %v", err)
        }
        log.Info("环境初始化完成", "results", len(results))
    }

    // 3. 创建MCP客户端
    mcpClients, err := createMCPClients(mcpConfig)
    if err != nil {
        return fmt.Errorf("创建MCP客户端失败: %v", err)
    }
    defer closeMCPClients(mcpClients)

    // 4. 创建LLM提供者
    provider, err := createProvider(modelConfig)
    if err != nil {
        return fmt.Errorf("创建LLM提供者失败: %v", err)
    }

    // 5. 收集可用工具
    allTools := collectAvailableTools(mcpClients)

    // 6. 执行对话和工具调用
    query := benchConfig.GetQuery()
    systemPrompt := benchConfig.GetSystemPrompt()

    var benchResult BenchResult
    benchResult.ToolCallList = make([]ToolCallRecord, 0)
    benchResult.CheckList = make([]CheckResult, 0)
    benchResult.AvailableTools = allTools

    // 根据provider类型选择处理方式
    if assistantProvider, ok := provider.(*assistant_api.Provider); ok {
        err = runAssistantAPIWithRecording(ctx, assistantProvider, mcpClients, allTools, query, &messages, &benchResult)
    } else {
        err = runPromptWithToolRecording(ctx, provider, mcpClients, allTools, query, &messages, &benchResult)
    }

    // 7. 执行结果验证
    if len(benchConfig.ExtraInfo.CheckList) > 0 {
        checkerManager := check.NewCheckerManager()
        checkerManager.RegisterChecker("rule_judge", check.NewRuleChecker())
        checkerManager.RegisterChecker("llm_judge", check.NewLLMChecker(provider))

        for _, checkItem := range benchConfig.ExtraInfo.CheckList {
            result := checkerManager.Check(ctx, checkItem)
            benchResult.CheckList = append(benchResult.CheckList, result)
        }
    }

    // 8. 保存结果
    return saveResult(benchResult)
}
```

### 3.3 工具生态系统

#### 3.3.1 支持的MCP服务器
| 服务器名称 | 功能描述 | 通信模式 | 配置示例 |
|-----------|---------|---------|---------|
| **filesystem** | 文件系统操作 | STDIO | `@modelcontextprotocol/server-filesystem` |
| **sqlite** | SQLite数据库操作 | STDIO | `mcp-server-sqlite --db-path ./db.sqlite` |
| **playwright** | 网页自动化操作 | STDIO | `@playwright/mcp --headless --browser chromium` |
| **firecrawl-mcp** | 网页内容抓取 | STDIO | `firecrawl-mcp` (需要API密钥) |
| **amap-maps** | 地图服务 | SSE | HTTP服务模式 |

#### 3.3.2 通信协议详解

**STDIO模式实现**：
```go
// STDIO服务器配置
type STDIOServerConfig struct {
    Command string            `json:"command"`
    Args    []string         `json:"args"`
    Env     map[string]string `json:"env"`
}

// 创建STDIO客户端
client, err := mcpclient.NewStdioMCPClient(
    stdioConfig.Command,
    env,
    stdioConfig.Args...)
```

**SSE模式实现**：
```go
// SSE服务器配置
type SSEServerConfig struct {
    URL string `json:"url"`
}

// 创建SSE客户端
client, err := mcpclient.NewSSEMCPClient(sseConfig.URL)
```

#### 3.3.3 工具调用流程
```mermaid
sequenceDiagram
    participant LLM as LLM模型
    participant MCPHost as MCPHost
    participant MCPClient as MCP客户端
    participant MCPServer as MCP服务器

    LLM->>MCPHost: 工具调用请求
    MCPHost->>MCPHost: 解析工具名称和参数
    MCPHost->>MCPClient: 转发工具调用
    MCPClient->>MCPServer: 执行具体工具
    MCPServer->>MCPClient: 返回执行结果
    MCPClient->>MCPHost: 返回结果
    MCPHost->>MCPHost: 记录工具调用
    MCPHost->>LLM: 返回工具执行结果
```

## 4. 数据流设计

### 4.1 评估任务数据流
```mermaid
sequenceDiagram
    participant UI as 评估系统UI
    participant OP as 算子引擎
    participant K8S as K8s集群
    participant MC as MCP容器
    participant LLM as LLM服务
    participant MCP as MCP服务器

    UI->>OP: 提交评估任务
    OP->>OP: 解析配置文件
    OP->>K8S: 创建Pod
    K8S->>MC: 启动容器
    MC->>MCP: 启动工具服务器
    MC->>LLM: 发送prompt
    LLM->>MC: 返回工具调用
    MC->>MCP: 执行工具调用
    MCP->>MC: 返回执行结果
    MC->>LLM: 提交工具结果
    LLM->>MC: 返回最终回答
    MC->>K8S: 输出评估结果
    K8S->>OP: 收集结果
    OP->>UI: 返回汇总报告
```

### 4.2 配置数据流
```mermaid
graph LR
    subgraph "输入配置"
        MC[模型配置]
        SC[服务器配置]
        BC[评估配置]
    end
    
    subgraph "算子处理"
        PP[参数解析]
        CG[配置生成]
        TV[模板验证]
    end
    
    subgraph "容器执行"
        ENV[环境初始化]
        EXEC[任务执行]
        RESULT[结果收集]
    end
    
    MC --> PP
    SC --> PP
    BC --> PP
    PP --> CG
    CG --> TV
    TV --> ENV
    ENV --> EXEC
    EXEC --> RESULT
```

## 5. 配置管理详设

### 5.1 配置文件结构

#### 5.1.1 模型配置 (model_config.json)
```json
{
  "model_name": "gpt-4o-mini",
  "model_provider": "openai",
  "openai_url": "https://api.openai.com/v1",
  "openai_api_key": "${OPENAI_API_KEY}",
  "global_timeout": 30,

  // 百度文心/千帆配置
  "assistant_id": "asst_xxx",
  "authorization": "Bearer xxx",
  "backend": "qianfan",
  "app_id": "xxx",

  // Anthropic配置
  "anthropic_url": "https://api.anthropic.com",
  "anthropic_api_key": "${ANTHROPIC_API_KEY}",

  // Google配置
  "google_api_key": "${GOOGLE_API_KEY}"
}
```

#### 5.1.2 MCP服务器配置 (mcpservers.json)
```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem@2025.3.28", "./"],
      "env": {}
    },
    "sqlite": {
      "command": "uvx",
      "args": ["mcp-server-sqlite", "--db-path", "./test.db"],
      "env": {}
    },
    "playwright": {
      "command": "npx",
      "args": [
        "-y", "@playwright/mcp@0.0.27",
        "--headless", "--browser", "chromium",
        "--no-sandbox",
        "--executable-path", "/usr/bin/chromium"
      ],
      "env": {}
    },
    "amap-maps": {
      "type": "sse",
      "url": "https://mcp.api-inference.modelscope.cn/sse/amap"
    }
  }
}
```

#### 5.1.3 评估配置 (bench.json)
```json
{
  "data_id": 1,
  "type": "static",
  "tags": ["filesystem", "单轮"],
  "ground_truth": "期望的结果描述",
  "reference": "参考信息",
  "system": "你是一个有用的助手，擅长调用工具解决问题。",
  "extra_info": {
    "index": 1,
    "sub_scenario": "文件操作测试",
    "difficulty_level": "easy",
    "query": "请创建一个名为test.txt的文件，内容为'Hello World'",
    "environment_dependency": [
      {
        "path": "./test_output/",
        "type": "directory",
        "content": ""
      },
      {
        "path": "./test.db",
        "type": "db",
        "content": "CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT);"
      }
    ],
    "check_list": [
      {
        "type": "check_file_exist",
        "target": "./test_output/test.txt",
        "method": "rule_judge",
        "pass_required": true,
        "value": "文件应该存在"
      },
      {
        "type": "check_file_content",
        "target": "./test_output/test.txt",
        "method": "llm_judge",
        "pass_required": true,
        "value": "文件内容应该包含'Hello World'"
      }
    ]
  }
}
```

### 5.2 技术栈和依赖

#### 5.2.1 编程语言和框架
- **Go 1.23+**：MCPHost核心服务，高性能并发处理
- **Python 3.11+**：算子引擎，丰富的数据处理生态
- **Node.js 20+**：MCP服务器运行时，JavaScript生态支持
- **Bash Shell**：容器启动脚本和环境管理

#### 5.2.2 Go依赖详解
```go
// go.mod核心依赖
module github.com/mark3labs/mcphost

go 1.23

require (
    github.com/mark3labs/mcp-go v0.1.0           // MCP协议客户端实现
    github.com/spf13/cobra v1.8.0               // 命令行框架
    github.com/charmbracelet/log v0.4.0         // 结构化日志库
    google.golang.org/api v0.169.0              // Google AI API
    github.com/google/generative-ai-go v0.15.0  // Google Gemini SDK
    icode.baidu.com/baidu/gdp/logit v1.0.0      // 百度内部日志库
)
```

#### 5.2.3 Python依赖详解
```python
# operator/requirements.txt
dataeng-sdk>=1.0.0           # DataEng算子开发框架
requests>=2.31.0             # HTTP客户端
PyYAML>=6.0                  # YAML配置解析
pathlib>=1.0.1               # 路径操作
concurrent.futures           # 并发执行（标准库）
json                         # JSON处理（标准库）
subprocess                   # 子进程管理（标准库）
```

#### 5.2.4 Node.js依赖
```json
{
  "dependencies": {
    "@modelcontextprotocol/server-filesystem": "^2025.3.28",
    "@playwright/mcp": "^0.0.27",
    "firecrawl-mcp": "^1.11.0",
    "mcp-server-sqlite": "latest"
  }
}
```

### 5.3 外部服务依赖
- **Kubernetes**：容器编排平台，支持大规模任务调度
- **Docker Registry**：镜像仓库，存储和分发容器镜像
- **LLM API服务**：
  - OpenAI GPT系列
  - Anthropic Claude系列
  - Google Gemini系列
  - 百度千帆/文心系列
- **监控服务**：日志收集、指标监控、链路追踪

## 6. 部署架构详设

### 6.1 容器化设计

#### 6.1.1 多阶段构建Dockerfile
```dockerfile
# 第一阶段：Go构建环境
FROM golang:1.23-alpine AS go-builder
WORKDIR /app
COPY mcphost/go.mod mcphost/go.sum ./
RUN go mod download
COPY mcphost/ .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o mcphost .

# 第二阶段：Node.js环境准备
FROM node:20-alpine AS node-builder
RUN npm install -g @modelcontextprotocol/server-filesystem@2025.3.28 \
    @playwright/mcp@0.0.27 \
    firecrawl-mcp@1.11.0

# 第三阶段：Python运行环境
FROM python:3.11-slim AS runtime
WORKDIR /home/<USER>

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    sqlite3 \
    chromium \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install uv

# 安装Python依赖
RUN pip config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple
RUN pip install --no-cache-dir --user dspy-ai mcp fastmcp asyncio aiofiles

# 使用uv安装SQLite MCP服务器
RUN uv tool install mcp-server-sqlite

# 复制构建产物
COPY --from=go-builder /app/mcphost /home/<USER>/mcphost
COPY --from=node-builder /usr/local/lib/node_modules /usr/local/lib/node_modules
COPY --from=node-builder /usr/local/bin /usr/local/bin

# 创建必要目录
RUN mkdir -p /home/<USER>/input /home/<USER>/data /home/<USER>/output /home/<USER>/logs

# 复制启动脚本
COPY mcphost/boost.sh /home/<USER>/boost.sh
RUN chmod +x /home/<USER>/boost.sh

# 设置环境变量
ENV PATH="/home/<USER>/.local/bin:$PATH"
ENV DATAENG_INPUT_DATA_DIR="/home/<USER>/input"
ENV DATAENG_OUTPUT_DIR="/home/<USER>/output"

# 设置入口点
ENTRYPOINT ["./boost.sh"]
```

#### 6.1.2 启动脚本设计 (boost.sh)
```bash
#!/bin/bash

# 创建日志目录
mkdir -p ${DATAENG_OUTPUT_DIR}

# 生成唯一日志文件名
LOG_FILE_NAME=$(date '+%Y%m%d_%H%M%S_')${RANDOM}
LOG_FILE="${DATAENG_OUTPUT_DIR}/${LOG_FILE_NAME}.log"

# 配置Python包源
pip config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple
export UV_DEFAULT_INDEX=https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple

echo "日志将保存到: $LOG_FILE"
echo "开始执行 mcphost..."

# 启动应用并重定向输出到日志文件
./mcphost --config ${DATAENG_INPUT_DATA_DIR}/model_config.json \
    --mcpservers ${DATAENG_INPUT_DATA_DIR}/mcpservers.json \
    --bench ${DATAENG_INPUT_DATA_DIR}/bench.json \
    -o ${DATAENG_OUTPUT_DIR}/result.json 2>&1 | tee "$LOG_FILE"

echo "执行完成，日志已保存到: $LOG_FILE"
```

### 6.2 Kubernetes部署模式

#### 6.2.1 Job模板定义
```yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: mcp-evaluation-${TASK_ID}
  labels:
    app: mcp-evaluation
    task-id: ${TASK_ID}
spec:
  backoffLimit: 3
  activeDeadlineSeconds: 3600  # 1小时超时
  template:
    metadata:
      labels:
        app: mcp-evaluation
        task-id: ${TASK_ID}
    spec:
      restartPolicy: Never
      containers:
      - name: mcp-container
        image: ${DOCKER_REGISTRY}/mcp/mcp-env:${IMAGE_TAG}
        command: ["/bin/bash", "/home/<USER>/boost.sh"]
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2"
        env:
        - name: DATAENG_INPUT_DATA_DIR
          value: "/home/<USER>/input"
        - name: DATAENG_OUTPUT_DIR
          value: "/home/<USER>/output"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: openai-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: anthropic-api-key
        volumeMounts:
        - name: input-volume
          mountPath: /home/<USER>/input
          readOnly: true
        - name: output-volume
          mountPath: /home/<USER>/output
        - name: tmp-volume
          mountPath: /tmp
      volumes:
      - name: input-volume
        configMap:
          name: mcp-evaluation-config-${TASK_ID}
      - name: output-volume
        persistentVolumeClaim:
          claimName: mcp-evaluation-output-${TASK_ID}
      - name: tmp-volume
        emptyDir: {}
```

#### 6.2.2 ConfigMap和Secret管理
```yaml
# API密钥Secret
apiVersion: v1
kind: Secret
metadata:
  name: llm-api-keys
type: Opaque
data:
  openai-api-key: <base64-encoded-key>
  anthropic-api-key: <base64-encoded-key>
  google-api-key: <base64-encoded-key>

---
# 配置文件ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: mcp-evaluation-config-${TASK_ID}
data:
  model_config.json: |
    {
      "model_name": "gpt-4o-mini",
      "model_provider": "openai",
      "openai_url": "https://api.openai.com/v1",
      "openai_api_key": "${OPENAI_API_KEY}",
      "global_timeout": 30
    }
  mcpservers.json: |
    {
      "mcpServers": {
        "filesystem": {
          "command": "npx",
          "args": ["-y", "@modelcontextprotocol/server-filesystem", "./"]
        }
      }
    }
  bench.json: |
    {
      "data_id": 1,
      "system": "你是一个有用的助手",
      "extra_info": {
        "query": "测试查询内容"
      }
    }
```

### 6.3 网络架构设计

#### 6.3.1 网络拓扑
```mermaid
graph TB
    subgraph "K8s集群"
        subgraph "评估Pod"
            MCPHost[MCPHost进程]
            MCPServers[MCP服务器进程]
            MCPHost -.->|localhost| MCPServers
        end

        subgraph "网络层"
            Service[K8s Service]
            Ingress[Ingress Controller]
        end
    end

    subgraph "外部服务"
        OpenAI[OpenAI API]
        Anthropic[Anthropic API]
        Google[Google API]
        Baidu[百度API]
    end

    MCPHost -->|HTTPS| OpenAI
    MCPHost -->|HTTPS| Anthropic
    MCPHost -->|HTTPS| Google
    MCPHost -->|HTTPS| Baidu

    Service --> MCPHost
    Ingress --> Service
```

#### 6.3.2 网络策略
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mcp-evaluation-network-policy
spec:
  podSelector:
    matchLabels:
      app: mcp-evaluation
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from: []  # 不允许入站流量
  egress:
  - to: []    # 允许所有出站流量（访问LLM API）
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
```

## 7. 环境初始化和结果验证详设

### 7.1 环境初始化系统

#### 7.1.1 环境依赖类型
```go
type EnvironmentDependency struct {
    Path    string `json:"path"`
    Type    string `json:"type"`    // "directory", "file", "db"
    Content string `json:"content"` // 文件内容或SQL语句
}
```

#### 7.1.2 初始化策略模式
```go
// 策略接口
type InitializationStrategy interface {
    GetSupportedTypes() []string
    Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error)
}

// 目录创建策略
type DirectoryStrategy struct{}
func (s *DirectoryStrategy) Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error) {
    dirPath := dep.Path
    if strings.HasPrefix(dirPath, "/") {
        dirPath = "." + dirPath
    }

    if err := os.MkdirAll(dirPath, 0755); err != nil {
        return &InitializationResult{Success: false, ErrorMessage: err.Error()}, err
    }

    return &InitializationResult{Success: true, Path: dirPath, Type: "directory"}, nil
}

// 文件创建策略
type FileStrategy struct{}
func (s *FileStrategy) Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error) {
    filePath := dep.Path
    if strings.HasPrefix(filePath, "/") {
        filePath = "." + filePath
    }

    // 确保父目录存在
    if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
        return &InitializationResult{Success: false, ErrorMessage: err.Error()}, err
    }

    // 写入文件内容
    if err := os.WriteFile(filePath, []byte(dep.Content), 0644); err != nil {
        return &InitializationResult{Success: false, ErrorMessage: err.Error()}, err
    }

    return &InitializationResult{Success: true, Path: filePath, Type: "file"}, nil
}

// 数据库初始化策略
type DatabaseStrategy struct{}
func (s *DatabaseStrategy) Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error) {
    dbPath := dep.Path
    if strings.HasPrefix(dbPath, "/") {
        dbPath = "." + dbPath
    }

    // 打开SQLite数据库
    db, err := sql.Open("sqlite3", dbPath)
    if err != nil {
        return &InitializationResult{Success: false, ErrorMessage: err.Error()}, err
    }
    defer db.Close()

    // 执行初始化SQL
    if dep.Content != "" {
        if _, err := db.ExecContext(ctx, dep.Content); err != nil {
            return &InitializationResult{Success: false, ErrorMessage: err.Error()}, err
        }
    }

    return &InitializationResult{Success: true, Path: dbPath, Type: "db"}, nil
}
```

### 7.2 结果验证系统

#### 7.2.1 检查器接口设计
```go
type Checker interface {
    Check(ctx context.Context, item CheckItem) CheckResult
}

type CheckItem struct {
    Type         string `json:"type"`          // 检查类型
    Target       string `json:"target"`        // 检查目标
    Method       string `json:"method"`        // 检查方法：rule_judge, llm_judge
    PassRequired bool   `json:"pass_required"` // 是否必须通过
    Value        string `json:"value"`         // 期望值或描述
}

type CheckResult struct {
    Type         string `json:"type"`
    Target       string `json:"target"`
    Method       string `json:"method"`
    PassRequired bool   `json:"pass_required"`
    Value        string `json:"value"`
    ExcuteResult string `json:"excute_result"` // "success" or "failed"
    ErrMsg       string `json:"err_msg,omitempty"`
    TimeCostMs   int64  `json:"time_cost_ms"`
}
```

#### 7.2.2 规则检查器实现
```go
type RuleChecker struct{}

func (r *RuleChecker) Check(ctx context.Context, item CheckItem) CheckResult {
    result := CheckResult{
        Type:         item.Type,
        Target:       item.Target,
        Method:       item.Method,
        PassRequired: item.PassRequired,
        Value:        item.Value,
    }

    startTime := time.Now()
    defer func() {
        result.TimeCostMs = time.Since(startTime).Milliseconds()
    }()

    switch item.Type {
    case "check_file_exist":
        return r.checkFileExist(item)
    case "check_directory_exist":
        return r.checkDirectoryExist(item)
    case "check_file_content":
        return r.checkFileContent(item)
    default:
        return r.runShellCheck(item)
    }
}

func (r *RuleChecker) checkFileExist(item CheckItem) CheckResult {
    result := CheckResult{
        Type:         item.Type,
        Target:       item.Target,
        Method:       item.Method,
        PassRequired: item.PassRequired,
        Value:        item.Value,
    }

    if _, err := os.Stat(item.Target); err == nil {
        result.ExcuteResult = "success"
    } else {
        result.ExcuteResult = "failed"
        result.ErrMsg = fmt.Sprintf("文件不存在: %s", item.Target)
    }

    return result
}
```

#### 7.2.3 LLM检查器实现
```go
type LLMChecker struct {
    provider llm.Provider
}

func (l *LLMChecker) Check(ctx context.Context, item CheckItem) CheckResult {
    result := CheckResult{
        Type:         item.Type,
        Target:       item.Target,
        Method:       item.Method,
        PassRequired: item.PassRequired,
        Value:        item.Value,
    }

    startTime := time.Now()
    defer func() {
        result.TimeCostMs = time.Since(startTime).Milliseconds()
    }()

    switch item.Type {
    case "check_file_content":
        return l.checkFileContentByLLM(ctx, item)
    default:
        return l.checkByLLMJudgment(ctx, item)
    }
}

func (l *LLMChecker) checkFileContentByLLM(ctx context.Context, item CheckItem) CheckResult {
    result := CheckResult{
        Type:         item.Type,
        Target:       item.Target,
        Method:       item.Method,
        PassRequired: item.PassRequired,
        Value:        item.Value,
    }

    // 读取文件内容
    content, err := os.ReadFile(item.Target)
    if err != nil {
        result.ExcuteResult = "failed"
        result.ErrMsg = "无法读取文件: " + err.Error()
        return result
    }

    // 构造LLM提示词
    prompt := fmt.Sprintf(`请判断以下文件内容是否符合要求：

要求描述: %s

文件内容:
%s

请判断文件内容是否符合要求，只回答"是"或"否"，并简要说明理由。`, item.Value, string(content))

    // 调用LLM
    message, err := l.provider.CreateMessage(ctx, prompt, nil, nil)
    if err != nil {
        result.ExcuteResult = "failed"
        result.ErrMsg = "LLM检查失败: " + err.Error()
        return result
    }

    response := message.GetContent()

    // 判断LLM响应
    if strings.Contains(response, "是") || strings.Contains(response, "符合") || strings.Contains(response, "满足") {
        result.ExcuteResult = "success"
    } else {
        result.ExcuteResult = "failed"
        result.ErrMsg = "LLM判断不符合要求: " + response
    }

    return result
}
```

## 8. 扩展性设计

### 8.1 模型提供商扩展

#### 8.1.1 新增模型提供商步骤
1. **实现Provider接口**：
```go
type NewProvider struct {
    client     *http.Client
    apiKey     string
    baseURL    string
    model      string
    systemPrompt string
}

func (p *NewProvider) Name() string {
    return "new-provider"
}

func (p *NewProvider) CreateMessage(ctx context.Context, prompt string, messages []llm.Message, tools []llm.Tool) (llm.Message, error) {
    // 实现具体的模型调用逻辑
    // 1. 转换消息格式
    // 2. 构造API请求
    // 3. 发送HTTP请求
    // 4. 解析响应
    // 5. 返回标准Message接口
}

func (p *NewProvider) SupportsTools() bool {
    return true // 或false，取决于模型是否支持工具调用
}
```

2. **注册到工厂函数**：
```go
func createProvider(config ModelConfig) (llm.Provider, error) {
    switch config.ModelProvider {
    case "openai":
        return openai.NewProvider(config.OpenAIAPIKey, config.ModelName, config.OpenAIURL, systemPrompt)
    case "anthropic":
        return anthropic.NewProvider(config.AnthropicAPIKey, config.ModelName, config.AnthropicURL, systemPrompt)
    case "new-provider":
        return newprovider.NewProvider(config.NewProviderAPIKey, config.ModelName, config.NewProviderURL, systemPrompt)
    default:
        return nil, fmt.Errorf("不支持的模型提供者: %s", config.ModelProvider)
    }
}
```

3. **更新配置结构**：
```go
type ModelConfig struct {
    ModelName       string `json:"model_name"`
    ModelProvider   string `json:"model_provider"`
    // 新增提供商配置字段
    NewProviderAPIKey string `json:"new_provider_api_key,omitempty"`
    NewProviderURL    string `json:"new_provider_url,omitempty"`
}
```

### 8.2 工具服务器扩展

#### 8.2.1 STDIO模式工具扩展
```json
{
  "mcpServers": {
    "custom-tool": {
      "command": "python",
      "args": ["/path/to/custom_mcp_server.py"],
      "env": {
        "CUSTOM_API_KEY": "your-api-key"
      }
    }
  }
}
```

#### 8.2.2 SSE模式工具扩展
```json
{
  "mcpServers": {
    "custom-sse-tool": {
      "type": "sse",
      "url": "https://your-custom-mcp-server.com/sse"
    }
  }
}
```

#### 8.2.3 自定义MCP服务器开发
```python
# custom_mcp_server.py
import asyncio
import json
import sys
from mcp import Server, Tool

app = Server("custom-tool")

@app.tool("custom_function")
async def custom_function(param1: str, param2: int) -> str:
    """自定义工具函数"""
    # 实现具体的工具逻辑
    result = f"处理结果: {param1} - {param2}"
    return result

if __name__ == "__main__":
    asyncio.run(app.run())
```

### 8.3 评估策略扩展

#### 8.3.1 自定义检查器
```go
type CustomChecker struct {
    // 自定义配置
}

func (c *CustomChecker) Check(ctx context.Context, item CheckItem) CheckResult {
    result := CheckResult{
        Type:         item.Type,
        Target:       item.Target,
        Method:       item.Method,
        PassRequired: item.PassRequired,
        Value:        item.Value,
    }

    // 实现自定义检查逻辑
    switch item.Type {
    case "custom_check_type":
        // 自定义检查实现
        if customCheckLogic(item.Target, item.Value) {
            result.ExcuteResult = "success"
        } else {
            result.ExcuteResult = "failed"
            result.ErrMsg = "自定义检查失败"
        }
    }

    return result
}

// 注册自定义检查器
func init() {
    checkerManager := check.NewCheckerManager()
    checkerManager.RegisterChecker("custom_judge", &CustomChecker{})
}
```

#### 8.3.2 算子层面扩展
```python
class CustomOperator(OperatorBase):
    """自定义评估算子"""

    def _setup(self):
        """自定义初始化逻辑"""
        super()._setup()
        # 添加自定义初始化

    def _run(self):
        """自定义执行逻辑"""
        # 可以完全重写执行逻辑
        # 或者调用父类方法后添加自定义处理
        result = super()._run()

        # 自定义后处理
        self._custom_post_process(result)

        return result

    def _custom_post_process(self, result):
        """自定义后处理逻辑"""
        # 实现自定义的结果处理
        pass
```

### 8.4 配置模板扩展

#### 8.4.1 动态配置生成
```python
def generate_dynamic_config(template_path: str, variables: dict) -> dict:
    """根据模板和变量生成动态配置"""
    with open(template_path, 'r') as f:
        template = json.load(f)

    # 使用Jinja2或其他模板引擎
    from jinja2 import Template
    template_str = json.dumps(template)
    jinja_template = Template(template_str)

    rendered = jinja_template.render(**variables)
    return json.loads(rendered)
```

#### 8.4.2 配置验证器
```python
def validate_config(config: dict, schema_path: str) -> bool:
    """验证配置文件格式"""
    import jsonschema

    with open(schema_path, 'r') as f:
        schema = json.load(f)

    try:
        jsonschema.validate(config, schema)
        return True
    except jsonschema.ValidationError as e:
        logger.error(f"配置验证失败: {e}")
        return False
```

## 9. 监控和运维详设

### 9.1 日志体系设计

#### 9.1.1 结构化日志格式
```go
// 日志结构定义
type LogEntry struct {
    Timestamp    time.Time              `json:"timestamp"`
    Level        string                 `json:"level"`
    Message      string                 `json:"message"`
    Component    string                 `json:"component"`
    TaskID       string                 `json:"task_id,omitempty"`
    RequestID    string                 `json:"request_id,omitempty"`
    Duration     time.Duration          `json:"duration,omitempty"`
    Error        string                 `json:"error,omitempty"`
    Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// 使用示例
log.Info("工具调用开始",
    "tool_name", toolCall.GetName(),
    "arguments", string(input),
    "task_id", taskID)
```

#### 9.1.2 日志分层策略
- **DEBUG**：详细的调试信息，包括变量值、函数调用栈
- **INFO**：正常的业务流程信息，如任务开始、完成
- **WARN**：警告信息，如重试、降级操作
- **ERROR**：错误信息，如API调用失败、配置错误

#### 9.1.3 日志收集和存储
```yaml
# Fluent Bit配置示例
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         1
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf

    [INPUT]
        Name              tail
        Path              /var/log/containers/mcp-evaluation-*.log
        Parser            docker
        Tag               mcp.evaluation.*
        Refresh_Interval  5

    [OUTPUT]
        Name  es
        Match mcp.evaluation.*
        Host  elasticsearch.logging.svc.cluster.local
        Port  9200
        Index mcp-evaluation-logs
```

### 9.2 监控指标体系

#### 9.2.1 业务指标
```go
// Prometheus指标定义
var (
    taskTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "mcp_evaluation_tasks_total",
            Help: "Total number of evaluation tasks",
        },
        []string{"status", "model_provider"},
    )

    taskDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "mcp_evaluation_task_duration_seconds",
            Help: "Duration of evaluation tasks",
            Buckets: prometheus.DefBuckets,
        },
        []string{"model_provider"},
    )

    toolCallTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "mcp_tool_calls_total",
            Help: "Total number of tool calls",
        },
        []string{"tool_name", "status"},
    )

    tokenUsage = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "mcp_token_usage_total",
            Help: "Total token usage",
        },
        []string{"model_provider", "type"}, // type: prompt, completion
    )
)
```

#### 9.2.2 系统指标
- **CPU使用率**：容器和节点级别的CPU使用情况
- **内存使用率**：内存消耗和内存泄漏监控
- **网络I/O**：API调用的网络延迟和吞吐量
- **存储I/O**：文件系统操作的性能指标

#### 9.2.3 Grafana仪表板配置
```json
{
  "dashboard": {
    "title": "MCP评估平台监控",
    "panels": [
      {
        "title": "任务执行成功率",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(mcp_evaluation_tasks_total{status=\"success\"}[5m]) / rate(mcp_evaluation_tasks_total[5m]) * 100"
          }
        ]
      },
      {
        "title": "任务执行时长分布",
        "type": "histogram",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(mcp_evaluation_task_duration_seconds_bucket[5m]))"
          }
        ]
      },
      {
        "title": "Token消耗趋势",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(mcp_token_usage_total[5m])"
          }
        ]
      }
    ]
  }
}
```

### 9.3 错误处理和恢复

#### 9.3.1 重试机制实现
```go
// 指数退避重试
func retryWithBackoff(ctx context.Context, operation func() error, maxRetries int) error {
    var lastErr error

    for attempt := 0; attempt <= maxRetries; attempt++ {
        select {
        case <-ctx.Done():
            return fmt.Errorf("context cancelled: %w", ctx.Err())
        default:
        }

        if attempt > 0 {
            delay := time.Duration(math.Pow(2, float64(attempt-1))) * time.Second
            if delay > 60*time.Second {
                delay = 60 * time.Second
            }

            log.Debug("重试操作",
                "attempt", attempt,
                "delay", delay,
                "last_error", lastErr)

            timer := time.NewTimer(delay)
            select {
            case <-ctx.Done():
                timer.Stop()
                return fmt.Errorf("context cancelled during retry: %w", ctx.Err())
            case <-timer.C:
            }
        }

        if err := operation(); err != nil {
            lastErr = err
            if attempt == maxRetries {
                return fmt.Errorf("max retries (%d) exceeded, last error: %w", maxRetries, err)
            }
            continue
        }

        return nil
    }

    return lastErr
}
```

#### 9.3.2 熔断器模式
```go
type CircuitBreaker struct {
    maxFailures int
    timeout     time.Duration
    failures    int
    lastFailure time.Time
    state       string // "closed", "open", "half-open"
    mutex       sync.RWMutex
}

func (cb *CircuitBreaker) Call(operation func() error) error {
    cb.mutex.Lock()
    defer cb.mutex.Unlock()

    switch cb.state {
    case "open":
        if time.Since(cb.lastFailure) > cb.timeout {
            cb.state = "half-open"
            cb.failures = 0
        } else {
            return fmt.Errorf("circuit breaker is open")
        }
    }

    err := operation()
    if err != nil {
        cb.failures++
        cb.lastFailure = time.Now()

        if cb.failures >= cb.maxFailures {
            cb.state = "open"
        }

        return err
    }

    cb.failures = 0
    cb.state = "closed"
    return nil
}
```

#### 9.3.3 优雅降级策略
```go
// 降级配置
type FallbackConfig struct {
    EnableFallback     bool          `json:"enable_fallback"`
    FallbackProvider   string        `json:"fallback_provider"`
    FallbackTimeout    time.Duration `json:"fallback_timeout"`
    MaxFallbackRetries int           `json:"max_fallback_retries"`
}

// 降级处理
func (p *Provider) CreateMessageWithFallback(ctx context.Context, prompt string, messages []llm.Message, tools []llm.Tool) (llm.Message, error) {
    // 尝试主要提供商
    message, err := p.primaryProvider.CreateMessage(ctx, prompt, messages, tools)
    if err == nil {
        return message, nil
    }

    log.Warn("主要提供商调用失败，尝试降级",
        "primary_provider", p.primaryProvider.Name(),
        "error", err,
        "fallback_provider", p.fallbackProvider.Name())

    // 降级到备用提供商
    if p.fallbackProvider != nil {
        fallbackCtx, cancel := context.WithTimeout(ctx, p.config.FallbackTimeout)
        defer cancel()

        return p.fallbackProvider.CreateMessage(fallbackCtx, prompt, messages, tools)
    }

    return nil, fmt.Errorf("primary provider failed and no fallback available: %w", err)
}
```

## 10. 安全设计详设

### 10.1 API密钥安全管理

#### 10.1.1 密钥存储策略
```yaml
# Kubernetes Secret管理
apiVersion: v1
kind: Secret
metadata:
  name: llm-api-keys
  namespace: mcp-evaluation
type: Opaque
data:
  openai-api-key: <base64-encoded-key>
  anthropic-api-key: <base64-encoded-key>
  google-api-key: <base64-encoded-key>
  baidu-api-key: <base64-encoded-key>

---
# 密钥轮换Job
apiVersion: batch/v1
kind: CronJob
metadata:
  name: api-key-rotation
spec:
  schedule: "0 2 * * 0"  # 每周日凌晨2点
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: key-rotator
            image: key-rotation:latest
            command: ["/bin/sh", "-c"]
            args:
            - |
              # 从外部密钥管理系统获取新密钥
              NEW_OPENAI_KEY=$(fetch-new-key openai)
              NEW_ANTHROPIC_KEY=$(fetch-new-key anthropic)

              # 更新Kubernetes Secret
              kubectl patch secret llm-api-keys -p='{"data":{"openai-api-key":"'$(echo -n $NEW_OPENAI_KEY | base64)'"}}'
              kubectl patch secret llm-api-keys -p='{"data":{"anthropic-api-key":"'$(echo -n $NEW_ANTHROPIC_KEY | base64)'"}}'
```

#### 10.1.2 密钥使用安全
```go
// 密钥脱敏处理
func maskAPIKey(apiKey string) string {
    if len(apiKey) <= 8 {
        return "***"
    }
    return apiKey[:4] + "***" + apiKey[len(apiKey)-4:]
}

// 安全的HTTP客户端配置
func createSecureHTTPClient() *http.Client {
    transport := &http.Transport{
        TLSClientConfig: &tls.Config{
            MinVersion: tls.VersionTLS12,
            CipherSuites: []uint16{
                tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
                tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
                tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
            },
        },
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
    }

    return &http.Client{
        Transport: transport,
        Timeout:   30 * time.Second,
    }
}
```

### 10.2 容器安全

#### 10.2.1 容器镜像安全
```dockerfile
# 使用非root用户运行
FROM python:3.11-slim
RUN groupadd -r mcpuser && useradd -r -g mcpuser mcpuser
RUN mkdir -p /home/<USER>/home/<USER>
USER mcpuser
WORKDIR /home/<USER>

# 移除不必要的包和文件
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 设置只读文件系统
COPY --chown=mcpuser:mcpuser mcphost /home/<USER>/mcphost
RUN chmod +x /home/<USER>/mcphost
```

#### 10.2.2 Pod安全策略
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: mcp-evaluation-pod
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: mcp-container
    image: mcp/mcp-env:latest
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      capabilities:
        drop:
        - ALL
    resources:
      limits:
        memory: "4Gi"
        cpu: "2"
      requests:
        memory: "1Gi"
        cpu: "500m"
    volumeMounts:
    - name: tmp-volume
      mountPath: /tmp
    - name: var-tmp-volume
      mountPath: /var/tmp
  volumes:
  - name: tmp-volume
    emptyDir: {}
  - name: var-tmp-volume
    emptyDir: {}
```

### 10.3 网络安全

#### 10.3.1 网络策略配置
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mcp-evaluation-network-policy
  namespace: mcp-evaluation
spec:
  podSelector:
    matchLabels:
      app: mcp-evaluation
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from: []  # 禁止所有入站流量
  egress:
  - to: []    # 允许所有出站流量（访问LLM API）
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  - to:       # 允许DNS查询
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53
```

#### 10.3.2 TLS配置
```go
// TLS配置
func configureTLS() *tls.Config {
    return &tls.Config{
        MinVersion:               tls.VersionTLS12,
        PreferServerCipherSuites: true,
        CipherSuites: []uint16{
            tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
            tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
            tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
        },
        CurvePreferences: []tls.CurveID{
            tls.CurveP521,
            tls.CurveP384,
            tls.CurveP256,
        },
    }
}
```

### 10.4 数据安全

#### 10.4.1 敏感数据脱敏
```go
// 日志脱敏处理
type SensitiveDataMasker struct {
    patterns map[string]*regexp.Regexp
}

func NewSensitiveDataMasker() *SensitiveDataMasker {
    return &SensitiveDataMasker{
        patterns: map[string]*regexp.Regexp{
            "api_key":     regexp.MustCompile(`"api_key":\s*"([^"]+)"`),
            "password":    regexp.MustCompile(`"password":\s*"([^"]+)"`),
            "token":       regexp.MustCompile(`"token":\s*"([^"]+)"`),
            "secret":      regexp.MustCompile(`"secret":\s*"([^"]+)"`),
            "credit_card": regexp.MustCompile(`\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b`),
        },
    }
}

func (m *SensitiveDataMasker) MaskData(data string) string {
    result := data
    for name, pattern := range m.patterns {
        result = pattern.ReplaceAllStringFunc(result, func(match string) string {
            return pattern.ReplaceAllString(match, `"`+name+`": "***"`)
        })
    }
    return result
}
```

#### 10.4.2 数据清理策略
```yaml
# 数据清理CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: data-cleanup
  namespace: mcp-evaluation
spec:
  schedule: "0 1 * * *"  # 每天凌晨1点
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: cleanup
            image: alpine:latest
            command: ["/bin/sh", "-c"]
            args:
            - |
              # 清理超过7天的日志文件
              find /var/log/mcp-evaluation -name "*.log" -mtime +7 -delete

              # 清理临时文件
              find /tmp -name "mcp-*" -mtime +1 -delete

              # 清理已完成的评估数据
              find /data/evaluation-results -name "*.json" -mtime +30 -delete
            volumeMounts:
            - name: log-volume
              mountPath: /var/log/mcp-evaluation
            - name: data-volume
              mountPath: /data
          volumes:
          - name: log-volume
            hostPath:
              path: /var/log/mcp-evaluation
          - name: data-volume
            persistentVolumeClaim:
              claimName: evaluation-data-pvc
          restartPolicy: OnFailure
```

## 11. 性能优化详设

### 11.1 并发优化策略

#### 11.1.1 算子层面并发控制
```python
# operator/self_define_operator.py
import concurrent.futures
from threading import ThreadPoolExecutor

class Operator(OperatorBase):
    def _run(self):
        # 配置并发参数
        max_workers = min(self.params.get("max_concurrent_tasks", 5), 10)
        timeout_seconds = self.params.get("task_timeout", 300)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_task = {}
            for task_info in self.task_list:
                if self.execution_mode == "k8s":
                    future = executor.submit(self._execute_single_task_k8s, task_info)
                else:
                    future = executor.submit(self._execute_single_task_local, task_info)
                future_to_task[future] = task_info

            # 收集结果
            results = []
            for future in concurrent.futures.as_completed(future_to_task, timeout=timeout_seconds):
                task_info = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"任务执行失败: {task_info['task_id']}, 错误: {e}")
                    results.append({
                        "task_id": task_info["task_id"],
                        "success": False,
                        "error": str(e)
                    })

        return results
```

#### 11.1.2 HTTP客户端连接池优化
```go
// mcphost/pkg/llm/openai/provider.go
func createOptimizedHTTPClient() *http.Client {
    transport := &http.Transport{
        MaxIdleConns:        100,              // 最大空闲连接数
        MaxIdleConnsPerHost: 20,               // 每个主机最大空闲连接数
        MaxConnsPerHost:     50,               // 每个主机最大连接数
        IdleConnTimeout:     90 * time.Second, // 空闲连接超时
        TLSHandshakeTimeout: 10 * time.Second, // TLS握手超时
        ResponseHeaderTimeout: 30 * time.Second, // 响应头超时
        ExpectContinueTimeout: 1 * time.Second,  // Expect: 100-continue超时

        // 启用HTTP/2
        ForceAttemptHTTP2: true,

        // 连接复用
        DisableKeepAlives: false,
    }

    return &http.Client{
        Transport: transport,
        Timeout:   60 * time.Second,
    }
}
```

#### 11.1.3 异步工具调用处理
```go
// 异步工具调用处理
func (p *Provider) handleToolCallsAsync(ctx context.Context, toolCalls []ToolCall, mcpClients map[string]mcpclient.MCPClient) ([]ToolResult, error) {
    type toolCallJob struct {
        toolCall ToolCall
        result   chan ToolResult
        err      chan error
    }

    jobs := make([]toolCallJob, len(toolCalls))

    // 启动所有工具调用
    for i, toolCall := range toolCalls {
        jobs[i] = toolCallJob{
            toolCall: toolCall,
            result:   make(chan ToolResult, 1),
            err:      make(chan error, 1),
        }

        go func(job toolCallJob) {
            result, err := p.executeToolCall(ctx, job.toolCall, mcpClients)
            if err != nil {
                job.err <- err
            } else {
                job.result <- result
            }
        }(jobs[i])
    }

    // 收集结果
    results := make([]ToolResult, len(toolCalls))
    for i, job := range jobs {
        select {
        case result := <-job.result:
            results[i] = result
        case err := <-job.err:
            return nil, fmt.Errorf("tool call %d failed: %w", i, err)
        case <-ctx.Done():
            return nil, fmt.Errorf("context cancelled: %w", ctx.Err())
        }
    }

    return results, nil
}
```

### 11.2 资源优化策略

#### 11.2.1 内存优化
```go
// 内存池复用
var (
    bufferPool = sync.Pool{
        New: func() interface{} {
            return make([]byte, 0, 4096)
        },
    }

    stringBuilderPool = sync.Pool{
        New: func() interface{} {
            return &strings.Builder{}
        },
    }
)

func processLargeData(data []byte) string {
    // 从池中获取buffer
    buf := bufferPool.Get().([]byte)
    defer bufferPool.Put(buf[:0])

    // 从池中获取StringBuilder
    sb := stringBuilderPool.Get().(*strings.Builder)
    defer func() {
        sb.Reset()
        stringBuilderPool.Put(sb)
    }()

    // 处理数据
    // ...

    return sb.String()
}
```

#### 11.2.2 镜像优化
```dockerfile
# 多阶段构建优化
FROM golang:1.23-alpine AS go-builder
WORKDIR /app
# 只复制go.mod和go.sum，利用Docker层缓存
COPY mcphost/go.mod mcphost/go.sum ./
RUN go mod download
# 复制源代码
COPY mcphost/ .
# 静态编译，减小二进制文件大小
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-w -s' -o mcphost .

FROM node:20-alpine AS node-builder
# 只安装必要的包
RUN npm install -g --production \
    @modelcontextprotocol/server-filesystem@2025.3.28 \
    @playwright/mcp@0.0.27 \
    firecrawl-mcp@1.11.0 \
    && npm cache clean --force

# 最小化运行时镜像
FROM python:3.11-slim AS runtime
# 只安装必要的系统包
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 使用.dockerignore排除不必要的文件
# .dockerignore内容:
# .git
# .gitignore
# README.md
# docs/
# tests/
# *.log
# .DS_Store
```

#### 11.2.3 缓存策略
```go
// LRU缓存实现
type LRUCache struct {
    capacity int
    cache    map[string]*list.Element
    list     *list.List
    mutex    sync.RWMutex
}

type cacheItem struct {
    key   string
    value interface{}
}

func NewLRUCache(capacity int) *LRUCache {
    return &LRUCache{
        capacity: capacity,
        cache:    make(map[string]*list.Element),
        list:     list.New(),
    }
}

func (c *LRUCache) Get(key string) (interface{}, bool) {
    c.mutex.RLock()
    defer c.mutex.RUnlock()

    if elem, ok := c.cache[key]; ok {
        c.list.MoveToFront(elem)
        return elem.Value.(*cacheItem).value, true
    }
    return nil, false
}

func (c *LRUCache) Put(key string, value interface{}) {
    c.mutex.Lock()
    defer c.mutex.Unlock()

    if elem, ok := c.cache[key]; ok {
        c.list.MoveToFront(elem)
        elem.Value.(*cacheItem).value = value
        return
    }

    if c.list.Len() >= c.capacity {
        oldest := c.list.Back()
        if oldest != nil {
            c.list.Remove(oldest)
            delete(c.cache, oldest.Value.(*cacheItem).key)
        }
    }

    item := &cacheItem{key: key, value: value}
    elem := c.list.PushFront(item)
    c.cache[key] = elem
}

// 使用缓存优化配置加载
var configCache = NewLRUCache(100)

func loadConfigWithCache(configPath string) (*Config, error) {
    if cached, ok := configCache.Get(configPath); ok {
        return cached.(*Config), nil
    }

    config, err := loadConfigFromFile(configPath)
    if err != nil {
        return nil, err
    }

    configCache.Put(configPath, config)
    return config, nil
}
```

### 11.3 网络优化策略

#### 11.3.1 请求压缩和优化
```go
// HTTP请求压缩
func createCompressedRequest(method, url string, body []byte) (*http.Request, error) {
    var buf bytes.Buffer
    gzipWriter := gzip.NewWriter(&buf)

    if _, err := gzipWriter.Write(body); err != nil {
        return nil, err
    }

    if err := gzipWriter.Close(); err != nil {
        return nil, err
    }

    req, err := http.NewRequest(method, url, &buf)
    if err != nil {
        return nil, err
    }

    req.Header.Set("Content-Encoding", "gzip")
    req.Header.Set("Accept-Encoding", "gzip, deflate")
    req.Header.Set("Content-Type", "application/json")

    return req, nil
}

// 响应解压缩
func decompressResponse(resp *http.Response) ([]byte, error) {
    var reader io.ReadCloser

    switch resp.Header.Get("Content-Encoding") {
    case "gzip":
        var err error
        reader, err = gzip.NewReader(resp.Body)
        if err != nil {
            return nil, err
        }
        defer reader.Close()
    default:
        reader = resp.Body
    }

    return io.ReadAll(reader)
}
```

#### 11.3.2 批量请求优化
```go
// 批量API调用
type BatchRequest struct {
    Requests []APIRequest `json:"requests"`
}

type APIRequest struct {
    ID     string      `json:"id"`
    Method string      `json:"method"`
    URL    string      `json:"url"`
    Body   interface{} `json:"body"`
}

func (p *Provider) BatchCreateMessages(ctx context.Context, requests []MessageRequest) ([]MessageResponse, error) {
    // 将多个请求合并为批量请求
    batchReq := BatchRequest{
        Requests: make([]APIRequest, len(requests)),
    }

    for i, req := range requests {
        batchReq.Requests[i] = APIRequest{
            ID:     fmt.Sprintf("req_%d", i),
            Method: "POST",
            URL:    "/v1/chat/completions",
            Body:   req,
        }
    }

    // 发送批量请求
    resp, err := p.sendBatchRequest(ctx, batchReq)
    if err != nil {
        return nil, err
    }

    // 解析批量响应
    return p.parseBatchResponse(resp)
}
```

### 11.4 数据库和存储优化

#### 11.4.1 SQLite优化配置
```go
// SQLite性能优化
func optimizeSQLiteConnection(db *sql.DB) error {
    pragmas := []string{
        "PRAGMA journal_mode = WAL",           // 使用WAL模式提高并发性能
        "PRAGMA synchronous = NORMAL",         // 平衡安全性和性能
        "PRAGMA cache_size = 10000",           // 增加缓存大小
        "PRAGMA temp_store = memory",          // 临时表存储在内存中
        "PRAGMA mmap_size = 268435456",        // 256MB内存映射
        "PRAGMA optimize",                     // 优化查询计划
    }

    for _, pragma := range pragmas {
        if _, err := db.Exec(pragma); err != nil {
            return fmt.Errorf("failed to execute pragma %s: %w", pragma, err)
        }
    }

    return nil
}
```

#### 11.4.2 文件I/O优化
```go
// 批量文件操作
func writeBatchFiles(files map[string][]byte) error {
    // 使用goroutine池并发写入文件
    const maxWorkers = 10
    semaphore := make(chan struct{}, maxWorkers)

    var wg sync.WaitGroup
    var mu sync.Mutex
    var errors []error

    for path, content := range files {
        wg.Add(1)
        go func(path string, content []byte) {
            defer wg.Done()

            semaphore <- struct{}{}
            defer func() { <-semaphore }()

            if err := os.WriteFile(path, content, 0644); err != nil {
                mu.Lock()
                errors = append(errors, fmt.Errorf("failed to write %s: %w", path, err))
                mu.Unlock()
            }
        }(path, content)
    }

    wg.Wait()

    if len(errors) > 0 {
        return fmt.Errorf("batch write failed: %v", errors)
    }

    return nil
}
```

## 12. 未来演进规划

### 12.1 技术演进路线图

#### 12.1.1 短期目标（3-6个月）
- **性能优化**：完善并发控制和资源管理
- **监控完善**：建立完整的监控和告警体系
- **文档完善**：补充API文档和运维手册
- **测试覆盖**：提高单元测试和集成测试覆盖率

#### 12.1.2 中期目标（6-12个月）
- **微服务拆分**：将单体应用拆分为更细粒度的微服务
- **事件驱动架构**：引入消息队列实现异步处理
- **多云部署**：支持多云环境的部署和管理
- **AI增强评估**：集成AI能力进行智能化评估分析

#### 12.1.3 长期目标（1-2年）
- **平台化建设**：构建一站式AI评估平台
- **生态建设**：建立开发者社区和插件生态
- **标准制定**：参与行业标准的制定和推广
- **商业化**：探索商业化模式和产品化路径

### 12.2 功能扩展计划

#### 12.2.1 Web管理界面
```typescript
// 前端技术栈规划
interface WebUIArchitecture {
  frontend: {
    framework: "React 18 + TypeScript";
    stateManagement: "Redux Toolkit";
    uiLibrary: "Ant Design";
    buildTool: "Vite";
  };
  backend: {
    framework: "Go Gin + GORM";
    database: "PostgreSQL";
    cache: "Redis";
    messageQueue: "RabbitMQ";
  };
  features: [
    "任务配置和管理",
    "实时监控和日志查看",
    "结果分析和报告生成",
    "用户权限管理",
    "系统配置管理"
  ];
}
```

#### 12.2.2 智能化功能
```python
# AI增强评估功能
class AIEnhancedEvaluator:
    def __init__(self):
        self.test_case_generator = TestCaseGenerator()
        self.result_analyzer = ResultAnalyzer()
        self.optimization_advisor = OptimizationAdvisor()

    def generate_test_cases(self, scenario: str, difficulty: str) -> List[TestCase]:
        """基于场景和难度自动生成测试用例"""
        prompt = f"""
        基于以下场景生成{difficulty}难度的测试用例：
        场景：{scenario}

        请生成包含以下要素的测试用例：
        1. 环境初始化要求
        2. 具体的查询内容
        3. 期望的工具调用序列
        4. 验证规则
        """

        # 调用大模型生成测试用例
        generated_cases = self.llm.generate(prompt)
        return self.parse_test_cases(generated_cases)

    def analyze_results(self, results: List[EvaluationResult]) -> AnalysisReport:
        """智能分析评估结果"""
        analysis = {
            "success_rate": self.calculate_success_rate(results),
            "common_failures": self.identify_common_failures(results),
            "performance_bottlenecks": self.identify_bottlenecks(results),
            "improvement_suggestions": self.generate_suggestions(results)
        }

        return AnalysisReport(analysis)
```

#### 12.2.3 多模态评估支持
```go
// 多模态评估架构
type MultimodalEvaluator struct {
    textEvaluator  TextEvaluator
    imageEvaluator ImageEvaluator
    audioEvaluator AudioEvaluator
    videoEvaluator VideoEvaluator
}

func (m *MultimodalEvaluator) EvaluateMultimodal(ctx context.Context, request MultimodalRequest) (*EvaluationResult, error) {
    var results []ModalityResult

    // 并行处理不同模态
    var wg sync.WaitGroup
    resultChan := make(chan ModalityResult, len(request.Modalities))

    for _, modality := range request.Modalities {
        wg.Add(1)
        go func(mod Modality) {
            defer wg.Done()

            var result ModalityResult
            switch mod.Type {
            case "text":
                result = m.textEvaluator.Evaluate(ctx, mod.Data)
            case "image":
                result = m.imageEvaluator.Evaluate(ctx, mod.Data)
            case "audio":
                result = m.audioEvaluator.Evaluate(ctx, mod.Data)
            case "video":
                result = m.videoEvaluator.Evaluate(ctx, mod.Data)
            }

            resultChan <- result
        }(modality)
    }

    wg.Wait()
    close(resultChan)

    // 收集结果
    for result := range resultChan {
        results = append(results, result)
    }

    // 融合多模态结果
    return m.fuseResults(results), nil
}
```

### 12.3 生态建设规划

#### 12.3.1 开源社区建设
```yaml
# 开源项目结构
opensource_ecosystem:
  core_projects:
    - name: "mcp-evaluation-core"
      description: "核心评估引擎"
      license: "Apache 2.0"

    - name: "mcp-tools-collection"
      description: "MCP工具集合"
      license: "MIT"

    - name: "mcp-evaluation-ui"
      description: "Web管理界面"
      license: "Apache 2.0"

  community_features:
    - "插件市场"
    - "模板库"
    - "最佳实践分享"
    - "技术博客"
    - "开发者论坛"

  contribution_guidelines:
    - "代码贡献指南"
    - "文档贡献指南"
    - "Bug报告模板"
    - "功能请求模板"
```

#### 12.3.2 标准化推进
```markdown
# MCP评估标准制定计划

## 评估协议标准
- 定义统一的评估任务描述格式
- 制定标准的结果输出格式
- 建立评估指标的标准化体系

## 工具接入标准
- MCP服务器开发规范
- 工具调用接口标准
- 错误处理和重试机制标准

## 安全和隐私标准
- 数据处理安全规范
- API密钥管理标准
- 隐私保护最佳实践

## 性能基准标准
- 评估任务性能基准
- 资源使用效率标准
- 可扩展性评估标准
```

### 12.4 商业化探索

#### 12.4.1 产品化路径
```mermaid
graph TB
    subgraph "开源版本"
        OSS[开源核心功能]
        Community[社区支持]
        BasicFeatures[基础功能]
    end

    subgraph "企业版本"
        Enterprise[企业级功能]
        Support[专业支持]
        Advanced[高级功能]
    end

    subgraph "云服务版本"
        SaaS[SaaS服务]
        API[API服务]
        Managed[托管服务]
    end

    OSS --> Enterprise
    Enterprise --> SaaS
    Community --> Support
    BasicFeatures --> Advanced
    Advanced --> API
```

#### 12.4.2 服务模式
- **开源免费版**：核心功能开源，社区支持
- **企业版**：增值功能、专业支持、SLA保障
- **云服务版**：SaaS模式、按需付费、托管服务
- **咨询服务**：定制开发、技术咨询、培训服务

---

## 总结

本架构方案详设为MCP评估平台提供了完整的技术架构蓝图，涵盖了从系统设计到实施部署的各个方面。该方案具有以下特点：

### 核心优势
1. **标准化**：基于MCP协议实现工具调用的标准化
2. **可扩展**：模块化设计支持快速扩展新功能
3. **高性能**：多层次的性能优化策略
4. **安全可靠**：完善的安全机制和错误处理
5. **云原生**：基于Kubernetes的容器化部署

### 技术创新
1. **混合架构**：Go语言高性能服务 + Python算子引擎
2. **协议抽象**：统一的LLM提供商接口
3. **环境隔离**：容器化的评估环境
4. **智能验证**：规则检查 + LLM检查的混合验证

### 实施价值
1. **提升效率**：自动化的评估流程大幅提升评估效率
2. **保证质量**：标准化的评估环境确保结果一致性
3. **降低成本**：容器化部署和资源优化降低运营成本
4. **促进创新**：开放的架构促进AI评估技术创新

### 开源生态价值
本平台的部分组件基于开源MCP工具生态构建，同时也为社区贡献了创新的评估框架设计。通过开放核心组件和标准化接口，促进了AI评估领域的技术交流和协作发展。

### 技术债务管理
在快速迭代过程中，我们始终关注技术债务的管理：
- **代码质量**：通过代码审查和自动化测试保证代码质量
- **架构演进**：定期进行架构评审和重构
- **文档维护**：保持文档与代码的同步更新
- **性能监控**：持续监控系统性能并及时优化

该架构方案为MCP评估平台的长期发展奠定了坚实的技术基础，既满足了当前的业务需求，又为未来的技术演进预留了充分的扩展空间。通过持续的技术创新和生态建设，该平台将成为AI评估领域的重要基础设施。