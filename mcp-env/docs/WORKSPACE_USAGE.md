# MCP 工作空间和输入目录功能说明

## 功能概述

新增了工作空间管理和输入目录支持，允许用户：
1. 自动将所有输入文件复制到工作空间
2. 通过指定目录批量添加相关文件
3. 支持自定义 MCP 服务器脚本

## 支持的文件类型

### 自动识别的文件类型：
- **JSONL 文件**: `*.jsonl` - 测试数据文件
- **MCP 服务器配置**: `*mcpservers*.json` - 自定义 MCP 服务器配置
- **检查脚本**: `*check*.py` - 结果检查脚本
- **MCP 服务器脚本**: `*mock_mcp*.py` 或 `*mcp_server*.py` - 自定义 MCP 服务器实现
- **其他文件**: 任何其他相关文件

## 使用方法

### 1. 输入目录方式（推荐）

```bash
# 指定包含所有相关文件的目录
INPUT_FILE='/path/to/test.jsonl' \
INPUT_DIR='/path/to/mcp/scripts' \
MODELS='uni_crawl:deepseek-v3' \
MAX_WORKERS=4 \
make run-mcp-eval
```

目录结构示例：
```
/path/to/mcp/scripts/
├── mock_mcp_01.py          # MCP 服务器脚本
├── mock_mcp_filesystem.py  # 文件系统 MCP 服务器
├── mcpservers.json         # 自定义配置
├── check.py                # 检查脚本
└── utils.py                # 其他支持文件
```

### 2. 单独指定文件方式

```bash
# 分别指定各种文件
INPUT_FILE='/path/to/test.jsonl' \
MCP_SERVERS='/path/to/mcpservers.json' \
CHECK_SCRIPT='/path/to/check.py' \
MODELS='uni_crawl:deepseek-v3' \
MAX_WORKERS=4 \
make run-mcp-eval
```

## 自定义 MCP 服务器配置示例

### mcpservers.json 配置
```json
{
  "mock_server_01": {
    "command": "uv",
    "args": [
      "run",
      "${DATAENG_INPUT_DATA_DIR}/mock_mcp_01.py"
    ]
  },
  "custom_filesystem": {
    "command": "python",
    "args": [
      "${DATAENG_INPUT_DATA_DIR}/mock_mcp_filesystem.py",
      "--workspace",
      "./workspace"
    ]
  },
  "sqlite_custom": {
    "command": "uvx",
    "args": [
      "mcp-server-sqlite==2025.4.25",
      "--db-path",
      "${DATAENG_INPUT_DATA_DIR}/custom.db"
    ]
  }
}
```

### MCP 服务器脚本示例 (mock_mcp_01.py)
```python
#!/usr/bin/env python3
"""
自定义 MCP 服务器示例
"""
import asyncio
import json
from mcp.server import Server
from mcp.types import Tool, TextContent

app = Server("mock-server-01")

@app.list_tools()
async def list_tools():
    return [
        Tool(
            name="custom_tool",
            description="自定义工具示例",
            inputSchema={
                "type": "object",
                "properties": {
                    "message": {"type": "string"}
                }
            }
        )
    ]

@app.call_tool()
async def call_tool(name: str, arguments: dict):
    if name == "custom_tool":
        message = arguments.get("message", "Hello")
        return [TextContent(type="text", text=f"自定义响应: {message}")]
    
    raise ValueError(f"Unknown tool: {name}")

if __name__ == "__main__":
    asyncio.run(app.run())
```

## 工作空间机制

### 文件复制流程
1. **自动识别**: 系统自动识别输入文件类型
2. **工作空间创建**: 在输出目录下创建 `tmp/workspace/` 目录
3. **文件复制**: 将所有相关文件复制到工作空间
4. **路径映射**: K8s 运行时，`${DATAENG_INPUT_DATA_DIR}` 指向包含这些文件的目录

### 环境变量说明
- `${DATAENG_INPUT_DATA_DIR}`: 输入数据目录
  - **本地执行**: 自动设置为 `tmp/workspace/` 目录的绝对路径
  - **K8s执行**: 容器内的输入数据目录，由K8s环境自动提供
- `${DATAENG_OUTPUT_DIR}`: K8s 容器内的输出目录（仅K8s执行时可用）

#### 环境变量使用示例
在本地执行时，系统会自动设置环境变量：
```bash
DATAENG_INPUT_DATA_DIR=/path/to/output/tmp/workspace
```

在K8s执行时，环境变量由容器运行时提供：
```bash
DATAENG_INPUT_DATA_DIR=/input  # 容器内路径
DATAENG_OUTPUT_DIR=/output     # 容器内路径
```

## 最佳实践

### 1. 目录组织
```
project/
├── data/
│   ├── test_data.jsonl      # 测试数据
│   └── results.jsonl        # 历史结果（重试用）
├── scripts/
│   ├── mock_mcp_01.py       # 自定义 MCP 服务器
│   ├── mock_mcp_02.py
│   ├── mcpservers.json      # MCP 配置
│   └── check.py             # 检查脚本
└── configs/
    └── model_config.json    # 模型配置
```

### 2. 开发流程
1. **开发阶段**: 在本地测试自定义 MCP 服务器
2. **配置阶段**: 编写 mcpservers.json 配置
3. **测试阶段**: 使用 INPUT_DIR 批量加载文件
4. **生产阶段**: K8s 环境自动部署

### 3. 错误排查
- 检查工作空间日志: `operator/output/tmp/logs/`
- 验证文件复制: `operator/output/tmp/workspace/`
- 查看配置生成: `operator/output/tmp/configs/`

## 注意事项

1. **文件名冲突**: 相同文件名会被覆盖，建议使用唯一命名
2. **权限问题**: 确保 Python 脚本有执行权限
3. **依赖管理**: MCP 服务器脚本的依赖需要在容器中可用
4. **路径引用**: 在配置中使用 `${DATAENG_INPUT_DATA_DIR}` 引用文件

这种设计让 MCP 工具使用更加灵活和强大！ 