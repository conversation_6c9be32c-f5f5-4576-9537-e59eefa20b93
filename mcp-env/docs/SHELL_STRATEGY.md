# Shell Strategy Documentation

The Shell Strategy (`sh`) allows you to execute shell scripts as part of environment initialization. This strategy creates temporary script files, executes them, and automatically cleans up the files after execution.

## Features

- **Type name**: `sh`
- **Script execution**: Supports both default and custom execution commands
- **Automatic cleanup**: Temporary script files are automatically deleted after execution
- **Flexible configuration**: Supports both plain text and JSON configuration formats
- **Security**: Path validation prevents directory traversal attacks

## Usage

### Basic Usage (Plain Text)

```go
dep := EnvironmentDependency{
    Path:    "setup.sh",
    Type:    "sh", 
    Content: "#!/bin/bash\necho 'Hello World'\nmkdir -p data",
}
```

### Advanced Usage (JSON Configuration)

```go
config := map[string]interface{}{
    "content": "#!/bin/bash\necho \"Args: $@\"",
    "cmd":     "bash setup.sh arg1 arg2",
}
configJSON, _ := json.Marshal(config)

dep := EnvironmentDependency{
    Path:    "setup.sh",
    Type:    "sh",
    Content: string(configJSON),
}
```

## Configuration Fields

### Required Fields

- **`path`**: The name of the shell script file (e.g., "setup.sh")
- **`content`**: The actual shell script content as a string

### Optional Fields (JSON format only)

- **`cmd`**: Custom execution command (e.g., "bash setup.sh --verbose")

## Execution Behavior

1. **Default execution**: If no `cmd` is specified, the script is executed using `bash {path}`
2. **Custom execution**: If `cmd` is specified, that exact command is used
3. **Cleanup**: The temporary script file is automatically deleted after execution completes

## Examples

### Example 1: Simple Script

```go
manager := DefaultManager()

dep := EnvironmentDependency{
    Path:    "hello.sh",
    Type:    "sh",
    Content: "#!/bin/bash\necho 'Hello from shell script!'",
}

results, err := manager.Initialize(context.Background(), []EnvironmentDependency{dep})
```

### Example 2: Script with Custom Command

```go
config := ShellConfig{
    Content: "#!/bin/bash\necho \"Processing: $1\"",
    Cmd:     "bash process.sh data.txt",
}
configJSON, _ := json.Marshal(config)

dep := EnvironmentDependency{
    Path:    "process.sh", 
    Type:    "sh",
    Content: string(configJSON),
}

results, err := manager.Initialize(context.Background(), []EnvironmentDependency{dep})
```

### Example 3: Environment Setup Script

```go
setupScript := `#!/bin/bash
# Create directory structure
mkdir -p logs temp data

# Set permissions
chmod 755 logs temp data

# Create config file
cat > config.ini << EOF
[settings]
debug=true
port=8080
EOF

echo "Environment setup complete"
`

dep := EnvironmentDependency{
    Path:    "setup_env.sh",
    Type:    "sh", 
    Content: setupScript,
}
```

## Security Features

- **Path validation**: Prevents directory traversal attacks (e.g., "../../../etc/passwd")
- **Relative paths**: All paths are converted to relative paths for safety
- **Automatic cleanup**: Temporary files are always cleaned up, even on execution failure

## Error Handling

The strategy provides detailed error information including:
- Script execution errors with exit codes
- Command output (both stdout and stderr)
- Execution timing information
- Cleanup status

## Integration

The Shell Strategy is automatically registered with the `DefaultManager()` and is available alongside other environment strategies like file, directory, database, URL, and binary strategies.

```go
// Shell strategy is included by default
manager := DefaultManager()

// Or register manually
manager := NewManager()
manager.RegisterStrategy(NewShellStrategy())
```
