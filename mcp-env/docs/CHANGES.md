# MCPHost 重构变更说明

## 概述

本次重构主要完成了以下几个方面的改进：

1. **移除旧格式兼容** - 统一使用最新的 bench.json 格式
2. **实现检查策略模式** - 为不同的检查类型和方法实现策略模式
3. **修复工具调用记录** - 正确实现 tool_call_list_real 字段的输出
4. **遵循result.json格式** - 确保输出格式符合预期

## 主要变更

### 1. 移除旧格式兼容

**修改文件:** `mcphost/cmd/mcp.go`

- 移除了 `BenchConfig` 结构中的旧字段：
  - `Sence`, `Query`, `Queries` 
  - `ServerName`, `InputDependcy`
  - `APICallList`, `Thoughts`, `OutputPath`
  - `CheckList`, `HardDegree`
- 简化了 `GetQuery()` 方法，统一使用 `ExtraInfo.Query`
- 移除了 `GetQueries()` 和 `IsMultiRound()` 方法

### 2. 检查策略模式实现

**新增文件:**
- `mcphost/pkg/check/checker.go` - 检查器接口和管理器
- `mcphost/pkg/check/rule_checker.go` - 规则检查器实现
- `mcphost/pkg/check/llm_checker.go` - LLM检查器实现

**特性:**
- 支持 `rule_judge` 和 `llm_judge` 两种检查方法
- 支持多种检查类型：
  - `check_file_exist` - 文件存在性检查
  - `check_directory_exist` - 目录存在性检查  
  - `check_file_content` - 文件内容检查
  - `check_tool_call_list` - 工具调用列表检查
- 可扩展的策略注册机制

### 3. 工具调用记录修复

**修改文件:** `mcphost/cmd/root.go`

- 重写了 `runPromptWithToolRecording()` 函数
- 正确记录每个工具调用的详细信息：
  - 工具名称 (`name`)
  - 调用参数 (`arguments`) 
  - 工具输出 (`tool_content`)
  - 耗时 (`time_cost_ms`)
- 支持错误情况的记录

### 4. 自定义输出文件路径

**修改文件:** `mcphost/cmd/root.go`

- 新增 `--output` / `-o` 命令行参数
- 支持指定自定义的结果输出文件路径
- 如果未指定，默认使用 `bench_result.json`
- 在日志中显示实际保存的文件路径
- **修复目录创建问题**: 在写入文件前自动创建必要的目录结构
- **改进错误处理**: 即使API调用失败也能保存包含错误信息的结果文件

### 5. boost.sh 脚本日志功能增强

**修改文件:** `mcphost/boost.sh`

- 新增自动日志记录功能
- 日志文件命名格式：`日期_UUID.log`（如：`20250529_d7393f3bcba24d7094ba4fd058ef16b1.log`）
- 自动创建 `logs/` 目录
- 使用 `tee` 命令同时输出到终端和日志文件
- 捕获所有标准输出和错误输出（`2>&1`）
- 提供执行前后的状态提示

### 6. 输出格式标准化

确保输出符合 `result.json` 格式：

```json
{
    "response": "模型的最终响应",
    "tool_call_list_real": [
        {
            "name": "工具名称",
            "arguments": "工具参数JSON字符串",
            "tool_content": "工具执行结果",
            "time_cost_ms": 150
        }
    ],
    "check_list_real": [
        {
            "type": "检查类型",
            "target": "检查目标",
            "method": "检查方法",
            "pass_required": true,
            "value": "检查描述",
            "time_cost_ms": 25,
            "excute_result": "success",
            "err_msg": ""
        }
    ],
    "total_steps": 1,
    "total_time_cost_ms": 2500
}
```

## 使用示例

### 新格式的 bench.json

```json
{
  "data_id": 14,
  "type": "static",
  "tags": ["agent", "单轮"],
  "system": "你是一个有用的助手",
  "extra_info": {
    "query": "创建一个网站项目结构",
    "check_list": [
      {
        "type": "check_file_exist",
        "target": "./output/index.html",
        "method": "rule_judge",
        "pass_required": true,
        "value": "HTML文件应该存在"
      },
      {
        "type": "check_file_content",
        "target": "./output/index.html",
        "method": "llm_judge", 
        "pass_required": true,
        "value": "应包含基本的HTML5结构"
      }
    ]
  }
}
```

### 运行命令

```bash
# 基本用法（输出到默认文件 bench_result.json）
./mcphost --config model_config.json --mcpservers mcp_config.json --bench bench.json

# 指定自定义输出文件路径
./mcphost --config model_config.json --mcpservers mcp_config.json --bench bench.json -o custom_result.json

# 输出到子目录
./mcphost --config model_config.json --mcpservers mcp_config.json --bench bench.json -o output/results/my_test.json

# 使用 boost.sh 脚本（自动日志记录）
./boost.sh
```

### boost.sh 脚本使用

```bash
# 运行脚本，自动生成日志文件
./boost.sh

# 查看日志文件
ls logs/
cat logs/20250529_d7393f3bcba24d7094ba4fd058ef16b1.log
```

## 向后兼容性

⚠️ **重要:** 本次重构移除了对旧格式的兼容性支持。请确保：

1. 所有的 bench.json 文件都使用新格式
2. 查询内容统一放在 `extra_info.query` 字段
3. 检查列表使用新的 `check_list` 格式

## 测试

使用提供的 `test_new_format.sh` 脚本进行功能验证：

```bash
chmod +x test_new_format.sh
./test_new_format.sh
```

## 受益

1. **代码简化** - 移除了大量兼容性代码
2. **功能增强** - 支持更灵活的检查策略
3. **输出标准** - 统一的结果格式
4. **可维护性** - 清晰的模块划分和接口设计
5. **灵活性** - 可自定义输出文件路径
6. **自动目录创建** - 智能处理输出路径的目录结构
7. **错误容错** - 即使API失败也能保存结果记录
8. **日志管理** - 自动生成唯一的日志文件，便于追踪和调试