# Assistant API Provider 使用说明

## 概述

Assistant API Provider允许MCPHost通过百度的Assistant API（千帆和一言）来调用AI模型。这个provider支持基于assistant和thread的对话模式，适用于需要使用百度内部Assistant API的场景。

## 支持的Backend

- **qianfan**: 千帆Assistant API
- **yiyan**: 一言Assistant API

## 配置方式

Assistant API Provider只能通过配置文件使用，不支持命令行直接配置。

### 千帆配置示例

```json
{
  "model_name": "ERNIE-3.5-8K",
  "model_provider": "assistant_api",
  "backend": "qianfan",
  "assistant_id": "asst_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "authorization": "your_authorization_token_here",
  "global_timeout": 300
}
```

### 一言配置示例

```json
{
  "model_name": "EBX1",
  "model_provider": "assistant_api",
  "backend": "yiyan",
  "assistant_id": "asst_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "authorization": "your_authorization_token_here",
  "app_id": "123456",
  "global_timeout": 300
}
```

## 配置参数说明

| 参数 | 必填 | 说明 |
|------|------|------|
| `model_name` | 是 | 模型名称，如ERNIE-3.5-8K、EBX1等 |
| `model_provider` | 是 | 固定值："assistant_api" |
| `backend` | 否 | backend类型，"qianfan"或"yiyan"，默认为"qianfan" |
| `assistant_id` | 是 | Assistant ID，从Assistant API创建助手后获得 |
| `authorization` | 是 | 认证token |
| `app_id` | 一言必填 | 应用ID，仅一言backend需要 |
| `global_timeout` | 否 | 全局超时时间（秒），默认300 |

## 使用方式

1. 创建模型配置文件（如上述示例）
2. 创建MCP服务器配置文件
3. 创建测试配置文件
4. 运行mcphost：

```bash
./mcphost --config model.json --mcpservers mcp.json --bench bench.json
```

## 特性

- ✅ 支持基础对话
- ✅ 支持系统提示词
- ✅ 支持工具调用（通过Assistant API的function calling）
- ✅ 自动线程管理
- ✅ 运行状态监控
- ❌ 不支持推理内容输出（ReasoningContent）
- ❌ 不支持实时token统计

## 工具调用

Assistant API Provider支持工具调用，但工具的执行仍然由MCPHost处理。当Assistant API请求工具调用时，provider会：

1. 获取工具调用请求
2. 返回默认的工具响应（实际工具调用由外部MCP服务器处理）
3. 继续对话流程

## 注意事项

1. Assistant API Provider需要预先创建的Assistant ID
2. 不同backend需要不同的认证信息
3. 工具调用的实际执行仍依赖MCP服务器
4. 目前不支持流式响应
5. 线程在provider实例中自动管理，每次创建新provider会创建新线程

## 错误处理

常见错误及解决方案：

- `assistant_api需要配置assistant_id`: 检查配置文件中是否包含assistant_id字段
- `assistant_api需要配置authorization`: 检查认证token是否正确配置
- `yiyan backend需要配置app_id`: 使用一言时必须配置app_id
- `创建thread失败`: 检查认证信息和网络连接
- `run执行超时`: 增加global_timeout值或检查Assistant API服务状态 