# MCP评估算子使用说明

## 算子能力介绍

MCP评估算子是一个专为大模型Agent能力评估设计的自动化工具，基于Model Context Protocol（MCP）协议实现标准化的工具调用评估。

### 核心能力

- **多模型支持**：同时评估多个大模型（OpenAI、Anthropic、Google、百度文心等）
- **标准化工具调用**：基于MCP协议统一不同工具的调用接口
- **自动化环境初始化**：支持文件系统、数据库等环境的自动准备
- **智能结果验证**：结合规则检查和LLM检查的混合验证机制
- **并发执行**：支持多任务并发处理，提高评估效率
- **灵活部署**：支持本地执行和Kubernetes集群执行两种模式

### 支持的工具类型

| 工具类型 | 功能描述 | 配置名称 |
|---------|---------|---------|
| **文件系统** | 文件和目录操作 | `filesystem` |
| **数据库** | SQLite数据库操作 | `sqlite` |
| **网页自动化** | 浏览器自动化操作 | `playwright` |
| **网页抓取** | 网页内容抓取 | `firecrawl-mcp` |
| **地图服务** | 地图查询和导航 | `amap-maps` |

## 输入要求

### 必需输入文件

#### 1. JSONL数据文件（必需）
包含评估任务的数据文件，每行一个JSON对象。

#### 2. mcpservers.json（可选）
自定义MCP服务器配置文件，用于覆盖默认配置。

### 算子参数

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|-------|------|------|-------|------|
| `models` | 数组/字符串 | 是 | - | 要评估的模型列表，支持 `provider:model` 格式 |
| `max_workers` | 整数 | 否 | 4 | 并发执行的最大任务数 |
| `run_local` | 布尔值 | 否 | false | 是否本地执行（true=本地，false=K8s） |

### 参数配置示例

```json
{
  "models": [
    "openai:gpt-4o-mini",
    "anthropic:claude-3-sonnet",
    "assistant_api:ERNIE-4.5-single-custom-yiyanweb"
  ],
  "max_workers": 8,
  "run_local": false
}
```

## 输入文件Schema说明

### JSONL数据文件格式

每行必须是一个完整的JSON对象，包含以下字段：

```json
{
  "data_id": 1,
  "type": "static",
  "tags": ["filesystem", "单轮"],
  "ground_truth": "期望的结果描述",
  "reference": "参考信息",
  "system": "你是一个有用的助手，擅长调用工具解决问题。",
  "extra_info": {
    "index": 1,
    "sub_scenario": "文件操作测试",
    "difficulty_level": "easy",
    "query": "请创建一个名为test.txt的文件，内容为'Hello World'",
    "server_name": ["filesystem", "sqlite"],
    "environment_dependency": [
      {
        "path": "./test_output/",
        "type": "directory",
        "content": ""
      }
    ],
    "check_list": [
      {
        "type": "check_file_exist",
        "target": "./test_output/test.txt",
        "method": "rule_judge",
        "pass_required": true,
        "value": "文件应该存在"
      }
    ],
    "mcpservers": {
      "filesystem": {
        "command": "npx",
        "args": ["-y", "@modelcontextprotocol/server-filesystem", "./"],
        "env": {}
      }
    }
  }
}
```

#### 字段说明

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `data_id` | 整数 | 是 | 数据唯一标识符 |
| `type` | 字符串 | 否 | 任务类型（如"static"） |
| `tags` | 数组 | 否 | 任务标签 |
| `ground_truth` | 字符串 | 否 | 期望结果描述 |
| `reference` | 字符串 | 否 | 参考信息 |
| `system` | 字符串 | 否 | 系统提示词 |
| `extra_info` | 对象 | 是 | 扩展信息（详见下表） |

#### extra_info字段详解

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `query` | 字符串 | 是 | 用户查询内容 |
| `server_name` | 数组 | 是 | 需要使用的MCP服务器列表 |
| `environment_dependency` | 数组 | 否 | 环境初始化依赖 |
| `check_list` | 数组 | 否 | 结果验证规则 |
| `mcpservers` | 对象 | 否 | 自定义MCP服务器配置（最高优先级） |

### 环境依赖配置详解

`environment_dependency` 用于在评估任务执行前自动初始化所需的环境，支持文件系统、数据库等多种类型的环境准备。

#### 基本结构

```json
{
  "path": "目标路径",
  "type": "环境类型",
  "content": "内容（可选）"
}
```

#### 字段说明

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `path` | 字符串 | 是 | 目标路径，支持相对路径和绝对路径 |
| `type` | 字符串 | 是 | 环境类型，见下表支持的类型 |
| `content` | 字符串 | 否 | 内容数据，根据类型有不同用途 |

#### 支持的环境类型

| 类型 | 别名 | 说明 | content字段用途 |
|------|------|------|----------------|
| `directory` | `dir` | 创建目录 | 忽略（可为空） |
| `file` | - | 创建文件 | 文件内容 |
| `db` | `database` | 初始化SQLite数据库 | SQL初始化脚本 |

#### 路径处理规则

- **相对路径**：以 `./` 开头，相对于任务执行目录
- **绝对路径**：以 `/` 开头，会自动转换为相对路径（添加 `./` 前缀）
- **目录创建**：自动创建必要的父目录
- **权限设置**：目录权限 `0755`，文件权限 `0644`

### 1. 目录创建（directory/dir）

创建目录结构，支持多层嵌套目录。

```json
{
  "path": "./test_output/",
  "type": "directory",
  "content": ""
}
```

**特性**：
- 自动创建多层目录结构
- 如果目录已存在，跳过创建
- 设置目录权限为 `0755`
- 支持相对路径和绝对路径

**示例**：
```json
[
  {
    "path": "./project/src/",
    "type": "directory"
  },
  {
    "path": "./project/docs/",
    "type": "dir"
  },
  {
    "path": "/tmp/workspace/logs/",
    "type": "directory"
  }
]
```

### 2. 文件创建（file）

创建文件并写入指定内容。

```json
{
  "path": "./test_output/config.txt",
  "type": "file",
  "content": "# 配置文件\ndebug=true\nport=8080"
}
```

**特性**：
- 自动创建文件所在的目录
- 如果文件已存在，跳过创建
- 设置文件权限为 `0644`
- 支持多行文本内容
- 支持UTF-8编码

**示例**：
```json
[
  {
    "path": "./config/app.conf",
    "type": "file",
    "content": "# 应用配置\napp_name=MyApp\nversion=1.0.0\ndebug=false"
  },
  {
    "path": "./data/sample.json",
    "type": "file",
    "content": "{\"name\": \"test\", \"value\": 123}"
  },
  {
    "path": "./scripts/init.sh",
    "type": "file",
    "content": "#!/bin/bash\necho \"初始化脚本\"\nmkdir -p logs"
  }
]
```

### 3. 数据库初始化（db/database）

创建SQLite数据库并执行初始化SQL脚本。

```json
{
  "path": "./test.db",
  "type": "db",
  "content": "CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT); INSERT INTO users (name) VALUES ('test');"
}
```

**特性**：
- 使用纯Go的SQLite驱动（modernc.org/sqlite）
- 如果数据库文件已存在，会先删除再重新创建
- 支持复杂的SQL脚本，包括多表创建和数据插入
- 自动解析SQL语句，支持注释和多行语句
- 提供详细的执行结果反馈

**SQL脚本解析特性**：
- 支持单行注释：`--` 和 `#`
- 支持块注释：`/* ... */`
- 自动分割多个SQL语句（以分号分隔）
- 忽略空行和纯注释行
- 记录每条语句的执行时间和影响行数

**示例**：
```json
[
  {
    "path": "./users.db",
    "type": "database",
    "content": "-- 用户表\nCREATE TABLE users (\n  id INTEGER PRIMARY KEY AUTOINCREMENT,\n  username VARCHAR(50) UNIQUE NOT NULL,\n  email VARCHAR(100),\n  created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n);\n\n-- 插入测试数据\nINSERT INTO users (username, email) VALUES \n  ('admin', '<EMAIL>'),\n  ('user1', '<EMAIL>');"
  },
  {
    "path": "./inventory.db",
    "type": "db",
    "content": "CREATE TABLE products (id INTEGER PRIMARY KEY, name TEXT, price REAL, stock INTEGER); CREATE TABLE orders (id INTEGER PRIMARY KEY, product_id INTEGER, quantity INTEGER, order_date DATETIME DEFAULT CURRENT_TIMESTAMP); INSERT INTO products VALUES (1, 'Laptop', 999.99, 10), (2, 'Mouse', 29.99, 50);"
  }
]
```

**复杂SQL脚本示例**：
```json
{
  "path": "./complex.db",
  "type": "db",
  "content": "-- 创建用户表\nCREATE TABLE users (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    username VARCHAR(50) UNIQUE NOT NULL,\n    password_hash VARCHAR(255) NOT NULL,\n    email VARCHAR(100),\n    status INTEGER DEFAULT 1,\n    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n);\n\n-- 创建角色表\nCREATE TABLE roles (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    name VARCHAR(50) UNIQUE NOT NULL,\n    description TEXT\n);\n\n-- 创建用户角色关联表\nCREATE TABLE user_roles (\n    user_id INTEGER,\n    role_id INTEGER,\n    PRIMARY KEY (user_id, role_id),\n    FOREIGN KEY (user_id) REFERENCES users(id),\n    FOREIGN KEY (role_id) REFERENCES roles(id)\n);\n\n-- 插入初始角色\nINSERT INTO roles (name, description) VALUES \n    ('admin', '系统管理员'),\n    ('user', '普通用户'),\n    ('guest', '访客用户');\n\n-- 插入测试用户\nINSERT INTO users (username, password_hash, email) VALUES \n    ('admin', 'hashed_password_1', '<EMAIL>'),\n    ('testuser', 'hashed_password_2', '<EMAIL>');\n\n-- 分配角色\nINSERT INTO user_roles (user_id, role_id) VALUES \n    (1, 1),  -- admin用户分配admin角色\n    (2, 2);  -- testuser分配user角色"
}
```

### 结果验证配置

#### 规则检查（rule_judge）
```json
{
  "type": "check_file_exist",
  "target": "./test_output/test.txt",
  "method": "rule_judge",
  "pass_required": true,
  "value": "文件应该存在"
}
```

#### LLM检查（llm_judge）
```json
{
  "type": "check_file_content",
  "target": "./test_output/test.txt",
  "method": "llm_judge",
  "pass_required": true,
  "value": "文件内容应该包含'Hello World'"
}
```

### mcpservers.json配置文件

可选的自定义MCP服务器配置文件，用于覆盖默认配置：

```json
{
  "filesystem": {
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "./custom_path"],
    "env": {
      "NODE_ENV": "production"
    }
  },
  "sqlite": {
    "command": "uvx",
    "args": ["mcp-server-sqlite", "--db-path", "./custom.db"],
    "env": {}
  }
}
```

## 输出文件说明

### 输出文件列表

算子会为每个模型和每个输入文件生成对应的结果文件：

1. **模型结果文件**：`{输入文件名}_{模型名}_results.jsonl`
2. **汇总报告**：`summary_report.json`

### 输出文件Schema

#### 模型结果文件格式（JSONL）

每行包含一个完整的评估结果：

```json
{
  "data_id": 1,
  "type": "static",
  "tags": ["filesystem", "单轮"],
  "ground_truth": "期望的结果描述",
  "reference": "参考信息",
  "system": "你是一个有用的助手，擅长调用工具解决问题。",
  "extra_info": {
    "index": 1,
    "sub_scenario": "文件操作测试",
    "difficulty_level": "easy",
    "query": "请创建一个名为test.txt的文件，内容为'Hello World'",
    "server_name": ["filesystem"],
    "environment_dependency": [...],
    "check_list": [...]
  },
  "bench_result": {
    "tool_call_list": [
      {
        "tool_name": "write_file",
        "arguments": {
          "path": "./test_output/test.txt",
          "content": "Hello World"
        },
        "result": "文件创建成功",
        "success": true,
        "time_cost_ms": 150
      }
    ],
    "check_list": [
      {
        "type": "check_file_exist",
        "target": "./test_output/test.txt",
        "method": "rule_judge",
        "pass_required": true,
        "value": "文件应该存在",
        "excute_result": "success",
        "time_cost_ms": 50
      }
    ],
    "available_tools": [
      {
        "name": "write_file",
        "description": "Write content to a file"
      }
    ]
  },
  "execution_result": {
    "task_id": "openai_gpt-4o-mini_1_1735123456",
    "model_string": "openai:gpt-4o-mini",
    "data_id": 1,
    "success": true,
    "log_path": "logs/openai_gpt-4o-mini_1_1735123456.log"
  }
}
```

#### 汇总报告格式（JSON）

```json
{
  "total_models": 2,
  "models": [
    "openai:gpt-4o-mini",
    "anthropic:claude-3-sonnet"
  ],
  "total_jsonl_files": 1,
  "total_custom_mcp_servers": 0,
  "jsonl_files": ["test_data.jsonl"],
  "custom_mcp_servers": [],
  "total_output_files": 2,
  "output_files": [
    "test_data_openai_gpt-4o-mini_results.jsonl",
    "test_data_anthropic_claude-3-sonnet_results.jsonl"
  ],
  "execution_summary": {
    "openai:gpt-4o-mini": {
      "total_tasks": 10,
      "successful_tasks": 8,
      "success_rate": 0.8,
      "output_files": ["test_data_openai_gpt-4o-mini_results.jsonl"]
    },
    "anthropic:claude-3-sonnet": {
      "total_tasks": 10,
      "successful_tasks": 9,
      "success_rate": 0.9,
      "output_files": ["test_data_anthropic_claude-3-sonnet_results.jsonl"]
    }
  }
}
```

### 输出字段说明

#### bench_result字段

| 字段 | 类型 | 说明 |
|------|------|------|
| `tool_call_list` | 数组 | 工具调用记录列表 |
| `check_list` | 数组 | 结果验证记录列表 |
| `available_tools` | 数组 | 可用工具列表 |

#### execution_result字段

| 字段 | 类型 | 说明 |
|------|------|------|
| `task_id` | 字符串 | 任务唯一标识符 |
| `model_string` | 字符串 | 模型标识符 |
| `data_id` | 整数 | 数据ID |
| `success` | 布尔值 | 执行是否成功 |
| `log_path` | 字符串 | 日志文件路径（本地执行） |
| `log_url` | 字符串 | 日志文件URL（K8s执行） |
| `error` | 字符串 | 错误信息（如果失败） |

## 使用示例

### 基本使用

1. **准备输入文件**：
   - `test_data.jsonl`：包含评估任务
   - `mcpservers.json`（可选）：自定义MCP配置

2. **配置算子参数**：
```json
{
  "models": ["openai:gpt-4o-mini", "anthropic:claude-3-sonnet"],
  "max_workers": 4,
  "run_local": false
}
```

3. **执行算子**，获得输出文件：
   - `test_data_openai_gpt-4o-mini_results.jsonl`
   - `test_data_anthropic_claude-3-sonnet_results.jsonl`
   - `summary_report.json`

### 配置优先级

MCP服务器配置按以下优先级应用：

1. **最高优先级**：JSONL中的 `extra_info.mcpservers`
2. **中等优先级**：输入文件中的 `mcpservers.json`
3. **最低优先级**：算子内置的默认配置

这种设计允许用户在不同层级灵活配置MCP服务器，满足不同场景的需求。

## 常见使用场景

### 场景1：文件系统操作评估

**输入示例**：
```json
{
  "data_id": 1,
  "system": "你是一个文件管理助手",
  "extra_info": {
    "query": "创建一个项目目录结构，包含src、docs、tests三个子目录，并在src中创建main.py文件",
    "server_name": ["filesystem"],
    "environment_dependency": [
      {
        "path": "./project_root/",
        "type": "directory",
        "content": ""
      }
    ],
    "check_list": [
      {
        "type": "check_directory_exist",
        "target": "./project_root/src",
        "method": "rule_judge",
        "pass_required": true,
        "value": "src目录应该存在"
      },
      {
        "type": "check_file_exist",
        "target": "./project_root/src/main.py",
        "method": "rule_judge",
        "pass_required": true,
        "value": "main.py文件应该存在"
      }
    ]
  }
}
```

### 场景2：数据库操作评估

**输入示例**：
```json
{
  "data_id": 2,
  "system": "你是一个数据库管理助手",
  "extra_info": {
    "query": "查询用户表中所有年龄大于25岁的用户信息",
    "server_name": ["sqlite"],
    "environment_dependency": [
      {
        "path": "./users.db",
        "type": "db",
        "content": "CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT, age INTEGER); INSERT INTO users (name, age) VALUES ('Alice', 30), ('Bob', 20), ('Charlie', 28);"
      }
    ],
    "check_list": [
      {
        "type": "check_sql_result",
        "target": "SELECT COUNT(*) FROM users WHERE age > 25",
        "method": "llm_judge",
        "pass_required": true,
        "value": "应该返回2条记录"
      }
    ]
  }
}
```

### 场景3：网页操作评估

**输入示例**：
```json
{
  "data_id": 3,
  "system": "你是一个网页自动化助手",
  "extra_info": {
    "query": "访问百度首页，搜索'人工智能'关键词，并获取搜索结果标题",
    "server_name": ["playwright"],
    "check_list": [
      {
        "type": "check_search_results",
        "target": "",
        "method": "llm_judge",
        "pass_required": true,
        "value": "搜索结果应该包含与人工智能相关的内容"
      }
    ]
  }
}
```

## 最佳实践

### 1. 任务设计建议

- **明确性**：query字段应该清晰描述期望的操作
- **可验证性**：设计合适的check_list来验证结果
- **环境隔离**：使用不同的目录路径避免任务间冲突
- **错误处理**：考虑异常情况的处理和验证

### 2. 性能优化

- **并发控制**：根据系统资源调整max_workers参数
- **批量处理**：将相似任务放在同一个JSONL文件中
- **资源管理**：合理设置环境依赖，避免创建过大的文件或数据库

### 3. 调试技巧

- **日志查看**：检查execution_result中的log_path或log_url
- **分步验证**：使用多个check_list项目分步验证结果
- **本地测试**：使用run_local=true进行本地调试

## 故障排除

### 常见错误及解决方案

#### 1. 模型配置错误
**错误信息**：`未找到模型 xxx 的配置`
**解决方案**：
- 检查models参数格式是否正确（provider:model）
- 确认配置文件中包含对应的模型配置
- 验证API密钥是否正确设置

#### 2. MCP服务器启动失败
**错误信息**：`MCP服务器连接失败`
**解决方案**：
- 检查server_name是否在配置中存在
- 验证MCP服务器的command和args配置
- 确认所需的依赖包已安装

#### 3. 环境初始化失败
**错误信息**：`环境初始化失败`
**解决方案**：
- 检查path路径是否有效
- 确认有足够的磁盘空间
- 验证数据库SQL语句语法

#### 4. 结果验证失败
**错误信息**：`检查项目执行失败`
**解决方案**：
- 检查target路径是否正确
- 验证检查条件是否合理
- 确认文件或数据确实被创建

### 日志分析

#### 本地执行日志
日志文件位置：`logs/{task_id}.log`
```bash
# 查看最新日志
tail -f logs/*.log

# 搜索错误信息
grep -i error logs/*.log
```

#### K8s执行日志
通过log_url字段获取日志链接，或使用kubectl命令：
```bash
# 查看Pod日志
kubectl logs -f job/mcp-evaluation-{task_id}

# 查看Pod状态
kubectl describe job mcp-evaluation-{task_id}
```

## 高级配置

### 自定义模型提供商

在配置文件中添加新的模型提供商：

```yaml
model_providers:
  custom_provider:
    openai_url: "https://api.custom.com/v1"
    openai_api_key: "${CUSTOM_API_KEY}"
    global_timeout: 300
    model_list:
      - "custom-model-1"
      - "custom-model-2"
```

### 自定义MCP服务器

添加新的MCP服务器配置：

```yaml
mcp_servers:
  custom_tool:
    command: "python"
    args: ["/path/to/custom_mcp_server.py"]
    env:
      CUSTOM_CONFIG: "value"
```

### 环境变量配置

支持的环境变量：

| 变量名 | 说明 | 示例 |
|--------|------|------|
| `OPENAI_API_KEY` | OpenAI API密钥 | `sk-xxx` |
| `ANTHROPIC_API_KEY` | Anthropic API密钥 | `sk-ant-xxx` |
| `GOOGLE_API_KEY` | Google API密钥 | `AIzaSyxxx` |
| `MCPHOST_PATH` | mcphost程序路径（本地执行） | `/usr/local/bin/mcphost` |

## 版本兼容性

### 当前版本特性
- 支持MCP协议标准
- 多模型并发评估
- 智能结果验证
- 容器化部署

### 升级注意事项
- 确保JSONL格式符合最新schema
- 检查MCP服务器配置兼容性
- 验证新增的检查类型支持

---

## 技术支持

如遇到问题，请提供以下信息：
1. 输入文件内容（脱敏后）
2. 算子参数配置
3. 错误日志信息
4. 执行环境信息（本地/K8s）

更多技术细节请参考：
- [架构设计文档](./architecture-design.md)
- [技术实现细节](./technical-details.md)
- [API参考文档](./api-reference.md)
