# MCPHost Benchmark 功能使用说明

## 概述

MCPHost 现在支持新的 benchmark 测试格式，可以从脚本中获取参数信息，初始化环境，配置系统提示词，执行大模型调用和工具调用，并进行结果验证。

## 新功能特性

1. **环境初始化**: 支持文件系统和数据库的自动初始化
2. **系统提示词配置**: 从 bench.json 的 `system` 字段读取
3. **查询执行**: 从 `extra_info.query` 字段读取查询内容
4. **结果验证**: 支持 `rule_judge` 和 `llm_judge` 两种检查方法
5. **工具调用记录**: 记录所有工具调用的详细信息
6. **结果输出**: 按照标准格式输出 JSON 结果

## 使用方法

### 基本命令

```bash
./mcphost --config model_config.json --mcpservers mcp_config.json --bench bench.json
```

### 配置文件说明

#### 1. 模型配置文件 (model_config.json)

```json
{
  "model_name": "gpt-4o-mini",
  "model_provider": "openai",
  "openai_url": "",
  "openai_api_key": "your-api-key",
  "global_timeout": 30
}
```

#### 2. MCP服务器配置文件 (mcp_config.json)

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "./"],
      "env": {}
    }
  }
}
```

#### 3. Benchmark配置文件 (bench.json)

```json
{
  "data_id": 1,
  "type": "static",
  "tags": ["test", "单轮"],
  "ground_truth": "",
  "reference": "",
  "system": "你是一个有用的助手，擅长调用工具解决问题",
  "extra_info": {
    "index": 1,
    "sub_scenario": "测试场景",
    "difficulty_level": "easy",
    "query": "请创建一个简单的测试文件 test.txt，内容为 'Hello World'",
    "environment_dependency": [
      {
        "path": "./test_output/",
        "type": "directory",
        "content": ""
      }
    ],
    "check_list": [
      {
        "type": "check_file_exist",
        "target": "./test_output/test.txt",
        "method": "rule_judge",
        "pass_required": true,
        "value": "测试文件应该存在"
      }
    ]
  }
}
```

## 环境依赖类型

### 1. 目录创建
```json
{
  "path": "./test_output/",
  "type": "directory",
  "content": ""
}
```

### 2. 文件创建
```json
{
  "path": "./test_output/config.txt",
  "type": "file",
  "content": "配置内容"
}
```

### 3. 数据库初始化
```json
{
  "path": "./test.db",
  "type": "database",
  "content": "CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT); INSERT INTO users (name) VALUES ('test');"
}
```

## 检查类型

### 1. 规则检查 (rule_judge)
使用 shell 命令进行检查：
```json
{
  "type": "check_file_exist",
  "target": "./test_output/test.txt",
  "method": "rule_judge",
  "pass_required": true,
  "value": "测试文件应该存在"
}
```

### 2. LLM检查 (llm_judge)
使用大模型进行智能检查：
```json
{
  "type": "check_content",
  "target": "",
  "method": "llm_judge",
  "pass_required": true,
  "value": "检查文件内容是否包含 'Hello World'"
}
```

## 输出格式

程序会输出标准的 JSON 格式结果：

```json
{
    "response": "模型的响应内容",
    "tool_call_list_real": [
        {
            "name": "工具名称",
            "arguments": "工具参数JSON字符串",
            "tool_content": "工具执行结果",
            "time_cost_ms": 150
        }
    ],
    "check_list_real": [
        {
            "type": "检查类型",
            "target": "检查目标",
            "method": "检查方法",
            "pass_required": true,
            "value": "检查描述",
            "time_cost_ms": 25,
            "excute_result": "success",
            "err_msg": ""
        }
    ],
    "total_steps": 1,
    "total_time_cost_ms": 2500
}
```

## 兼容性

程序保持对旧格式 bench.json 的兼容性，支持：
- 旧的 `query` 和 `queries` 字段
- 旧的 `input_dependcy` 字段
- 旧的 `check_list` 格式

## 注意事项

1. 确保有足够的权限创建文件和目录
2. 数据库初始化需要 SQLite 支持
3. MCP 服务器需要正确配置和启动
4. API 密钥需要正确设置 