# MCP评估平台技术实现细节

## 1. 核心组件实现细节

### 1.1 算子引擎深度解析

#### 1.1.1 配置管理机制
算子通过YAML配置文件管理多种模型提供商：

```python
# config.py 配置示例
config = {
    "model_providers": {
        "assistant_api": {
            "backend": "yiyan",
            "authorization": "xxx",
            "assistant_server_ip": "*************",
            "assistant_server_port": 8361,
            "app_id": "123456",
            "global_timeout": 300,
            "model_list": ["ERNIE-4.5-single-custom-yiyanweb"]
        },
        "uni_crawl": {
            "openai_url": "http://************:8081/api/real_time/v1",
            "openai_api_key": "sk-123",
            "global_timeout": 300,
            "model_list": ["gpt41_pool", "qwen3235ba22b_pool"]
        }
    }
}
```

#### 1.1.2 任务调度策略
```python
def _process_jsonl_file(self, input_file):
    """处理JSONL文件并生成任务列表"""
    tasks = []
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                bench_data = json.loads(line.strip())
                # 为每个模型生成单独的任务
                for model_string in self.eval_model_list:
                    task_info = {
                        'model_string': model_string,
                        'bench_data': bench_data,
                        'task_id': f"{bench_data.get('data_id', line_num)}_{model_string}",
                        'line_num': line_num
                    }
                    tasks.append(task_info)
            except json.JSONDecodeError as e:
                logger.error(f"解析JSONL第{line_num}行失败: {e}")
    return tasks
```

#### 1.1.3 K8s作业提交机制
```python
def _execute_single_task_k8s(self, task_info):
    """K8s模式执行单个任务"""
    task_id = task_info['task_id']
    
    # 准备输入数据
    input_dir = self.config_dir / f"input_{task_id}"
    input_dir.mkdir(exist_ok=True)
    
    # 生成配置文件
    self._generate_model_config(task_info['model_string'], input_dir / "model_config.json")
    self._generate_bench_config(task_info['bench_data'], input_dir / "bench.json")
    
    # 提交K8s作业
    job_result = submit_k8s_job(
        image_id=K8S_IMAGE_ID,
        command=K8S_COMMAND,
        input_path=str(input_dir),
        output_path=str(self.log_dir),
        timeout=K8S_TIMEOUT,
        source_type=K8S_SOURCE_TYPE
    )
    
    return self._parse_k8s_result(job_result, task_id)
```

### 1.2 MCPHost核心实现

#### 1.2.1 Provider接口设计
```go
// pkg/llm/provider.go
type Provider interface {
    Name() string
    Prompt(ctx context.Context, messages []Message, tools []Tool) (*PromptResult, error)
}

type PromptResult struct {
    Message    Message
    ToolCalls  []ToolCall
    Usage      *Usage
    StopReason string
    Thinking   string  // 用于支持思考过程的模型
}
```

#### 1.2.2 Assistant API Provider实现
```go
// pkg/llm/assistant_api/assistant_api.go
type Provider struct {
    client       *yiyan.YiyanClient
    assistantID  string
    threadID     string
    model        string
    systemPrompt string
    timeout      time.Duration
}

func (p *Provider) Prompt(ctx context.Context, messages []Message, tools []Tool) (*PromptResult, error) {
    // 转换工具格式
    yiyanTools := convertTools(tools)
    
    // 创建运行请求
    runReq := &yiyan.CreateRunRequest{
        AssistantID: p.assistantID,
        ThreadID:    p.threadID,
        Model:       p.model,
        Tools:       yiyanTools,
        Stream:      true,
    }
    
    // 执行流式运行
    return p.handleYiyanStreamRun(ctx, runReq)
}
```

#### 1.2.3 MCP客户端集成
```go
func initializeMCPClients(config MCPConfig) map[string]mcpclient.MCPClient {
    clients := make(map[string]mcpclient.MCPClient)
    
    for serverName, serverConfig := range config.MCPServers {
        if serverConfig.Type == "sse" {
            // SSE模式客户端
            client := mcpclient.NewSSEClient(serverConfig.URL, serverConfig.Headers)
            clients[serverName] = client
        } else {
            // STDIO模式客户端
            client := mcpclient.NewStdioClient(serverConfig.Command, serverConfig.Args)
            clients[serverName] = client
        }
    }
    
    return clients
}
```

### 1.3 工具调用处理流程

#### 1.3.1 工具调用检测和转发
```go
func handleToolCalls(ctx context.Context, toolCalls []ToolCall, mcpClients map[string]mcpclient.MCPClient) []ToolResult {
    var results []ToolResult
    
    for _, toolCall := range toolCalls {
        // 查找对应的MCP客户端
        client := findMCPClientForTool(toolCall.Function.Name, mcpClients)
        if client == nil {
            results = append(results, ToolResult{
                ToolCallID: toolCall.ID,
                Content:    "Tool not found",
                IsError:    true,
            })
            continue
        }
        
        // 执行工具调用
        result := executeToolCall(ctx, client, toolCall)
        results = append(results, result)
    }
    
    return results
}
```

#### 1.3.2 工具结果验证
```go
func (b *BenchResult) ValidateResults() {
    for _, expected := range b.ExpectedResults {
        switch expected.Type {
        case "file":
            b.validateFileExists(expected)
        case "directory":
            b.validateDirectoryExists(expected)
        case "database":
            b.validateDatabaseContent(expected)
        }
    }
}
```

## 2. 数据模型设计

### 2.1 评估结果数据结构
```go
type BenchResult struct {
    DataID           int64       `json:"data_id"`
    ModelName        string      `json:"model_name"`
    StartTime        time.Time   `json:"start_time"`
    EndTime          time.Time   `json:"end_time"`
    TotalTimeMs      int64       `json:"total_time_ms"`
    Query            string      `json:"query"`
    SystemPrompt     string      `json:"system_prompt"`
    ModelResponse    string      `json:"model_response"`
    ToolCalls        []ToolCall  `json:"tool_calls"`
    ValidationResult Validation  `json:"validation_result"`
    Success          bool        `json:"success"`
    ErrorMessage     string      `json:"error_message,omitempty"`
}

type ToolCall struct {
    Name         string `json:"name"`
    Arguments    string `json:"arguments"`
    Content      string `json:"content"`
    TimeCostMs   int64  `json:"time_cost_ms"`
    Success      bool   `json:"success"`
    ErrorMessage string `json:"error_message,omitempty"`
}
```

### 2.2 配置数据模型
```go
type ModelConfig struct {
    ModelName            string `json:"model_name"`
    ModelProvider        string `json:"model_provider"`
    OpenAIURL           string `json:"openai_url,omitempty"`
    OpenAIAPIKey        string `json:"openai_api_key,omitempty"`
    AnthropicURL        string `json:"anthropic_url,omitempty"`
    AnthropicAPIKey     string `json:"anthropic_api_key,omitempty"`
    GoogleAPIKey        string `json:"google_api_key,omitempty"`
    Authorization       string `json:"authorization,omitempty"`
    AssistantID         string `json:"assistant_id,omitempty"`
    AssistantServerIP   string `json:"assistant_server_ip,omitempty"`
    AssistantServerPort int    `json:"assistant_server_port,omitempty"`
    AppID               string `json:"app_id,omitempty"`
    Backend             string `json:"backend,omitempty"`
    GlobalTimeout       int    `json:"global_timeout"`
}
```

## 3. 并发和性能优化

### 3.1 算子层面并发控制
```python
def _run(self):
    """主要执行逻辑"""
    all_tasks = []
    
    # 处理所有输入文件
    for input_file in self.input_files:
        tasks = self._process_jsonl_file(input_file)
        all_tasks.extend(tasks)
    
    logger.info(f"总共生成 {len(all_tasks)} 个评估任务")
    
    # 并发执行任务
    completed_tasks = []
    with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
        # 提交所有任务
        future_to_task = {}
        for task in all_tasks:
            if self.run_local:
                future = executor.submit(self._execute_single_task_local, task)
            else:
                future = executor.submit(self._execute_single_task_k8s, task)
            future_to_task[future] = task
        
        # 等待任务完成
        for future in as_completed(future_to_task):
            task = future_to_task[future]
            try:
                result = future.result()
                completed_tasks.append(result)
                logger.info(f"任务 {task['task_id']} 完成")
            except Exception as e:
                logger.error(f"任务 {task['task_id']} 执行失败: {e}")
```

### 3.2 连接池和重试机制
```go
// HTTP客户端配置
func createHTTPClient() *http.Client {
    transport := &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
        DisableKeepAlives:   false,
    }
    
    return &http.Client{
        Transport: transport,
        Timeout:   30 * time.Second,
    }
}

// 重试机制
func retryWithBackoff(operation func() error, maxRetries int) error {
    backoff := time.Second
    for i := 0; i < maxRetries; i++ {
        if err := operation(); err == nil {
            return nil
        }
        
        time.Sleep(backoff)
        backoff *= 2
        if backoff > 60*time.Second {
            backoff = 60 * time.Second
        }
    }
    return fmt.Errorf("operation failed after %d retries", maxRetries)
}
```

## 4. 错误处理和容错机制

### 4.1 分层错误处理
```go
type MCPError struct {
    Type    string `json:"type"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func (e MCPError) Error() string {
    return fmt.Sprintf("[%s] %s", e.Type, e.Message)
}

// 错误分类
const (
    ErrorTypeConnection = "CONNECTION_ERROR"
    ErrorTypeTimeout    = "TIMEOUT_ERROR"
    ErrorTypeValidation = "VALIDATION_ERROR"
    ErrorTypeModel      = "MODEL_ERROR"
    ErrorTypeTool       = "TOOL_ERROR"
)
```

### 4.2 超时处理
```go
func executeWithTimeout(ctx context.Context, timeout time.Duration, operation func(context.Context) error) error {
    ctx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()
    
    done := make(chan error, 1)
    go func() {
        done <- operation(ctx)
    }()
    
    select {
    case err := <-done:
        return err
    case <-ctx.Done():
        return fmt.Errorf("operation timed out after %v", timeout)
    }
}
```

## 5. 监控和可观测性

### 5.1 指标收集
```go
type Metrics struct {
    TasksTotal        int64         `json:"tasks_total"`
    TasksSuccessful   int64         `json:"tasks_successful"`
    TasksFailed       int64         `json:"tasks_failed"`
    AverageLatency    time.Duration `json:"average_latency"`
    ModelCallCount    map[string]int64 `json:"model_call_count"`
    ToolCallCount     map[string]int64 `json:"tool_call_count"`
    ErrorsByType      map[string]int64 `json:"errors_by_type"`
}

func (m *Metrics) RecordTaskStart() {
    atomic.AddInt64(&m.TasksTotal, 1)
}

func (m *Metrics) RecordTaskSuccess(duration time.Duration) {
    atomic.AddInt64(&m.TasksSuccessful, 1)
    // 更新平均延迟
}
```

### 5.2 日志结构化
```go
type LogEntry struct {
    Timestamp   time.Time   `json:"timestamp"`
    Level       string      `json:"level"`
    Message     string      `json:"message"`
    TaskID      string      `json:"task_id,omitempty"`
    ModelName   string      `json:"model_name,omitempty"`
    Duration    int64       `json:"duration_ms,omitempty"`
    Error       string      `json:"error,omitempty"`
    Metadata    interface{} `json:"metadata,omitempty"`
}
```

## 6. 安全和权限控制

### 6.1 API密钥管理
```go
type SecretManager struct {
    secrets map[string]string
    mutex   sync.RWMutex
}

func (sm *SecretManager) GetSecret(key string) (string, error) {
    sm.mutex.RLock()
    defer sm.mutex.RUnlock()
    
    if value, exists := sm.secrets[key]; exists {
        return value, nil
    }
    
    // 尝试从环境变量获取
    if envValue := os.Getenv(key); envValue != "" {
        return envValue, nil
    }
    
    return "", fmt.Errorf("secret not found: %s", key)
}
```

### 6.2 容器安全
```dockerfile
# Dockerfile安全配置
FROM python:3.11-slim

# 创建非root用户
RUN groupadd -r work && useradd -r -g work work

# 设置工作目录
WORKDIR /home/<USER>

# 复制文件并设置权限
COPY --chown=work:work . .

# 切换到非root用户
USER work

# 限制网络访问
ENV NO_PROXY=localhost,127.0.0.1
```

## 7. 部署和运维

### 7.1 健康检查
```go
func healthCheck() gin.HandlerFunc {
    return func(c *gin.Context) {
        status := map[string]interface{}{
            "status":    "healthy",
            "timestamp": time.Now(),
            "version":   version,
            "uptime":    time.Since(startTime),
        }
        
        // 检查关键依赖
        if err := checkDependencies(); err != nil {
            status["status"] = "unhealthy"
            status["error"] = err.Error()
            c.JSON(500, status)
            return
        }
        
        c.JSON(200, status)
    }
}
```

### 7.2 优雅关闭
```go
func gracefulShutdown(server *http.Server) {
    // 监听系统信号
    quit := make(chan os.Signal, 1)
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit
    
    log.Println("正在关闭服务器...")
    
    // 设置关闭超时
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    // 优雅关闭
    if err := server.Shutdown(ctx); err != nil {
        log.Fatal("服务器强制关闭:", err)
    }
    
    log.Println("服务器已关闭")
}
```

## 8. 测试策略

### 8.1 单元测试
```go
func TestProviderPrompt(t *testing.T) {
    provider := &MockProvider{}
    
    messages := []Message{{Role: "user", Content: "test"}}
    tools := []Tool{{Name: "test_tool"}}
    
    result, err := provider.Prompt(context.Background(), messages, tools)
    
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.Equal(t, "test_response", result.Message.Content)
}
```

### 8.2 集成测试
```python
def test_full_evaluation_pipeline():
    """测试完整的评估流程"""
    # 准备测试数据
    test_config = create_test_config()
    test_bench = create_test_bench()
    
    # 执行评估
    operator = Operator()
    result = operator.execute(test_config, test_bench)
    
    # 验证结果
    assert result.success
    assert len(result.tool_calls) > 0
    assert result.validation_result.passed
```

---

本技术实现细节文档深入分析了MCP评估平台的核心技术实现，为开发团队提供了详细的技术指导和最佳实践参考。 