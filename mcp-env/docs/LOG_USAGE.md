# MCPHost 日志功能使用说明

## 概述

MCPHost 现在支持自动日志记录功能，通过 `boost.sh` 脚本可以自动将运行日志保存到唯一命名的日志文件中。

## 功能特性

### 自动日志文件命名

- **格式**: `日期_UUID.log`
- **示例**: `20250529_d7393f3bcba24d7094ba4fd058ef16b1.log`
- **位置**: `logs/` 目录下

### 日志内容

日志文件包含：
- ✅ 所有程序输出信息（INFO、ERROR等）
- ✅ 执行结果和错误信息
- ✅ 时间戳
- ✅ 工具调用记录
- ✅ 检查结果

## 使用方法

### 1. 运行 boost.sh 脚本

```bash
./boost.sh
```

输出示例：
```
日志将保存到: logs/20250529_d7393f3bcba24d7094ba4fd058ef16b1.log
开始执行 mcphost...
[程序输出...]
执行完成，日志已保存到: logs/20250529_d7393f3bcba24d7094ba4fd058ef16b1.log
```

### 2. 查看日志文件

```bash
# 列出所有日志文件
ls logs/

# 查看最新的日志文件
ls -t logs/ | head -1

# 查看特定日志文件
cat logs/20250529_d7393f3bcba24d7094ba4fd058ef16b1.log

# 实时监控日志（如果程序正在运行）
tail -f logs/20250529_d7393f3bcba24d7094ba4fd058ef16b1.log
```

### 3. 日志文件管理

```bash
# 查看日志文件大小
ls -lh logs/

# 清理旧的日志文件（保留最近7天）
find logs/ -name "*.log" -mtime +7 -delete

# 按日期查找日志文件
ls logs/20250529_*.log
```

## 日志文件内容示例

```
2025/05/29 14:43:53 INFO 启动基准测试模式 config=conf/model_config.json mcpservers=conf/mcpservers.json bench=conf/bench.json
2025/05/29 14:43:53 INFO 使用系统提示词 prompt=你是一个有用的助手
2025/05/29 14:43:53 INFO 模型已加载 provider=anthropic
2025/05/29 14:43:53 INFO 执行查询 query="说 'Hello World'"

  You: 说 'Hello World'
2025/05/29 14:43:53 ERRO 执行查询失败 error="authentication_error: invalid x-api-key"
2025/05/29 14:43:53 INFO === 执行检查列表 ===
{
    "response": "错误: authentication_error: invalid x-api-key",
    "tool_call_list_real": [],
    "check_list_real": [],
    "total_steps": 0,
    "total_time_cost_ms": 616
}
2025/05/29 14:43:53 INFO 结果已保存 file=output/my_results.json
2025/05/29 14:43:53 INFO 基准测试完成
```

## 配置文件

确保以下配置文件存在：

- `conf/model_config.json` - 模型配置
- `conf/mcpservers.json` - MCP服务器配置  
- `conf/bench.json` - 基准测试配置

## 注意事项

1. **目录自动创建**: `logs/` 目录会在首次运行时自动创建
2. **文件权限**: 确保 `boost.sh` 脚本有执行权限（`chmod +x boost.sh`）
3. **磁盘空间**: 日志文件会占用磁盘空间，建议定期清理旧日志
4. **并发运行**: 每次运行都会生成新的日志文件，支持并发执行

## 故障排查

### 问题：无法创建日志文件

**解决方案**：
1. 检查当前目录是否有写入权限
2. 确保 `uuidgen` 命令可用
3. 检查磁盘空间是否充足

### 问题：日志内容不完整

**解决方案**：
1. 确保程序完全执行完毕
2. 检查程序是否被意外终止
3. 查看是否有权限问题

### 问题：找不到日志文件

**解决方案**：
1. 检查 `logs/` 目录是否存在
2. 确认脚本是否成功执行
3. 查看脚本输出的日志文件路径 