# 自定义算子集成自动评估流程技术方案

## 1. 背景和目标

### 1.1 当前现状
- 自定义算子 `self_define_operator.py` 目前完全依赖 `self.params` 获取参数
- 算子支持 MCP 配置生成和批量执行，适用于普通的手动调用流程
- 参数包括：models、max_workers、run_local、check_model、retry_failed_only 等

### 1.2 集成需求
- 需要兼容自动评估流程，支持 `global_params` 字段
- 有 `global_params` 字段说明是自动评估流程，没有则说明是普通流程
- 自动评估流程中没有 `self.params` 字段传入
- 保持向后兼容性，确保现有普通流程不受影响

### 1.3 目标
- 实现双模式支持：普通流程模式 + 自动评估流程模式
- 建立参数映射机制，将 `global_params` 转换为算子内部参数格式
- 提供统一的参数获取接口，屏蔽底层差异

## 2. 技术分析

### 2.1 现有算子分析
```python
# 当前参数获取方式（仅支持 self.params）
models_param = self.params.get("models")
max_workers_param = self.params.get("max_workers")
self.run_local = self.params.get("run_local", False)
self.check_model = self.params.get("check_model")
self.retry_failed_only = self.params.get("retry_failed_only", False)
```

### 2.2 参考算子分析
参考 `monitor_crawl_publish.py` 的关键特征：
- 检查 `self.global_params` 是否存在，为 None 则报错
- 从 `global_params` 获取配置：pool_name、models、project_flow_id 等
- 支持本地调试模式（通过环境变量控制）

### 2.3 参数格式差异
**global_params.models 格式：**
```python
"models": [
    {
        "model_id": "api3",
        "type": "sandbox",
        "params": {"userId": **********}
    }
]
```

**self.params.models 格式：**
```python
"models": ["provider:model1", "provider:model2"]
```

## 3. 方案设计

### 3.1 整体架构
```
┌─────────────────┐    ┌──────────────────┐
│   调用入口      │    │   参数来源判断    │
│                │    │                  │
│ ┌─────────────┐ │    │ ┌──────────────┐ │
│ │普通流程调用 │ │───▶│ │self.params   │ │
│ └─────────────┘ │    │ └──────────────┘ │
│                │    │                  │
│ ┌─────────────┐ │    │ ┌──────────────┐ │
│ │自动评估调用 │ │───▶│ │global_params │ │
│ └─────────────┘ │    │ └──────────────┘ │
└─────────────────┘    └──────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │   参数转换层     │
                    │                  │
                    │ ┌──────────────┐ │
                    │ │参数映射转换  │ │
                    │ │格式标准化    │ │
                    │ │默认值处理    │ │
                    │ └──────────────┘ │
                    └──────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │   统一参数接口   │
                    │                  │
                    │ eval_model_list  │
                    │ max_workers      │
                    │ run_local        │
                    │ check_model      │
                    │ retry_failed_only│
                    └──────────────────┘
```

### 3.2 流程判断逻辑
```python
def _is_auto_evaluation_mode(self):
    """判断是否为自动评估模式"""
    return hasattr(self, 'global_params') and self.global_params is not None

def _setup(self):
    """算子执行前准备工作"""
    logger.info("setup executing...")

    # 判断运行模式
    if self._is_auto_evaluation_mode():
        logger.info("运行在自动评估模式")
        self._parse_global_params()
    else:
        logger.info("运行在普通流程模式")
        self._parse_regular_params()

    # 后续统一处理逻辑...
```

### 3.3 参数映射关系
| global_params 字段 | 内部参数 | 转换逻辑 | 默认值 |
|-------------------|---------|----------|--------|
| models | eval_model_list | 转换为 "type:model_id" 格式 | 必填 |
| work_num | max_workers | 取第一个 pool 的 work_num | 4 |
| - | run_local | 自动评估通常远程执行 | false |
| - | check_model | 从配置或环境变量获取 | None |
| - | retry_failed_only | 根据评估策略确定 | false |

## 4. 实现细节

### 4.1 新增方法设计

#### 4.1.1 模式判断方法
```python
def _is_auto_evaluation_mode(self):
    """判断是否为自动评估模式"""
    return hasattr(self, 'global_params') and self.global_params is not None
```

#### 4.1.2 global_params 解析方法
```python
def _parse_global_params(self):
    """解析 global_params 并转换为内部参数格式"""
    if not self.global_params:
        raise ValueError("自动评估模式下 global_params 不能为空")

    # 转换模型列表
    self.eval_model_list = self._convert_models_from_global_params()

    # 获取其他参数
    self.max_workers = self._get_max_workers_from_global_params()
    self.run_local = False  # 自动评估通常远程执行
    self.check_model = self._get_check_model_from_global_params()
    self.retry_failed_only = False  # 默认不启用失败重试
```

#### 4.1.3 模型转换方法
```python
def _convert_models_from_global_params(self):
    """将 global_params.models 转换为内部格式"""
    models = self.global_params.get("models", [])
    if not models:
        raise ValueError("global_params.models 不能为空")

    converted_models = []
    for model in models:
        model_id = model.get("model_id")
        model_type = model.get("type", "openai")

        if not model_id:
            raise ValueError("model_id 不能为空")

        # 转换为 "provider:model" 格式
        converted_model = f"{model_type}:{model_id}"
        converted_models.append(converted_model)

    return converted_models
```

#### 4.1.4 其他参数获取方法
```python
def _get_max_workers_from_global_params(self):
    """从 global_params 获取最大并发数"""
    work_num = self.global_params.get("work_num", {})
    if isinstance(work_num, dict) and work_num:
        # 取第一个 pool 的 work_num
        return list(work_num.values())[0]
    return 4  # 默认值

def _get_check_model_from_global_params(self):
    """从 global_params 获取检查模型"""
    # 可以从环境变量或配置中获取
    return os.environ.get("CHECK_MODEL", None)

def _parse_regular_params(self):
    """解析普通流程的参数（保持原有逻辑）"""
    # 从 self.params 中获取参数，如果没有则使用配置文件中的默认值
    models_param = self.params.get("models")
    if models_param:
        # models参数直接是数组格式，不需要分割
        if isinstance(models_param, list):
            self.eval_model_list = models_param
        elif isinstance(models_param, str):
            # 兼容字符串格式（逗号分隔）
            self.eval_model_list = [
                model.strip() for model in models_param.split(",") if model.strip()
            ]
        else:
            logger.warning(
                f"models参数格式不正确: {type(models_param)}, 使用配置文件默认值"
            )
            self.eval_model_list = config.get("eval_model_list", [])
    else:
        # 使用配置文件中的默认值
        self.eval_model_list = config.get("eval_model_list", [])

    # 从 self.params 中获取 max_workers 参数
    max_workers_param = self.params.get("max_workers")
    if max_workers_param is not None:
        try:
            self.max_workers = int(max_workers_param)
        except (ValueError, TypeError):
            logger.warning(f"max_workers参数无效: {max_workers_param}, 使用默认值")
            self.max_workers = config.get("max_workers", 4)
    else:
        self.max_workers = config.get("max_workers", 4)  # 并发执行数

    # 从 self.params 中获取运行模式参数
    self.run_local = self.params.get("run_local", False)
    if isinstance(self.run_local, str):
        self.run_local = self.run_local.lower() in ("true", "1", "yes")

    # 从 self.params 中获取 check_model 参数
    self.check_model = self.params.get("check_model")

    # 从 self.params 中获取失败重试参数
    self.retry_failed_only = self.params.get("retry_failed_only", False)
    if isinstance(self.retry_failed_only, str):
        self.retry_failed_only = self.retry_failed_only.lower() in (
            "true",
            "1",
            "yes",
        )
```

### 4.2 修改现有 _setup 方法
```python
def _setup(self):
    """算子执行前准备工作"""
    logger.info("setup executing...")
    logger.info("self.task_info: %s", self.task_info)
    logger.info("self.input_files: %s", self.input_files)
    logger.info("self.output_local_path: %s", self.output_local_path)

    # 判断运行模式并解析参数
    if self._is_auto_evaluation_mode():
        logger.info("运行在自动评估模式")
        logger.info("self.global_params: %s", self.global_params)
        self._parse_global_params()
    else:
        logger.info("运行在普通流程模式")
        logger.info("self.params: %s", self.params)
        self._parse_regular_params()

    # 记录解析结果
    logger.info(f"解析得到的模型列表: {self.eval_model_list}")
    logger.info(f"解析得到的最大并发数: {self.max_workers}")
    logger.info(f"运行模式: {'本地运行' if self.run_local else 'K8s运行'}")
    logger.info(
        f"失败重试模式: {'仅重试失败任务' if self.retry_failed_only else '全部重新执行'}"
    )

    # 验证输入
    if not self.eval_model_list:
        raise ValueError(
            "eval_model_list参数不能为空，请通过params.models参数或配置文件指定"
        )

    # 记录模型解析信息
    logger.info("模型列表解析:")
    for model_string in self.eval_model_list:
        provider, model = self._parse_model_string(model_string)
        logger.info(f"  {model_string} -> provider: {provider}, model: {model}")

    if not self.input_files:
        raise ValueError("需要提供输入文件")

    # 后续统一处理逻辑保持不变...
```

### 4.3 错误处理机制
```python
def _validate_auto_evaluation_params(self):
    """验证自动评估模式下的参数"""
    if not self.global_params:
        self.report_error("自动评估模式下 global_params 不能为空", 0)

    models = self.global_params.get("models")
    if not models or not isinstance(models, list):
        self.report_error("global_params.models 必须是非空数组", 0)

    for model in models:
        if not model.get("model_id"):
            self.report_error("model_id 不能为空", 0)

def report_error(self, message, code=1):
    """统一错误报告方法"""
    logger.error(message)
    if self._is_auto_evaluation_mode():
        # 自动评估模式下的错误处理
        raise RuntimeError(f"自动评估错误: {message}")
    else:
        # 普通模式下的错误处理
        raise ValueError(message)
```

### 4.4 环境变量处理
```python
def _setup_environment(self):
    """设置环境变量"""
    if self._is_auto_evaluation_mode():
        # 自动评估模式下的环境设置
        self.local_env_set = os.getenv("LOCAL_ENV_SET")
        if self.local_env_set is None:
            self.env = os.getenv("AIRFLOW_PLATFORM_ENV", "production")
            self.user_name = os.getenv("AIRFLOW_CTX_DAG_OWNER")
            if self.user_name is None:
                self.report_error("AIRFLOW_CTX_DAG_OWNER(user_name) is none", 0)
        else:
            # 本地调试模式
            self.env = self.local_env_set
            self.user_name = "<EMAIL>"
    else:
        # 普通流程模式，不需要特殊的环境设置
        pass
```

## 5. 测试方案

### 5.1 单元测试
- 测试 `_is_auto_evaluation_mode()` 方法
- 测试 `_convert_models_from_global_params()` 方法
- 测试参数转换的正确性
- 测试错误处理逻辑

### 5.2 集成测试
- 普通流程兼容性测试
- 自动评估流程功能测试
- 参数映射完整性测试
- 异常场景测试

### 5.3 测试用例
```python
# 测试用例 1: 普通流程
params = {
    "models": ["openai:gpt-4", "qianfan:ernie-bot"],
    "max_workers": 2,
    "run_local": True
}

# 测试用例 2: 自动评估流程
global_params = {
    "models": [
        {"model_id": "gpt-4", "type": "openai"},
        {"model_id": "ernie-bot", "type": "qianfan"}
    ],
    "work_num": {"default_pool": 2}
}

# 测试用例 3: 错误场景
global_params_invalid = {
    "models": []  # 空数组，应该报错
}
```

### 5.4 测试脚本示例
```python
import unittest
from unittest.mock import Mock, patch

class TestSelfDefineOperator(unittest.TestCase):

    def setUp(self):
        self.operator = Operator([])

    def test_is_auto_evaluation_mode_true(self):
        """测试自动评估模式判断 - True"""
        self.operator.global_params = {"models": []}
        self.assertTrue(self.operator._is_auto_evaluation_mode())

    def test_is_auto_evaluation_mode_false(self):
        """测试自动评估模式判断 - False"""
        self.operator.global_params = None
        self.assertFalse(self.operator._is_auto_evaluation_mode())

    def test_convert_models_from_global_params(self):
        """测试模型转换"""
        self.operator.global_params = {
            "models": [
                {"model_id": "gpt-4", "type": "openai"},
                {"model_id": "ernie-bot", "type": "qianfan"}
            ]
        }
        result = self.operator._convert_models_from_global_params()
        expected = ["openai:gpt-4", "qianfan:ernie-bot"]
        self.assertEqual(result, expected)

    def test_convert_models_empty_error(self):
        """测试空模型列表错误"""
        self.operator.global_params = {"models": []}
        with self.assertRaises(ValueError):
            self.operator._convert_models_from_global_params()
```

## 6. 部署和维护

### 6.1 部署注意事项
- 确保自动评估环境中的依赖项完整
- 验证配置文件的兼容性
- 检查日志输出格式
- 确保环境变量正确设置

### 6.2 监控和日志
- 添加模式识别日志
- 记录参数转换详情
- 监控错误率和性能指标
- 设置关键指标告警

### 6.3 后续维护
- 定期检查参数映射关系的准确性
- 根据自动评估系统的变化调整适配逻辑
- 持续优化性能和稳定性
- 更新文档和测试用例

### 6.4 版本管理
- 使用语义化版本控制
- 记录每次变更的详细说明
- 保持向后兼容性
- 提供迁移指南

## 7. 风险评估

### 7.1 兼容性风险
- **风险**: 现有普通流程可能受到影响
- **缓解**: 充分的回归测试，保持原有逻辑不变
- **监控**: 设置兼容性测试自动化

### 7.2 参数转换风险
- **风险**: 参数映射可能不准确
- **缓解**: 详细的参数验证和错误提示
- **监控**: 记录转换失败的详细日志

### 7.3 性能风险
- **风险**: 新增逻辑可能影响性能
- **缓解**: 轻量级实现，避免重复计算
- **监控**: 性能基准测试和持续监控

### 7.4 维护风险
- **风险**: 双模式增加维护复杂度
- **缓解**: 清晰的代码结构和完善的文档
- **监控**: 代码质量检查和定期审查

## 8. 实施计划

### 8.1 第一阶段：基础功能实现（1-2天）
1. 实现模式判断逻辑
2. 添加 `_parse_global_params()` 方法
3. 实现模型参数转换
4. 修改 `_setup()` 方法

### 8.2 第二阶段：完善和测试（1-2天）
1. 添加错误处理机制
2. 完善参数验证
3. 编写单元测试
4. 进行集成测试

### 8.3 第三阶段：部署和验证（1天）
1. 在测试环境部署
2. 验证普通流程兼容性
3. 验证自动评估流程功能
4. 性能测试和优化

### 8.4 第四阶段：文档和培训（0.5天）
1. 更新使用文档
2. 编写迁移指南
3. 团队培训和知识分享

## 9. 关键代码示例

### 9.1 完整的 _setup 方法修改
```python
def _setup(self):
    """
    算子执行前准备工作
    """
    logger.info("setup executing...")
    logger.info("self.task_info: %s", self.task_info)
    logger.info("self.input_files: %s", self.input_files)
    logger.info("self.output_local_path: %s", self.output_local_path)

    # 判断运行模式并解析参数
    if self._is_auto_evaluation_mode():
        logger.info("运行在自动评估模式")
        logger.info("self.global_params: %s", self.global_params)
        self._parse_global_params()
        self._setup_environment()  # 设置自动评估环境
    else:
        logger.info("运行在普通流程模式")
        logger.info("self.params: %s", self.params)
        self._parse_regular_params()

    # 记录解析结果
    logger.info(f"解析得到的模型列表: {self.eval_model_list}")
    logger.info(f"解析得到的最大并发数: {self.max_workers}")
    logger.info(f"运行模式: {'本地运行' if self.run_local else 'K8s运行'}")
    logger.info(
        f"失败重试模式: {'仅重试失败任务' if self.retry_failed_only else '全部重新执行'}"
    )

    # 验证输入
    if not self.eval_model_list:
        raise ValueError(
            "eval_model_list参数不能为空，请通过params.models参数或配置文件指定"
        )

    # 记录模型解析信息
    logger.info("模型列表解析:")
    for model_string in self.eval_model_list:
        provider, model = self._parse_model_string(model_string)
        logger.info(f"  {model_string} -> provider: {provider}, model: {model}")

    if not self.input_files:
        raise ValueError("需要提供输入文件")

    # 后续处理逻辑保持不变...
```

### 9.2 参数转换核心逻辑
```python
def _convert_models_from_global_params(self):
    """将 global_params.models 转换为内部格式"""
    models = self.global_params.get("models", [])
    if not models:
        raise ValueError("global_params.models 不能为空")

    converted_models = []
    type_mapping = {
        "sandbox": "assistant_api",
        "pdc": "qianfan",
        "openai": "openai",
        "qianfan": "qianfan",
        "uni_crawl": "uni_crawl",
        "anquan": "anquan"
    }

    for model in models:
        model_id = model.get("model_id")
        model_type = model.get("type", "openai")

        if not model_id:
            raise ValueError("model_id 不能为空")

        # 映射模型类型到内部 provider
        provider = type_mapping.get(model_type, model_type)

        # 转换为 "provider:model" 格式
        converted_model = f"{provider}:{model_id}"
        converted_models.append(converted_model)

        logger.info(f"模型转换: {model_type}:{model_id} -> {converted_model}")

    return converted_models
```

## 10. 总结

本方案通过双模式设计实现了自定义算子与自动评估流程的无缝集成，主要特点：

1. **向后兼容**: 保持现有普通流程完全不变
2. **参数映射**: 建立了完整的参数转换机制
3. **错误处理**: 提供了完善的错误检查和报告
4. **可维护性**: 代码结构清晰，易于后续扩展
5. **可测试性**: 提供了完整的测试方案

### 10.1 预期收益
- 算子可同时支持手动调用和自动评估两种场景
- 减少重复开发，提高代码复用率
- 统一的参数处理逻辑，降低维护成本
- 完善的错误处理，提高系统稳定性

### 10.2 成功标准
- 普通流程 100% 向后兼容
- 自动评估流程功能完整可用
- 参数转换准确率 100%
- 单元测试覆盖率 > 90%
- 性能影响 < 5%

通过这个方案，自定义算子将具备更强的适应性和扩展性，能够满足不同业务场景的需求。