{"id": 1, "query": "在项目'alpha_project'中，将所有模块（'moduleA', 'moduleB'）的日志文件（扩展名为.log）从各自的'logs'子目录移动到统一的'./file_system_output/task_11/archived_logs'目录。同时，查找项目根目录下所有临时文件（.tmp后缀），并将这些临时文件名（仅文件名，非完整路径）写入到'./file_system_output/task_11/tmp_files_report.txt'报告中。"}
{"id": 2, "query": "为新项目 'WebAppProject' 创建标准目录结构：'./file_system_output/task_12/WebAppProject' 下包含 'src', 'tests', 'docs'。在 'src' 目录下创建一个 'main.py' 文件，包含一个简单的 'hello_world' 函数。在 'docs' 目录下创建一个 'README.txt' 文件，说明项目用途。"}
{"id": 3, "query": "读取 './file_system_mock_data/task_13/meeting_notes_raw.txt' 中的会议记录，提取所有标记为 '[ACTION]' 的行动项。将这些行动项整理后保存到 './file_system_output/task_13/action_items.json' 文件中，格式为JSON数组，每个对象包含 'item' 和 'assignee' (指派人，位于行动项后的括号内)字段。同时，将原始会议记录文件重命名并移动到 './file_system_output/task_13/processed_notes/meeting_notes_completed.bak'。"}
{"id": 4, "query": "在一个配置文件 './file_system_mock_data/task_14/app_config.json' 中，将所有出现的旧API域名 'old-api.example.com' 替换为 'new-api.internal.net'。同时，将 'debug_mode' 的值从 'true' 修改为 'false'。将修改后的内容保存到 './file_system_output/task_14/app_config_updated.json'。"}
{"id": 5, "query": "读取 './file_system_mock_data/task_15/product_list.csv' 文件内容，计算总共有多少行数据（不包括表头）。将此计数结果写入到 './file_system_output/task_15/product_count_report.txt'。"}
{"id": 6, "query": "为Python Flask Web服务创建一个基础项目结构。在'./file_system_output/task_16/flask_service'目录下创建'app'子目录和'tests'子目录。在'app'目录中创建'__init__.py', 'routes.py', 'models.py'三个空的Python文件。在根目录'./file_system_output/task_16/flask_service'下创建'run.py'和'config.py'两个空的Python文件。"}
{"id": 7, "query": "在'./file_system_mock_data/task_17/source_code/'目录下递归查找所有的Python文件（.py）。对每个找到的Python文件，提取所有以'TODO:'开头的注释行（去除'TODO:'及前面的字符和两端空白）。将这些提取的TODO项汇总到一个JSON文件中 './file_system_output/task_17/todo_summary.json'，格式为 { 'file_path': ['todo_item1', 'todo_item2', ...] }。同时，列出'./file_system_mock_data/task_17/source_code/'目录下的所有内容，并将列表保存到'./file_system_output/task_17/source_directory_listing.txt'。"}
{"id": 8, "query": "比较 './file_system_mock_data/task_18/version1/app.js' 和 './file_system_mock_data/task_18/version2/app.js' 两个JavaScript文件的内容。首先，获取这两个文件的元数据信息（大小、修改时间等），并将这些信息组合后写入 './file_system_output/task_18/metadata_comparison.txt'。然后，对这两个文件进行编辑，对 version1 的 'app.js' 使用 dryRun 模式预览将所有 'console.log' 替换为 'Logger.info' 的效果，并将此 dryRun 的差异输出保存到 './file_system_output/task_18/v1_potential_changes.txt'。"}
{"id": 9, "query": "检查 './file_system_mock_data/task_19/legacy_code.php' 文件是否存在。如果存在，读取其内容，并在内容末尾追加一行注释 '// PHP code review pending'。将修改后的内容保存到 './file_system_output/task_19/legacy_code_reviewed.php'。"}
{"id": 10, "query": "在'./file_system_mock_data/task_20/web_assets/'目录下，将所有的'.css'文件移动到新建的'./file_system_output/task_20/styles/'子目录中，并将所有的'.js'文件移动到新建的'./file_system_output/task_20/scripts/'子目录中。之后，列出'./file_system_output/task_20/'目录的内容，将列表保存到'./file_system_output/task_20/structure_report.txt'。"}
{"id": 11, "query": "我需要整理一下我的旧项目备份。请在 `./file_system_mock_data/task_31/project_backups/` 目录下查找所有以 `.bak` 结尾的旧备份文件，并将它们移动到一个新的存档目录 `./file_system_output/task_31/archived_backups/`。同时，记录下所有被移动的文件名及其原始路径到一个报告文件 `./file_system_output/task_31/archive_log.txt` 中。"}
{"id": 12, "query": "我正在重构一个Python项目。请帮我处理 `./file_system_mock_data/task_32/my_project/` 目录下的所有 `.py` 文件。对于每个文件，将所有出现的旧函数名 `old_func` 替换为 `new_func`，并将所有对弃用模块 `deprecated_module` 的导入语句 `import deprecated_module` 或 `from deprecated_module import ...` 替换为 `import new_replacement_module`。修改后，将所有修改过的文件复制到 `./file_system_output/task_32/refactored_project/` 目录，并保持原有文件名。最后，在输出目录生成一个 `refactor_summary.json` 文件，记录被修改的文件列表和每个文件中的修改次数（函数名替换次数和导入语句替换次数）。"}
{"id": 13, "query": "我有一批服务器日志文件，存放在 `./file_system_mock_data/task_33/server_logs/` 目录中，文件名格式为 `app_server_YYYYMMDD.log`。请处理所有这些日志文件：首先，将它们合并到一个名为 `consolidated_errors.log` 的文件中，该文件应保存在 `./file_system_output/task_33/` 目录，但只包含所有日志文件中标记为 'ERROR' 的行。其次，统计每个原始日志文件中 'ERROR' 级别日志的数量，并将此统计信息保存为 `./file_system_output/task_33/error_summary.csv` 文件，格式为 'FileName,ErrorCount'。"}
{"id": 14, "query": "我需要为我的新网站项目 `./file_system_output/task_34/new_website/` 创建基本的文件结构和一些初始内容。请创建以下目录结构：`./file_system_output/task_34/new_website/css` 和 `./file_system_output/task_34/new_website/js`。然后在根目录 `new_website` 下创建一个 `index.html` 文件，包含基本的HTML骨架和一个指向 `style.css` 的链接以及一个指向 `script.js` 的脚本引用。在 `css` 目录下创建一个 `style.css` 文件，包含一个简单的body背景颜色样式。在 `js` 目录下创建一个 `script.js` 文件，包含一个打印 'Hello World' 到控制台的简单脚本。"}
{"id": 15, "query": "在 `./file_system_mock_data/task_35/documents/` 目录及其子目录下，查找所有 `.txt` 文件。对于找到的每个文件，将所有出现的公司旧名称 'OldCorp' 替换为新名称 'NewFuture Inc.'。将修改后的文件保存在 `./file_system_output/task_35/updated_documents/` 对应的相对路径下。此外，生成一个 `replacement_log.txt` 文件在输出根目录，记录哪些文件被修改了。"}
{"id": 16, "query": "我有两个版本的配置文件 `./file_system_mock_data/task_36/config_v1.json` 和 `./file_system_mock_data/task_36/config_v2.json`。请比较这两个文件，找出它们之间的差异。将差异以Git风格的diff格式（包含上下文）保存到 `./file_system_output/task_36/config_diff.txt`。同时，获取这两个文件的详细元数据（大小、类型、修改时间——仅作记录，不用于判断“早于N天”）并将这些信息以及差异文件的路径汇总到一个JSON报告 `./file_system_output/task_36/comparison_report.json` 中。"}
{"id": 17, "query": "我需要快速搭建一个新的PHP项目骨架。请在 `./file_system_output/task_37/new_php_project/` 目录下创建以下结构：一个 `public` 目录，一个 `src` 目录。在 `public` 目录下创建一个 `index.php` 文件，内容为 `<?php echo 'Hello World from PHP!'; ?>`。在 `src` 目录下创建一个空的 `App.php` 文件。"}
{"id": 18, "query": "我有一个CSV格式的客户数据文件 `./file_system_mock_data/task_38/customer_data.csv`。请从中提取所有邮箱地址，并将这些邮箱地址（每行一个）保存到 `./file_system_output/task_38/emails.txt`。同时，将原始CSV文件重命名为 `customer_data_processed.csv.bak` 并将其移动到 `./file_system_output/task_38/archive/` 目录中。"}
{"id": 19, "query": "我有一个旧的JavaScript脚本 `./file_system_mock_data/task_39/old_script.js`。我想更新其中的一个版权声明。请将文件中第一次出现的 `'Copyright 2023'` 替换为 `'Copyright 2024'`。将修改后的内容保存到 `./file_system_output/task_39/updated_script.js`。"}
{"id": 20, "query": "我需要清理一些临时文件。请检查 `./file_system_mock_data/task_40/temp_dir/` 目录下的所有文件。如果文件名以 `_temp` 结尾或者扩展名为 `.tmp`，则将该文件移动到 `./file_system_output/task_40/toBeDeleted/` 目录下。然后，列出 `./file_system_output/task_40/toBeDeleted/` 目录中的所有文件，并将列表保存到 `./file_system_output/task_40/cleanup_report.txt`。"}
{"id": 21, "query": "为新启动的'阿尔法项目'创建一个项目笔记目录，路径为'./file_system_output/task_51/alpha_project_notes'。在该目录中创建一个名为'meeting_agenda.txt'的文件，记录下一次项目会议的议程要点：1. 项目目标回顾, 2. 近期任务分配, 3. 下一步计划讨论, 4. Q&A环节。完成后，列出这个新创建的笔记目录中的所有内容。"}
{"id": 23, "query": "一位开发者需要重构一个Python脚本。请将位于 './file_system_mock_data/task_53/script_module.py' 文件中的所有 'temporary_variable' 字符串替换为 'final_result_var'。在实际修改前，请先对 './file_system_output/task_53/script_module_edited.py' （这是原文件的副本）执行一次 dryRun 预览，确认更改。然后，在副本上执行实际的替换操作。原始文件不应被修改。"}
{"id": 24, "query": "为即将启动的 'OmegaPlatform' Web项目初始化标准目录结构。在 './file_system_output/task_54/OmegaPlatform/' 根目录下创建以下子目录: 'src/assets/css', 'src/assets/js', 'src/components', 'public_html', 'documentation', 'tests'。然后，将预先准备好的 './file_system_mock_data/task_54/templates/main_style.css' 文件移动到 'src/assets/css/' 目录并重命名为 'style.css'。同时，将 './file_system_mock_data/task_54/templates/info.txt' 移动到 'documentation/' 目录并重命名为 'project_readme.txt'。最后，在 'src/assets/js/' 目录下创建一个空的 'main_app.js' 文件。"}
{"id": 25, "query": "整理位于 './file_system_mock_data/task_55/temp_files/' 目录下的临时文件。首先，确保归档目录 './file_system_output/task_55/archived_temp_data/' 存在。然后，在该临时文件目录中，找到所有以 '.tmpdata' 为后缀的文件。对于找到的每个文件，将其重命名，在原文件名前添加 'processed_' 前缀，并更改后缀为 '.xlm'。最后，将这些重命名后的文件移动到之前创建的归档目录中。"}
{"id": 26, "query": "销售部门需要合并季度销售数据。有三个CSV文件分别位于 './file_system_mock_data/task_56/q1_sales.csv', './file_system_mock_data/task_56/q2_sales.csv', './file_system_mock_data/task_56/q3_sales.csv'。每个文件包含 'ProductID,Quantity,Revenue' 列。请读取这三个文件，汇总每个 'ProductID' 的总 'Quantity' 和总 'Revenue'。将最终的汇总结果保存到一个新的CSV文件 './file_system_output/task_56/annual_summary.csv' 中，文件应包含表头 'ProductID,TotalQuantity,TotalRevenue'。在开始操作前，先列出服务器允许访问的所有目录。"}
{"id": 27, "query": "需要从一个JSON配置文件 './file_system_mock_data/task_57/service_config.json' 中提取 'service_url' 字段的值。将这个提取到的URL值保存到一个新的文本文件 './file_system_output/task_57/extracted_service_url.txt' 中。新文件内容应该只有这个URL本身。"}
{"id": 28, "query": "项目经理需要整理所有项目文档中的待办事项。请在 './file_system_mock_data/task_58/project_documents/' 目录及其所有子目录中递归搜索所有以 '.txt' 或 '.md' 结尾的文件，查找包含 'TODO:' (不区分大小写) 字符串的行。将找到的每个文件的相对路径（相对于搜索起始目录）和第一个包含 'TODO:' 的完整行内容，汇总到一个名为 './file_system_output/task_58/project_todo_list.txt' 的文件中。在搜索时，需要排除 'project_documents/archive/' 目录下的所有文件。"}
{"id": 29, "query": "运维工程师需要修改一个PHP应用的配置文件。请检查位于 './file_system_mock_data/task_59/app_settings.php' 的文件。首先，将此文件备份到 './file_system_output/task_59/backup/app_settings.php.bak' (确保backup目录存在)。然后，在 './file_system_output/task_59/app_settings_modified.php' (这是原文件的一个副本) 上进行操作：如果文件中存在一行代码 `define('APP_DEBUG_MODE', true);` (注意精确匹配，包括分号)，则将其修改为 `define('APP_DEBUG_MODE', false);`。如果该行不存在，则在文件的 `<?php` 标签之后、其他内容之前，新插入一行 `define('APP_DEBUG_MODE', false);`。使用 `edit_file` API进行修改，先 `dryRun` 预览，再实际执行。"}
{"id": 30, "query": "需要对 './file_system_mock_data/task_60/application_logs/' 目录及其所有子目录中的日志文件进行初步审查。首先，在 './file_system_output/task_60/' 下创建一个名为 'audit_reports' 的子目录。然后，扫描所有 '.log' 文件。对于每个大小超过 50 字节 (bytes) 的 '.log' 文件，读取其内容的前3行。将这些符合条件的文件路径（相对于扫描起始目录的路径）及其提取的前3行内容，汇总到一个名为 './file_system_output/task_60/audit_reports/large_logs_preview.txt' 的报告文件中。每个文件条目格式应为：'File: [relative_path]\\n[line1]\\n[line2]\\n[line3]\\n---'"}
{"id": 31, "query": "为新Python项目 \"alpha_project\" 创建一个标准初始目录结构。在 `./file_system_output/task_1/alpha_project/` 路径下创建 `src`, `tests`, `docs` 三个子目录。然后在 `src` 目录下创建一个空的 `__init__.py` 文件，以及一个名为 `main.py` 的文件，其内容为一个打印 \"Hello, Alpha Project!\" 的Python脚本。"}
{"id": 32, "query": "将位于 `./file_system_mock_data/task_2/logs/app.log` 的日志文件移动到 `./file_system_output/task_2/archived_logs/` 目录下，并将其重命名为 `app_backup_20240715.log`。确保目标目录 `./file_system_output/task_2/archived_logs/` 已创建。"}
{"id": 33, "query": "分析 `./file_system_mock_data/task_3/product_sales.csv` 文件。该文件包含 'ProductID', 'Category', 'QuantitySold', 'UnitPrice' 列。计算每个 'Category' 的总收入 (QuantitySold * UnitPrice)，并将结果以JSON格式保存到 `./file_system_output/task_3/category_revenue_report.json`。JSON结构应为 Category名称到总收入的映射。"}
{"id": 34, "query": "更新位于 `./file_system_mock_data/task_4/settings.json` 的应用配置文件。将 `user_interface.theme` 的值从 \"light\" 修改为 \"dark\"，并在 `preferences` 对象内部添加一个新的键值对 `\"notifications_enabled\": false`。将修改直接保存在原文件。"}
{"id": 35, "query": "检查 `./file_system_mock_data/task_5/user_documents/` 目录下的所有 `.txt` 文件。对于每个文件，读取其内容。如果文件内容包含字符串 \"UrgentReviewRequired\"，则将该文件移动到同一目录下的 `needs_review` 子目录中。如果 `needs_review` 子目录不存在，则创建它。文件名在移动后保持不变。"}
{"id": 36, "query": "在 `./file_system_mock_data/task_6/python_app/` 目录及其所有子目录中递归搜索所有的 `.py` 文件。在每个找到的 `.py` 文件中，将所有出现的旧版权声明注释 `\"# (c) 2023 Old Company\"` 替换为新的版权声明 `\"# (c) 2024 New Future Inc.\"`。修改直接在原文件上进行。"}
{"id": 37, "query": "处理位于 `./file_system_mock_data/task_7/server_activity/` 目录下的 `access_log_part1.log`，`access_log_part2.log` 和 `temp_data.tmp` 文件。将这三个文件中所有包含 \"HTTP/1.1\" 404\"` (表示未找到资源的错误请求) 的行提取出来，合并到一个新文件 `./file_system_output/task_7/report/critical_errors.log` 中。在合并后的文件顶部添加一行 `# Critical 404 Errors Report`。确保输出目录存在。"}
{"id": 38, "query": "读取 `./file_system_mock_data/task_8/article.html` 文件。从中提取所有一级标题 (<h1>) 和二级标题 (<h2>) 的文本内容。将提取到的标题按其在HTML中出现的顺序列出，每个标题占一行，保存到 `./file_system_output/task_8/extracted_headings.txt`。"}
{"id": 39, "query": "在 `./file_system_mock_data/task_9/research_papers/` 目录下递归搜索所有 `.txt` 文件，但排除 `drafts` 子目录中的文件。对于每个找到的文件，获取其文件名和大小。如果文件内容包含短语 \"Climate Change Impact\"，则将该文件的信息（文件名，大小，以及首次出现该短语的行号和行内容）记录到一个报告文件 `./file_system_output/task_9/climate_impact_report.json` 中。报告应为JSON数组格式，每个元素是一个包含 `filename`, `size_bytes`, `line_number`, `line_content` 的对象。"}
{"id": 40, "query": "管理员需要审计特定文件的可访问性。首先，获取系统配置的允许操作的根目录列表。然后，针对给定的文件清单：`./file_system_mock_data/task_10/sensitive_data/financials.xlsx`, `./file_system_mock_data/task_10/public_docs/manual.txt`, `./another_root/task_10/system_config.json` (假设 `./another_root/` 不在允许列表中)。为每个文件生成一份报告条目，包含：文件路径 (`filePath`)、是否存在 (`exists`)、是否位于允许的根目录下 (`is_accessible`)、文件类型 (`type`，'file'或'directory') 和大小 (`size_bytes`，如果存在且可访问)。将这些条目汇总成一个JSON数组，保存到 `./file_system_output/task_10/access_audit_report.json`。假设 `list_allowed_directories` 返回 `[\"./file_system_mock_data/\", \"./file_system_output/\"]`。"}
{"id": 41, "query": "在 `./file_system_output/task_21/` 目录下创建一个名为 `project_plan.txt` 的文件，并写入以下内容：\n项目名称：Alpha项目\n项目目标：完成核心模块开发。\n截止日期：2024-12-31。\n责任人：开发团队。\n备注：每周五进行进度汇报。"}
{"id": 42, "query": "获取 `./file_system_mock_data/task_22/app_config.json` 文件的元数据信息，并将文件名、大小和类型（文件/目录）这三项信息写入 `./file_system_output/task_22/file_metadata_report.txt`，格式为 '文件名: [name]\n大小: [size] bytes\n类型: [type]'。"}
{"id": 43, "query": "读取 `./file_system_mock_data/task_23/sensor_data.csv` 文件，该文件包含'timestamp,sensor_id,temperature,humidity'列。筛选出所有'temperature'高于30.0摄氏度的记录行，并将这些筛选后的记录（包括表头）保存到 `./file_system_output/task_23/high_temp_alerts.csv`。"}
{"id": 44, "query": "在 `./file_system_mock_data/task_24/app_settings.json` 文件中，将配置项 `\"enableLogging\": true` 修改为 `\"enableLogging\": false`，并将 `\"logLevel\": \"debug\"` 修改为 `\"logLevel\": \"info\"`。首先使用 `dryRun` 模式预览更改，确认无误后实际应用这些编辑到原文件。"}
{"id": 45, "query": "将 `./file_system_mock_data/task_25/source_code/utils.py` 文件移动到 `./file_system_output/task_25/common_libs/` 目录下，并重命名为 `helpers.py`。之后，在 `./file_system_output/task_25/` 目录下创建一个 `migration_log.txt` 文件，记录此次操作：'Moved utils.py to common_libs/helpers.py and renamed.'"}
{"id": 46, "query": "在 `./file_system_mock_data/task_26/project_archive/` 目录及其所有子目录中，递归搜索所有扩展名为 `.log` 的文件，但排除所有名为 `temp_ignore.log` 的文件。将找到的符合条件的日志文件完整路径列表（每行一个路径）写入到 `./file_system_output/task_26/active_logs_list.txt`。"}
{"id": 47, "query": "扫描 `./file_system_mock_data/task_27/server_logs/` 目录及其子目录中所有以 `.log` 结尾的文件。对每个找到的日志文件，读取其内容并统计其中字符串 'FATAL_ERROR' 出现的次数。将统计结果汇总到一个JSON文件 `./file_system_output/task_27/fatal_error_summary.json` 中，格式为：`{\"filepath1\": count1, \"filepath2\": count2, ...}`。如果一个文件不包含 'FATAL_ERROR'，则不应出现在JSON结果中。"}
{"id": 48, "query": "为新的Web前端项目创建一个标准目录结构和基础文件。项目根目录为 `./file_system_output/task_28/my_cool_webapp/`。在此根目录下，创建 `index.html` 文件，以及 `assets/css/` 和 `assets/js/` 两个子目录。在 `assets/css/` 目录下创建 `style.css` 文件，在 `assets/js/` 目录下创建 `app.js` 文件。\n`index.html` 内容：包含HTML5基本骨架，标题为 'My Cool Webapp'，并正确链接到 `assets/css/style.css` 和 `assets/js/app.js`。\n`style.css` 内容：设置body元素的背景颜色为 `#e0e0e0`，并为h1元素设置蓝色字体颜色 `#0000FF`。\n`app.js` 内容：包含一条JavaScript语句，在页面加载完成后向控制台输出 'App initialized and running!'。"}
{"id": 49, "query": "读取位于 `./file_system_mock_data/task_29/product_catalog.xlm` (一个模拟的XML格式文件) 的内容。从中提取所有 `<product>` 标签下的 `<id>` 和 `<price>` 标签的值。将这些提取出的产品ID和价格信息转换为JSON数组格式，每个对象包含 'productId' 和 'productPrice' 两个键，并保存到 `./file_system_output/task_29/product_prices.json` 文件中。处理完成后，将原始的 `product_catalog.xlm` 文件移动到 `./file_system_output/task_29/archive/` 目录下，并重命名为 `product_catalog_backup.xlm`。"}
{"id": 50, "query": "获取所有当前环境允许访问的根目录列表。对于列表中的每一个目录，获取其详细元数据（类型、大小、修改时间戳）。将这些信息汇总到一个报告文件 `./file_system_output/task_30/accessible_dirs_audit.txt`。报告中每行代表一个目录，格式为 '路径: [path] | 类型: [type] | 大小: [size] bytes | 修改时间: [mod_time_iso]'。特别地，如果发现允许访问的目录中包含路径 `./file_system_mock_data/task_30/restricted_zone/`，则在此目录下额外创建一个名为 `AUDIT_FLAG.tmp` 的空文件作为标记。"}
{"id": 51, "query": "扫描Python项目目录 `./file_system_mock_data/task_61/project_alpha/`，找出所有`.py`文件。对于每个文件，如果文件中包含旧的API调用 `old_api_function()`，将其替换为 `new_api_function(param1, param2)`。同时，将所有被修改的文件完整路径记录到一个日志文件 `./file_system_output/task_61/refactor_log.txt`。"}
{"id": 52, "query": "将 `./file_system_mock_data/task_62/logs/app.log` 文件移动到 `./file_system_output/task_62/archived_logs/` 目录下，并重命名为 `app_archived.log`。"}
{"id": 53, "query": "创建一个标准的Web项目结构。根目录为`./file_system_output/task_63/my_web_project/`。在此目录下创建`css`, `js`, `html`三个子目录。然后在`html`目录下创建一个`index.html`文件，包含基本的HTML骨架和标题“我的网页”；在`css`目录下创建一个`style.css`文件，包含一个将body背景颜色设为`#f0f0f0`的样式；在`js`目录下创建一个`script.js`文件，包含一个打印“脚本已加载”到控制台的语句。最后，列出`./file_system_output/task_63/my_web_project/`的目录结构，并将此结构列表（每行一个文件或目录项，带[FILE]或[DIR]前缀）写入到`./file_system_output/task_63/structure.txt`。"}
{"id": 54, "query": "读取 `./file_system_mock_data/task_64/data/sales_q1.csv` 和 `./file_system_mock_data/task_64/data/sales_q2.csv` 两个CSV文件。将它们的内容合并（Q1数据在前，Q2数据在后，假设表头在两个文件中都存在且相同，最终合并的文件只保留一份表头），并将合并后的数据写入到 `./file_system_output/task_64/consolidated_sales.csv`。同时，生成一个摘要报告 `./file_system_output/task_64/summary.txt`，报告内容为“数据合并完成，共处理X行数据（不含表头）。”，其中X是两个文件数据行的总和。"}
{"id": 55, "query": "查找 `./file_system_mock_data/task_65/configs/` 目录下所有名为 `server_config.json` 的配置文件。对于每个找到的配置文件：1. 在同一目录下创建一个备份，将原始文件复制并命名为 `server_config.json.bak`。2. 然后，读取原 `server_config.json` 文件，将其中的 `\"debug_mode\": true` 修改为 `\"debug_mode\": false`，并将 `\"port\": 8080` 修改为 `\"port\": 8081`。3. 将修改后的内容写回原 `server_config.json` 文件。4. 将所有修改过的原始文件路径（例如 `./file_system_mock_data/task_65/configs/production/server_config.json`）记录到 `./file_system_output/task_65/update_log.txt`。"}
{"id": 56, "query": "在 `./file_system_output/task_66/notes/` 目录下创建一个名为 `meeting_summary.txt` 的文件。文件内容应包含会议日期“2024-07-30”，参与者“张三, 李四, 王五”，以及主要讨论点“项目A进展顺利，项目B遇到技术瓶颈，下周需组织技术攻关会议。”。"}
{"id": 57, "query": "读取 `./file_system_mock_data/task_67/system.log` 文件。提取所有包含关键词 \"ERROR\" 或 \"CRITICAL\" 的行（区分大小写），并将这些行按原顺序写入到 `./file_system_output/task_67/critical_alerts.log`。如果未找到任何匹配行，则输出文件应包含 \"No critical alerts found in the log.\"。"}
{"id": 58, "query": "比较 `./file_system_mock_data/task_68/version1/report.txt` 和 `./file_system_mock_data/task_68/version2/report.txt` 两个文件的内容。生成一个差异报告 `./file_system_output/task_68/diff_report.json`。报告格式为JSON，包含 `{\"file1_path\": \"./file_system_mock_data/task_68/version1/report.txt\", \"file2_path\": \"./file_system_mock_data/task_68/version2/report.txt\", \"are_identical\": boolean, \"lines_in_file1_only\": [\"line_content1\", ...], \"lines_in_file2_only\": [\"line_contentA\", ...]}`。只报告完全匹配的行之外的差异行，即只存在于文件1中的行和只存在于文件2中的行。"}
{"id": 59, "query": "读取 `./file_system_mock_data/task_69/config.php` 文件。该文件包含一个PHP数组定义，例如 `$app_config = ['db_host' => 'localhost', 'db_user' => 'root', 'db_pass' => 'secret', 'debug_mode' => true];`。提取这个 `$app_config` 数组的内容，并将其转换为JSON格式，键名和值保持不变（PHP的true应转为JSON的true），保存到 `./file_system_output/task_69/config.json`。"}
{"id": 60, "query": "首先，列出服务器允许操作的所有根目录。然后，在 `./file_system_mock_data/task_70/source_files/` 目录下，找出所有扩展名为 `.tmp` 的临时文件，但不包括那些文件名中包含 `_keep` 的文件。将这些找到的 `.tmp` 文件（不含 `_keep`）移动到 `./file_system_output/task_70/temporary_archive/` 目录。移动完成后，获取 `./file_system_mock_data/task_70/source_files/` 目录中所有剩余的 `.log` 文件的详细信息（文件名、大小和人类可读的修改时间YYYY-MM-DD HH:MM:SS格式），并将这些信息汇总到一个名为 `log_files_report.txt` 的报告文件中，存放在 `./file_system_output/task_70/` 目录。报告中每个log文件信息占一行，格式为 '文件名: [filename], 大小: [size_bytes] bytes, 修改时间: [mod_time]'。"}
{"id": 61, "query": "在 `./file_system_mock_data/task_41/server_logs/` 目录中，有多个格式为 `app_YYYYMMDD.log` 的日志文件。请将这些日志文件按年份和月份归档到 `./file_system_output/task_41/archived_logs/YYYY/MM/` 结构下。例如，`app_20230115.log` 应移动到 `./file_system_output/task_41/archived_logs/2023/01/app_20230115.log`。"}
{"id": 62, "query": "在Python项目 `./file_system_mock_data/task_42/my_project/` 中，需要将所有模块（.py文件）中的旧函数 `calculate_sum(a, b)` 调用替换为新函数 `compute_total(val1, val2)`。在进行实际替换前，先对 `utils/math_ops.py` 文件进行试运行（dryRun）查看变更。确认无误后，对项目内所有 `.py` 文件执行实际替换。同时，在修改任何文件前，将整个 `my_project` 目录备份到 `./file_system_output/task_42/backup/my_project_backup_YYYYMMDDHHMMSS/` (YYYYMMDDHHMMSS为当前时间戳，此处用占位符表示，模型应能动态生成)。"}
{"id": 63, "query": "为新启动的个人博客项目创建一个基本的网页结构。在 `./file_system_output/task_43/my_blog/` 目录下，创建一个 `index.html` 文件和一个 `style.css` 文件。`index.html` 应包含一个标题 '我的个人博客' 和一个段落 '欢迎来到我的博客！'，并且它需要链接到 `style.css`。`style.css` 文件中为 `body` 设置背景色为 `#f0f0f0`，为 `h1` 标题设置颜色为 `#333`。"}
{"id": 64, "query": "处理位于 `./file_system_mock_data/task_44/employee_data.csv` 的员工数据。筛选出 'Engineering' 部门所有员工的姓名（Name）和电子邮件（Email），并将结果保存到一个新的文本文件 `./file_system_output/task_44/engineering_contacts.txt` 中，每行格式为 '姓名: 电子邮件'。"}
{"id": 65, "query": "在一个项目中，配置文件分散在 `./file_system_mock_data/task_45/configs/` 目录下的多个子目录中 (如 `module_a/`, `module_b/`)，均为JSON格式。需要递归搜索这些子目录，读取所有名为 `config.json` 的文件，提取其中的 `service_url` 和 `timeout` 字段。将所有找到的这些配置参数汇总到一个 `./file_system_output/task_45/compiled_config.json` 文件中，格式为 `{\"module_name\": {\"service_url\": \"...\", \"timeout\": ...}}`。如果某个 `config.json` 文件不存在或者缺少某个字段，应在汇总结果中忽略该模块的条目，并继续处理其他文件。"}
{"id": 66, "query": "在 `./file_system_mock_data/task_46/php_website/includes/` 目录下的所有PHP (`.php`) 文件中，将页脚版权信息中的年份更新为 '2024'。旧的年份可能是 '2022', '2023', 或其他旧年份，但总是以 '© YYYY' 的格式出现。例如, '© 2023 YourCompanyName' 应变为 '© 2024 YourCompanyName'。"}
{"id": 67, "query": "为新的前端Web应用项目 `./file_system_output/task_47/my_web_app/` 初始化标准目录结构。需要创建 `css`, `js`, 和 `assets` 三个子目录。并在 `js` 目录下创建一个空的 `app.js` 文件, 在 `css` 目录下创建一个空的 `styles.css` 文件。同时, 在根目录 `my_web_app`下创建一个 `index.html` 文件，内容为基本的HTML骨架，包含标题 'My New App' 并引用 `css/styles.css` 和 `js/app.js`。"}
{"id": 68, "query": "为新的Python项目 'MyWebApp' 创建一个基本的目录结构。具体来说，创建 './file_system_output/task_81/MyWebApp/' 作为项目根目录，然后在根目录下创建 'app' 和 'tests' 两个子目录。最后，在 'app' 目录下创建一个空的 '__init__.py' 文件。"}
{"id": 69, "query": "在'./file_system_mock_data/task_82/raw_logs/'目录下有多个日志文件（'system_2023-10-01.log', 'application_2023-10-02.log'）。请将这些日志文件的内容合并到一个名为'master_combined_logs.txt'的文件中，该文件应存放在新建的'./file_system_output/task_82/archived_logs/'目录。合并完成后，将原始日志文件移动到新建的'./file_system_output/task_82/processed_originals/'目录。"}
{"id": 70, "query": "有一个INI配置文件 './file_system_mock_data/task_83/app_settings.ini'。需要对其进行两处修改：1. 将 '[logging]' 部分下的 'loglevel = INFO' 修改为 'loglevel = DEBUG'。2. 将 '[database]' 部分下的 'max_connections = 100' 修改为 'max_connections = 250'。首先对这两处修改进行一次合并的试运行（dryRun），预览更改。然后，将这些更改实际应用到新文件 './file_system_output/task_83/updated_app_settings.ini'中。"}
{"id": 71, "query": "在一个PHP项目'./file_system_mock_data/task_84/php_project/'中，所有'.php'文件内的PHP命名空间引用'OldProject\\Library\\Utils'需要被替换为'NewProject\\Core\\Helpers'。同时，需要查找所有原始文件中实际包含了'OldProject\\Library\\Utils'这个字符串的PHP文件，并将这些被修改文件的原始路径列表记录到一个'./file_system_output/task_84/refactor_impact_report.txt'文件中，每个文件路径占一行。所有PHP文件（无论是否修改）都应被复制到新的输出目录'./file_system_output/task_84/php_project_refactored/'下，保持其原有的相对目录结构，修改只在输出目录的文件上进行。"}
{"id": 72, "query": "读取位于 './file_system_mock_data/task_85/inventory_levels.csv' 的CSV文件。该文件包含列：'ItemID', 'ItemName', 'CurrentStock', 'WarehouseLocation'。请提取所有 'CurrentStock' 低于 20 的商品信息，并将这些信息（包括所有列）转换为JSON数组格式，每个商品一个JSON对象。将结果保存到新建目录 './file_system_output/task_85/alerts/'下的 'low_stock_alert.json' 文件中。"}
{"id": 73, "query": "检查'./file_system_mock_data/task_86/staging_area/'目录。将所有以'.tmp'和'.bak'为扩展名的文件移动到新建的'./file_system_output/task_86/cleanup_archive/'目录。如果'cleanup_archive'目录不存在，则创建它。最后，列出'./file_system_output/task_86/cleanup_archive/'目录中的文件和目录。"}
{"id": 74, "query": "在一个项目 './file_system_mock_data/task_87/project_docs/' 中，包含多个子目录和文档文件。\n1. 递归搜索所有'.txt'和'.py'文件。\n2. 对于每个找到的文件：如果其内容包含字符串 'ALPHA_RELEASE_CANDIDATE'，则将其替换为 'BETA_STABLE_VERSION'。此更改应直接在原始文件上进行，首先进行一次包含所有此类编辑的整体dryRun预览，然后再进行实际的修改。\n3. 将所有被修改过的文件的完整路径列表（每行一个路径）保存到 './file_system_output/task_87/modification_log.txt' 文件中。\n4. 获取 './file_system_mock_data/task_87/project_docs/config/settings.json' 文件的元数据信息，并将此信息（以可读格式）追加到'./file_system_output/task_87/modification_log.txt'的末尾。"}
{"id": 75, "query": "在 './file_system_mock_data/task_88/user_uploads/' 目录下，查找所有的 '.txt' 和 '.log' 文件。将这些找到的文件复制一份到新建的 './file_system_output/task_88/archived_copies/' 目录中，保持原文件名。复制操作完成后，获取 './file_system_output/task_88/archived_copies/' 目录的文件列表（仅文件名），并将此列表以及一句确认信息 'Archive copy process completed for TXT and LOG files.' 记录到 './file_system_output/task_88/archive_summary.xlm' 文件中（注意是.xlm扩展名）。"}
{"id": 76, "query": "处理分布在三个数据文件中的员工绩效数据：'./file_system_mock_data/task_89/employee_data.json' (包含EmployeeID, Name, Department), './file_system_mock_data/task_89/q3_reviews.csv' (EmployeeID, Q3Rating), 和 './file_system_mock_data/task_89/q4_reviews.csv' (EmployeeID, Q4Rating)。\n1. 读取这三个文件的内容。\n2. 对于每个员工，计算Q3和Q4的平均绩效评分 (Q3Rating 和 Q4Rating 均为1-5的数值)。\n3. 生成一个JSON报告 './file_system_output/task_89/performance_reports/annual_summary.json'，其中包含每个员工的EmployeeID, Name, Department, AverageRating。只包括平均评分大于等于3.5的员工。\n4. 同时，创建一个文本文件 './file_system_output/task_89/performance_reports/high_performers.txt'，列出所有平均评分大于等于4.5的员工的姓名和部门 (格式：'Name (Department)')，每人一行。\n5. 确保输出目录 './file_system_output/task_89/performance_reports/' 存在，并且列出允许文件操作的根目录。"}
{"id": 77, "query": "有一个HTML页面模板 './file_system_mock_data/task_90/article_template.html' 和一个包含文章数据的数据文件 './file_system_mock_data/task_90/article_data.json'。\n模板包含占位符如 `{{ARTICLE_TITLE}}`, `{{AUTHOR_NAME}}`, 和 `{{ARTICLE_BODY}}`。\n数据文件提供这些占位符的实际内容。\n1. 首先，对原始模板文件 './file_system_mock_data/task_90/article_template.html' 进行一次试运行编辑 (dryRun)，尝试将占位符 `{{AUTHOR_NAME}}` 替换为 `{{CONTENT_CREATOR}}`，但不实际保存此更改。\n2. 然后，读取原始模板文件和数据文件，使用JSON数据中的值完整替换模板中所有相应的占位符 (`{{ARTICLE_TITLE}}`, `{{AUTHOR_NAME}}`, `{{ARTICLE_BODY}}`)。\n3. 将最终生成的HTML内容保存到 './file_system_output/task_90/published_article.html'。\n4. 确保输出目录 './file_system_output/task_90/' 存在。"}
{"id": 78, "query": "在 `./file_system_mock_data/task_71/` 目录下有一个名为 `draft_report.txt` 的文件。请将其重命名为 `final_report.bak` 并移动到 `./file_system_output/task_71/archived_reports/` 目录中。"}
{"id": 79, "query": "请在 `./file_system_output/task_72/` 目录下创建一个名为 `meeting_minutes.txt` 的会议纪要文件。会议主题是 '季度规划会议'，日期是 '2024-07-28'。纪要需包含以下讨论点：1. 上季度目标回顾与成果。2. 本季度重点任务分配。3. 风险评估与应对措施。4. 下次会议时间。请确保文件内容至少包含这四个要点，并有适当的细节描述。"}
{"id": 80, "query": "我有一个Python项目，其主要逻辑在 `./file_system_mock_data/task_73/source_code/app.py`，辅助函数在 `utils.py`。请执行以下操作：1. 在 `./file_system_output/task_73/refactored_app/` 目录下创建输出结构。2. 将 `app.py` 复制到新目录。3. 使用 `edit_file` 工具对新目录中的 `app.py` 进行修改：将所有对旧函数 `calculate_sum` 的调用改为 `compute_total`，并在文件末尾添加一个新函数 `log_message(message)`，其内容为 `print(f\"LOG: {message}\")`。4. 将原项目中的 `utils.py` 文件也复制到 `./file_system_output/task_73/refactored_app/` 目录。"}
{"id": 81, "query": "我有一个目录 `./file_system_mock_data/task_74/data_exports/` 包含多个CSV文件。我只对体积大于50字节的CSV文件感兴趣。请将这些大于50字节的CSV文件复制到 `./file_system_output/task_74/large_exports/` 目录中。并在 `./file_system_output/task_74/report.txt` 中列出被复制的文件名及其大小，每行一个文件，格式为 '文件名: 大小字节'。"}
{"id": 82, "query": "请为我的新PHP Web项目创建一个基本的目录结构。在 `./file_system_output/task_75/my_php_project/` 路径下创建 `public`、`app` 和 `config` 三个子目录。然后在 `public` 目录下再创建一个 `assets` 子目录。"}
{"id": 83, "query": "我需要更新我们公司网站的版权年份。请搜索 `./file_system_mock_data/task_76/website_files/` 目录及其子目录下所有的 `.html`, `.js` 和 `.css` 文件。在这些文件中，将所有出现的 'Copyright 2023' 替换为 'Copyright 2024'，将 '© 2023' 替换为 '© 2024'。将修改后的文件保存到 `./file_system_output/task_76/updated_website_files/` 对应的目录结构中。确保原始文件不被修改。"}
{"id": 84, "query": "我有一个JSON配置文件 `./file_system_mock_data/task_77/service_config.json`。请读取此文件，提取 `api_details.url`，`api_details.key` 和 `database_connection.username` 的值。将这些信息格式化并保存到 `./file_system_output/task_77/connection_params.txt` 文件中，每行一个参数，格式为 '参数名: 值'，例如：\nAPI_URL: [url_value]\nAPI_KEY: [key_value]\nDB_USER: [username_value]"}
{"id": 85, "query": "请分析 `./file_system_mock_data/task_78/web_server.log` 文件。统计每种HTTP请求方法（如GET, POST）的出现次数，并找出出现频率最高的三个IP地址及其访问次数。将结果保存到 `./file_system_output/task_78/log_analysis_report.json` 文件中，格式应为：`{\"http_method_counts\": {\"GET\": count, \"POST\": count, ...}, \"top_3_ips\": [{\"ip\": \"ip_address1\", \"count\": count1}, {\"ip\": \"ip_address2\", \"count\": count2}, {\"ip\": \"ip_address3\", \"count\": count3}]}`。如果不足三个IP，则列出所有。"}
{"id": 86, "query": "我有一些PHP脚本在 `./file_system_mock_data/task_79/php_module/` 目录下。请为 `user_service.php` 和 `order_service.php` 这两个文件添加标准的文件头部注释。注释内容如下：\n```php\n<?php\n/**\n * Module: [MODULENAME]\n * File: [FILENAME]\n * Author: AutoSys\n * Date: 2024-07-29\n * Description: This file is part of the [MODULENAME] module.\n */\n```\n请将 `[MODULENAME]` 替换为 'CoreServices'，并将 `[FILENAME]` 替换为实际的文件名 (例如 'user_service.php')。修改后的文件应保存在 `./file_system_output/task_79/documented_scripts/` 目录，保持原文件名，确保原始文件不被修改。"}
{"id": 87, "query": "我需要更新一个JSON配置文件。首先，请使用`list_allowed_directories`告诉我你被允许操作的根目录有哪些。然后，在 `./file_system_mock_data/task_80/configurations/` 目录下有一个 `current_settings.json` 和一个描述更新指令的 `update_instructions.txt`。请根据 `update_instructions.txt` 的内容修改 `current_settings.json`，将修改后的配置保存为 `new_settings.json`到 `./file_system_output/task_80/` 目录。同时，使用 `edit_file` 的 `dryRun: true` 功能生成一个显示这些更改的diff报告（Git风格），并将此diff报告保存为 `settings_changes.diff.txt` 到同一输出目录。原始的 `current_settings.json` 不应被修改。"}
{"id": 88, "query": "在我负责的Python项目 `./file_system_mock_data/task_91/my_python_lib/` 中，需要进行一次小重构。首先，将所有以 `helper_` 开头的 `.py` 文件名中的 `helper_` 前缀移除 (例如 `helper_math.py` 变为 `math.py`)。然后，在这些被重命名的文件中，将所有出现的字符串 `legacy_api_call` 替换为 `new_api_call`。最后，将这些修改过的文件移动到 `./file_system_output/task_91/refactored_lib/` 目录下。请确保输出目录存在。"}
{"id": 89, "query": "我需要分析散落在 `./file_system_mock_data/task_92/server_logs/` 目录下不同子目录（如 `node1/`，`node2/`）中的所有 `.log` 文件。请收集这些日志文件中所有包含 'CRITICAL' 或 'FATAL' 关键字的日志条目。将这些条目汇总到 `./file_system_output/task_92/critical_alerts.log` 文件中。在汇总文件的开头，请添加一行统计信息，格式为 '汇总日期: YYYY-MM-DD HH:MM:SS | 总计严重条目: N'。如果某些日志文件不存在或为空，应跳过它们。"}
{"id": 90, "query": "为我的新PHP Web应用创建一个标准的初始项目结构。在 `./file_system_output/task_93/my_php_app/` 目录下，创建 `public/`、`src/` 和 `templates/` 三个子目录。然后在 `public/` 目录下创建一个名为 `index.php` 的空文件，并在 `src/` 目录下创建一个名为 `config.php` 的空文件。"}
{"id": 91, "query": "我有一个HTML模板文件 `./file_system_mock_data/task_94/email_template.html`。请读取这个模板，将其中的占位符 `{{USER_NAME}}` 替换为 '尊敬的客户'，`{{PROMO_MESSAGE}}` 替换为 '本周所有产品享受九折优惠！'。将修改后的内容保存到 `./file_system_output/task_94/generated_emails/promo_email.html`。请确保输出目录已创建。"}
{"id": 92, "query": "请比较 `./file_system_mock_data/task_95/versions/v1.0/app_config.json` 和 `./file_system_mock_data/task_95/versions/v1.1/app_config.json` 两个JSON配置文件。识别它们之间的差异，并将差异内容（逐行对比，指出哪个文件包含哪行）写入到 `./file_system_output/task_95/reports/config_diff_v1.0_vs_v1.1.txt`。同时，获取这两个原始配置文件的文件大小和类型信息，并将这些元数据追加到差异报告文件的末尾，格式为 '文件路径: [路径], 大小: [大小] bytes, 类型: [类型]'。"}
{"id": 93, "query": "请检查 `./file_system_mock_data/task_96/temp_uploads/` 目录。首先列出该目录下的所有内容（文件和子目录）。然后，将该目录中所有以 `.tmp` 结尾的文件移动到 `./file_system_output/task_96/archived_tmp/` 目录下。如果归档目录不存在，请创建它。"}
{"id": 94, "query": "我有几份网页存档在 `./file_system_mock_data/task_97/archived_pages/` 目录下。请扫描该目录及其子目录下的所有 `.html` 文件，从中提取所有唯一的电子邮件地址（符合标准格式，如 `<EMAIL>`）。将这些提取到的唯一电子邮件地址列表写入到 `./file_system_output/task_97/extracted_emails.txt` 文件中，每行一个邮件地址。"}
{"id": 95, "query": "我需要快速搭建一个前端JavaScript项目的基础结构。请在 `./file_system_output/task_98/my_js_project/` 目录下创建以下结构：1. `src/` 目录，并在其中创建 `app.js` (空文件) 和 `utils/` 子目录。2. `public/` 目录，并在其中创建 `index.html` (包含基本的HTML5骨架) 和 `css/` 子目录，`css/`子目录中再创建一个 `style.css` (空文件)。3. 在项目根目录下 (`./file_system_output/task_98/my_js_project/`) 创建一个 `.gitignore` 文件，其内容应包含 `node_modules/` 和 `dist/`。开始操作前，请先列出服务器允许操作的所有根目录。"}
{"id": 96, "query": "我需要更新一个PHP文件 `./file_system_mock_data/task_99/api_client.php` 中的数据库连接函数。请将所有对旧函数 `old_db_connect('localhost', 'user')` 的调用（注意参数可能会变化，但函数名是固定的 `old_db_connect`）替换为新函数调用 `Database::getInstance()->connect('new_host', 'new_user', 'secure_password')`。在实际应用更改之前，请先执行一次试运行（dry run）来预览将发生的更改。如果试运行显示了预期的修改，再实际执行编辑操作。将修改后的文件保存在原位。"}
{"id": 97, "query": "我有一个产品目录CSV文件 `./file_system_mock_data/task_100/catalog_data.csv`。请将其内容转换为JSON格式。在转换过程中，只包含那些 'Availability' 列为 'In Stock' 且 'Price' (numeric part) 大于 30.00 的产品。输出的JSON对象中，CSV的 'ProductID' 列应重命名为 'sku'，'ProductName' 重命名为 'name'，'Category' 重命名为 'group'，'Price' 列的值应只保留数字部分并转换为数字类型。将最终的JSON数组保存到 `./file_system_output/task_100/json_exports/filtered_catalog.json`。操作完成后，获取原始CSV文件和生成的JSON文件的元数据信息（包括大小，类型，创建时间和修改时间），并将这些信息格式化后写入 `./file_system_output/task_100/reports/conversion_metadata.txt`。"}
