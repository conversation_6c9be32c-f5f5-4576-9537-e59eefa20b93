#!/usr/bin/env python3
"""
民宿预订场景自动评估检查脚本 v2
参考check.py的框架结构，保留checker_hotel_v1.py的检查逻辑
"""

import json
import os
import hashlib
import time
import warnings
import re
import logging
import argparse
import sys
from pathlib import Path
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple

import litellm
from litellm import completion

# 过滤不必要的警告
warnings.filterwarnings("ignore", category=UserWarning, module="pydantic")
warnings.filterwarnings("ignore", message=".*PydanticSerializationUnexpectedValue.*")

# 配置日志
logging.basicConfig(level=logging.INFO)

# LLM模板 - 来自checker_hotel_v1.py
llm_template = """
你是一位严谨的AI智能体任务评估专家。你的任务是根据给定的[任务说明]和[待执行的检查项]，评估[待评估的智能体响应]是否满足所有要求。
每个检查项包含"检查目标"和可选的"必须包含的关键内容"。

[任务说明]:
{query}

[待评估的智能体响应]:
{response}

[待执行的检查项]:
{check_list_str}

---
你的回答必须是一个单一、合法的JSON对象，不包含任何其他文字、说明或Markdown代码块。
JSON对象必须包含一个名为 "results" 的键，其值是一个列表。
列表中的每个对象都应按顺序对应一个检查项。
每个对象必须包含两个键：
1. "analysis": (字符串) 你对该检查项的详细分析和推理过程。请结合"检查目标"和"关键内容"进行判断。
2. "conclusion": (字符串) 你的最终结论，值必须是 "合格" 或 "不合格"。

JSON格式示例:
{{
  "results": [
    {{
      "analysis": "响应内容满足'推荐别墅'的检查目标，并准确包含了关键内容'青岛海边度假别墅'，符合要求。",
      "conclusion": "合格"
    }},
    {{
      "analysis": "响应中虽然提到了房源，但未能找到关键内容'评分 4.9'或相关的高评分信息，不符合检查目标。",
      "conclusion": "不合格"
    }}
  ]
}}
"""

def request_llm_with_litellm(messages, model_name, api_base, api_key, max_retries=3):
    """
    使用LiteLLM调用模型 
    """
    # 设置API配置
    if api_base:
        litellm.api_base = api_base
    if api_key:
        litellm.api_key = api_key
    
    # 转换消息格式
    formatted_messages = []
    for msg in messages:
        formatted_messages.append({
            "role": msg["role"] if msg["role"] != "user" else "user",
            "content": msg["utterance"] if "utterance" in msg else msg["content"]
        })
    
    for attempt in range(max_retries):
        try:
            response = completion(
                model=model_name,
                messages=formatted_messages,
                response_format={"type": "json_object"},
                api_base=api_base,
                api_key=api_key,
                custom_llm_provider="openai"
            )
            
            content = response.choices[0].message.content
            print(f"LLM调用成功: {content}")
            return True, content
            
        except Exception as e:
            if "response_format" in str(e):
                logging.warning("模型或API不支持JSON模式，回退到文本模式。")
                try:
                    response = completion(
                        model=model_name,
                        messages=formatted_messages,
                        api_base=api_base,
                        api_key=api_key
                    )
                    return True, response.choices[0].message.content
                except Exception as fallback_e:
                    e = fallback_e
            
            print(f"LLM调用失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt == max_retries - 1:
                raise e
            time.sleep(2 ** attempt)  # 指数退避


def safe_json_extract(content: Optional[str]) -> List[Dict]:
    """
    从响应文本中安全提取JSON，支持JSONL格式解析
    """
    if not isinstance(content, str) or not content.strip():
        return []
    
    records = []
    lines = content.strip().split('\n')
    
    for i, line in enumerate(lines):
        if not line.strip():
            continue
            
        try:
            # 尝试直接解析单行JSON
            record = json.loads(line)
            records.append(record)
        except json.JSONDecodeError:
            # 如果单行解析失败，尝试提取JSON代码块
            try:
                json_pattern = r'```json\s*(.*?)\s*```'
                match = re.search(json_pattern, line, re.DOTALL)
                if match:
                    record = json.loads(match.group(1))
                    records.append(record)
                    continue
            except (json.JSONDecodeError, AttributeError):
                pass
            
            # 尝试提取大括号内容
            try:
                brace_pattern = r'\{.*\}'
                match = re.search(brace_pattern, line, re.DOTALL)
                if match:
                    record = json.loads(match.group())
                    records.append(record)
                    continue
            except (json.JSONDecodeError, AttributeError):
                pass
            
            # 如果所有方法都失败，记录警告并跳过
            logging.warning(f"JSONL文件第 {i+1} 行解析错误，已跳过。内容: {line[:100]}...")
    
    logging.info(f"成功解析 {len(records)} 条记录，跳过 {len(lines) - len(records)} 条无效记录")
    return records


def safe_json_extract_single(response_text: str) -> Dict:
    """
    从响应文本中安全提取单个JSON对象，用于LLM响应解析
    """
    if not response_text:
        raise ValueError("响应文本为空")
    
    # 尝试直接解析
    try:
        return json.loads(response_text)
    except json.JSONDecodeError:
        pass
    
    # 尝试提取JSON代码块
    try:
        json_pattern = r'```json\s*(.*?)\s*```'
        match = re.search(json_pattern, response_text, re.DOTALL)
        if match:
            return json.loads(match.group(1))
    except (json.JSONDecodeError, AttributeError):
        pass
    
    # 尝试提取大括号内容
    try:
        brace_pattern = r'\{.*\}'
        match = re.search(brace_pattern, response_text, re.DOTALL)
        if match:
            return json.loads(match.group())
    except (json.JSONDecodeError, AttributeError):
        pass
    
    # 如果所有方法都失败，抛出异常
    raise ValueError(f"无法从响应中提取有效JSON: {response_text[:200]}...")


class JSONLDataChecker:
    """JSONL数据文件状态检查器 - 来自checker_hotel_v1.py"""
    PK_MAP = {
        "bookings.jsonl": ["listing_id","booking_code"],
        "listings.jsonl": ["listing_code","id"],
        "ratings.jsonl": ["review_id","booking_code", "id"],
        "listing_calendar.jsonl": ["listing_id", "date"]
    }

    def __init__(self, env_before: List[Dict], env_after: List[Dict]):
        self.env_before = {item['path']: item for item in env_before}
        self.env_after = {item['path']: item for item in env_after}
        self.data_before = {path: safe_json_extract(item.get('content')) for path, item in self.env_before.items()}
        self.data_after = {path: safe_json_extract(item.get('content')) for path, item in self.env_after.items()}
    
    def _find_record_by_keys(self, records: List[Dict], identifier_dict: Dict) -> Optional[Dict]:
        """
        根据一个包含一个或多个键值对的字典，在记录列表中查找匹配的记录。
        """
        if not identifier_dict:
            return None
            
        for record in records:
            is_match = all(record.get(k) == v for k, v in identifier_dict.items())
            if is_match:
                return record
        return None

    def check_data_hash(self, check_item: Dict) -> Dict:
        """检查文件哈希值"""
        target_file = check_item.get("target")
        expected_hash_or_flag = check_item.get("value")

        # 处理列表格式的value（多文件检查）
        if isinstance(expected_hash_or_flag, list):
            failed_files = []
            for file_spec in expected_hash_or_flag:
                if isinstance(file_spec, dict):
                    if "target_file" in file_spec:
                        file_name = file_spec["target_file"]
                    else:
                        file_name = list(file_spec.keys())[0] if file_spec else None
                    if file_name:
                        before_info = self.env_before.get(file_name)
                        after_info = self.env_after.get(file_name)
                        
                        if not before_info or not after_info:
                            failed_files.append(f"{file_name}(文件未找到)")
                            continue
                            
                        hash_before = before_info.get('hash_value')
                        hash_after = after_info.get('hash_value')
                        
                        if hash_before != hash_after:
                            failed_files.append(f"{file_name}(hash值已改变)")
            
            if failed_files:
                return {"检查结论": "不合格", "原因": "部分文件hash值检查失败", "详情": f"失败的文件: {', '.join(failed_files)}"}
            else:
                return {"检查结论": "合格", "原因": "所有文件hash值未改变", "详情": "所有指定文件的hash值前后一致"}

        # 处理单文件检查
        after_info = self.env_after.get(target_file)
        if not after_info:
            return {"检查结论": "不合格", "原因": "文件未找到", "详情": f"执行后环境数据中找不到文件 {target_file}"}
        
        # 场景一: 检查哈希值是否应保持不变
        if expected_hash_or_flag is True or expected_hash_or_flag == "":
            before_info = self.env_before.get(target_file)
            if not before_info:
                return {"检查结论": "不合格", "原因": "文件未找到", "详情": f"执行前环境数据中找不到文件 {target_file}"}
            
            hash_before_val = before_info.get('hash_value')
            hash_after_val = after_info.get('hash_value')
            
            if hash_before_val == hash_after_val:
                return {"检查结论": "合格", "原因": "数据hash值未改变", "详情": f"文件 {target_file} 的hash值前后一致。"}
            else:
                return {"检查结论": "不合格", "原因": "数据hash值已改变", "详情": f"文件 {target_file} 的hash值发生改变。之前: {hash_before_val}, 之后: {hash_after_val}"}

        # 场景二: 检查哈希值是否为某个特定值
        actual_hash = after_info.get("hash_value")
        if actual_hash == expected_hash_or_flag:
            return {"检查结论": "合格", "原因": "数据hash值匹配", "详情": f"文件 {target_file} 的hash值与预期一致。"}
        else:
            return {"检查结论": "不合格", "原因": "数据hash值不匹配", "详情": f"期望: {expected_hash_or_flag}, 实际: {actual_hash}"}

    def check_data_addition(self, check_item: Dict) -> Dict:
        """检查数据新增"""
        target_file = check_item.get("target")
        value_raw = check_item.get("value", {})
        
        # 处理不同格式的value
        if isinstance(value_raw, list):
            # 列表格式转换为字典格式
            expected_addition = {}
            for item in value_raw:
                if isinstance(item, dict) and "field" in item and "expected_value" in item:
                    expected_addition[item["field"]] = item["expected_value"]
        else:
            # 字典格式直接使用
            expected_addition = value_raw
        
        before_records = self.data_before.get(target_file, [])
        after_records = self.data_after.get(target_file, [])
        
        before_records_set = {json.dumps(r, sort_keys=True) for r in before_records}
        new_records = [r for r in after_records if json.dumps(r, sort_keys=True) not in before_records_set]

        if not new_records:
            return {"检查结论": "不合格", "原因": "数据无新增", "详情": f"文件 {target_file} 中未发现任何新增记录。"}

        for record in new_records:
            match = all(record.get(k) == v for k, v in expected_addition.items())
            if match:
                return {"检查结论": "合格", "原因": "数据新增成功", "详情": f"在 {target_file} 中找到符合条件的新增记录: {expected_addition}"}

        return {"检查结论": "不合格", "原因": "新增数据不匹配", "详情": f"在 {target_file} 的新增记录中未找到符合 {expected_addition} 的记录。实际新增: {new_records}"}

    def check_data_delete(self, check_item: Dict) -> Dict:
        """检查数据删除"""
        target_file = check_item.get("target")
        value_raw = check_item.get("value", [{}])
        
        # 处理不同格式的value
        if isinstance(value_raw, list) and len(value_raw) > 0:
            delete_condition = value_raw[0]
        else:
            delete_condition = value_raw if isinstance(value_raw, dict) else {}
            
        key_field = delete_condition.get("field")
        key_value = delete_condition.get("expected_value")

        if not key_field:
            return {"检查结论": "不合格", "原因": "检查项配置错误", "详情": "删除检查缺少'field'字段。"}

        before_records = self.data_before.get(target_file, [])
        after_records = self.data_after.get(target_file, [])

        record_in_before = any(r.get(key_field) == key_value for r in before_records)
        if not record_in_before:
            return {"检查结论": "不合格", "原因": "待删除记录不存在", "详情": f"执行前 {target_file} 中就未找到 {key_field}={key_value} 的记录。"}
        
        record_in_after = any(r.get(key_field) == key_value for r in after_records)
        if not record_in_after:
            return {"检查结论": "合格", "原因": "数据删除成功", "详情": f"成功从 {target_file} 中删除 {key_field}={key_value} 的记录。"}
        else:
            return {"检查结论": "不合格", "原因": "数据未被删除", "详情": f"执行后 {target_file} 中仍存在 {key_field}={key_value} 的记录。"}
            
    def check_data_field(self, check_item: Dict) -> Dict:
        """检查数据字段是否更新，支持复合主键/候选键"""
        target_file = check_item.get("target")
        value_raw = check_item.get("value", {})
        
        # 处理不同格式的value
        if isinstance(value_raw, list):
            # 列表格式转换为字典格式
            update_info = {}
            for item in value_raw:
                if isinstance(item, dict) and "field" in item and "expected_value" in item:
                    update_info[item["field"]] = item["expected_value"]
        else:
            # 字典格式直接使用
            update_info = value_raw
        
        identifier_dict = {}
        potential_keys = self.PK_MAP.get(target_file, [])
        
        if not potential_keys:
            return {"检查结论": "不合格", "原因": "检查配置错误", "详情": f"PK_MAP中未定义文件 '{target_file}' 的主键/候选键。"}

        for key in potential_keys:
            if key in update_info:
                identifier_dict[key] = update_info[key]
        
        if not identifier_dict:
            return {
                "检查结论": "不合格", 
                "原因": "检查配置错误", 
                "详情": f"check_item的value中未提供任何有效标识符。对于'{target_file}'，需要提供以下至少一个: {potential_keys}"
            }
        
        fields_to_check = {
            k: v for k, v in update_info.items() if k not in identifier_dict
        }
        
        if not fields_to_check:
            return {
                "检查结论": "不合格",
                "原因": "检查配置错误",
                "详情": "check_item的value中没有提供任何需要检查更新的字段。"
            }

        after_records = self.data_after.get(target_file, [])
        target_record = self._find_record_by_keys(after_records, identifier_dict)
        if not target_record:
            return {
                "检查结论": "不合格",
                "原因": "记录未找到",
                "详情": f"执行后在 {target_file} 中未找到标识为 {identifier_dict} 的记录。"
            } 

        mismatched_fields = []
        for key, expected_val in fields_to_check.items():
            actual_val = target_record.get(key)
            if actual_val == expected_val:
                continue    
            try:
                if isinstance(expected_val, (int, float)) and isinstance(actual_val, (int, float)):
                    if float(actual_val) == float(expected_val):
                        continue
                if isinstance(expected_val, (int, float)) and isinstance(actual_val, str):
                    try:
                        converted_val = type(expected_val)(actual_val)
                        if converted_val == expected_val:
                            continue
                    except (ValueError, TypeError):
                        pass
            except (ValueError, TypeError):
                pass
            mismatched_fields.append(f"字段'{key}': 期望='{expected_val}'(类型:{type(expected_val)}), 实际='{actual_val}'(类型:{type(actual_val)})")
        
        if not mismatched_fields:
            return {"检查结论": "合格", "原因": "字段更新成功", "详情": f"记录 {identifier_dict} 的字段已按预期更新。"}
        else:
            return {"检查结论": "不合格", "原因": "字段更新不匹配", "详情": f"记录 {identifier_dict} 的以下字段不匹配: " + ", ".join(mismatched_fields)}


def check_response_llm(check_items: List[Tuple[int, Dict[str, Any]]], bench_data: Dict, result_data: Dict, model_name: str, api_base: str, api_key: str) -> Dict:
    """
    使用LLM检查响应内容 - 使用checker_hotel_v1.py的模板
    """
    if not check_items:
        return {}
    
    query = bench_data.get("extra_info", {}).get("query", "N/A")
    response_content = result_data.get("response", "")
    results = {}
    
    try:
        check_list_parts = []
        for i, (_, item) in enumerate(check_items):
            description = item.get("description", "")
            value = item.get("value")
            part = f"检查点 {i + 1}:\n  - 检查目标: {description}"
            if value is not None:
                part += f"\n  - 语义包括如下即可: '{value}'"
            check_list_parts.append(part)
        check_list_str = "\n\n".join(check_list_parts)
        print(f"response_content: \n{response_content}\n check_list_str: \n{check_list_str}\n")

        prompt = llm_template.format(query=query, response=response_content, check_list_str=check_list_str)
        success, llm_response_str = request_llm_with_litellm(
            [{"role": "user", "content": prompt}], 
            model_name, api_base, api_key
        )
        
        if success:
            parsed_results = parse_llm_response_json(llm_response_str, len(check_items))
            
            for i, (original_idx, _) in enumerate(check_items):
                item_result = parsed_results[i]
                results[f"检查项{original_idx}"] = {
                    "检查结论": item_result.get("conclusion", "不确定"),
                    "原因": "LLM判断",
                    "详情": item_result.get("analysis", "LLM未提供分析过程")
                }
        else:
            raise Exception("LLM调用失败")

    except Exception as e:
        print(f"LLM批量检查失败: {e}")
        for original_idx, _ in check_items:
            results[f"检查项{original_idx}"] = {
                "检查结论": "不确定", "原因": "LLM评估异常", "详情": str(e)
            }
    
    return results

def calculate_file_hash(file_path: str, algorithm: str = 'sha256', chunk_size: int = 8192) -> str:
    """计算文件的哈希值"""
    hash_func = hashlib.new(algorithm)
    try:
        with open(file_path, 'rb') as f:
            while chunk := f.read(chunk_size):
                hash_func.update(chunk)
        return hash_func.hexdigest()
    except FileNotFoundError:
        return ""
        
def load_env_after_from_disk(work_dir: str = ".") -> List[Dict[str, Any]]:
    """从磁盘加载指定任务的执行后环境数据"""
    env_after_data = []
    expected_files = [
        "bookings.jsonl", 
        "listings.jsonl", 
        "listing_calendar.jsonl",
        "listing_location.jsonl", 
        "ratings.jsonl"
    ]
    
    task_env_dir = Path(work_dir)
    print (f"work_dir: {work_dir}")
    print(f"加载执行后环境数据: {task_env_dir}")
    
    if not task_env_dir.is_dir():
        print(f"警告: 找不到执行后环境目录: {task_env_dir}")
        return []

    for filename in expected_files:
        file_path = task_env_dir / filename
        if file_path.is_file():
            try:
                content = file_path.read_text(encoding='utf-8')
                env_after_data.append({
                    "path": filename,
                    "type": "file",
                    "content": content,
                    "hash_value": calculate_file_hash(str(file_path))
                })
            except Exception as e:
                print(f"读取文件 {file_path} 失败: {e}")
    
    return env_after_data

def parse_llm_response_json(response_text: str, expected_count: int) -> List[Dict]:
    """解析LLM的JSON响应"""
    if not response_text:
        raise ValueError("LLM响应为空。")

    response_text = response_text.strip()
    
    try:
        data = safe_json_extract_single(response_text)
        if "results" in data and isinstance(data["results"], list):
            if len(data["results"]) == expected_count:
                return data["results"]
            else:
                raise ValueError(f"JSON 'results' 列表数量不匹配。期望 {expected_count}, 得到 {len(data['results'])}。")
        else:
            raise ValueError("JSON缺少 'results' 键或其值不是列表。")
    except (json.JSONDecodeError, ValueError) as e:
        print(f"LLM JSON响应解析失败: {e}\n原始响应: {response_text}")
        raise ValueError(f"LLM JSON解析错误: {e}") from e


def check_response_simple(check_item: Dict, result_data: Dict) -> Dict:
    """简单响应检查"""
    response_content = result_data.get("response", "")
    value_to_check = str(check_item.get("value", ""))
    method = check_item.get("method", "contains")
    
    is_contained = value_to_check in response_content
    
    if method in ["not_llm_judge", "not_contains"]:
        if not is_contained:
            return {"检查结论": "合格", "原因": "简单匹配", "详情": f"回复中未包含禁用词'{value_to_check}'。"}
        else:
            return {"检查结论": "不合格", "原因": "简单匹配", "详情": f"回复中包含了禁用词'{value_to_check}'。"}
    else:
        if is_contained:
            return {"检查结论": "合格", "原因": "简单匹配", "详情": f"回复中成功包含关键词'{value_to_check}'。"}
        else:
            return {"检查结论": "不合格", "原因": "简单匹配", "详情": f"回复中未能包含关键词'{value_to_check}'。"}


def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件失败 {file_path}: {str(e)}")
        return None


def perform_check(bench_data, result_data, model_name, api_base, api_key, work_dir="."):
    """
    执行检查逻辑 - 参考check.py的结构，使用checker_hotel_v1.py的检查方法
    """
    check_result = {
        "check_version": "hotel_v2_1.0",
        "check_timestamp": int(time.time()),
        "check_details": {},
        "overall_result": "Success",
        "error_reason": "",
        "check_list_count": 0,
        "completion_status": "in_progress",
        "completion_reason": ""
    }
    
    try:
        # 获取检查列表
        check_list = bench_data.get("extra_info", {}).get("check_list", [])
        if isinstance(check_list, str):
            check_list = json.loads(check_list)
        check_result["check_list_count"] = len(check_list)

        if not check_list:
            raise ValueError("检查列表(check_list)为空或格式错误")

        # 获取环境数据
        env_before = bench_data.get("extra_info", {}).get("environment_dependency", [])
        # 优先从result_data获取，如果没有则从磁盘加载
        #env_after = result_data.get("env_after_execute", [])
        #if not env_after:
        env_after = load_env_after_from_disk(work_dir)
        
        if not env_before:
            raise ValueError("缺少 'environment_dependency' 无法进行数据状态检查。")
        if not env_after:
            raise ValueError("缺少 'env_after_execute' 数据，无法进行数据状态检查。")
        

        # 创建数据检查器
        data_checker = JSONLDataChecker(env_before, env_after)
        llm_check_batch = []

        # 执行各项检查
        for i, item in enumerate(check_list):
            check_idx_str = f"检查项{i + 1}"
            if not isinstance(item, dict):
                check_result["check_details"][check_idx_str] = {
                    "检查结论": "跳过", "原因": "检查项格式错误", "详情": "非字典格式"
                }
                continue
            
            check_type = item.get("type")
            
            # 数据检查类型
            check_func_map = {
                'check_data_hash': data_checker.check_data_hash,
                'check_data_addition': data_checker.check_data_addition,
                'check_data_delete': data_checker.check_data_delete,
                'check_data_field': data_checker.check_data_field,
            }

            if check_type in check_func_map:
                check_result["check_details"][check_idx_str] = check_func_map[check_type](item)
            elif check_type == 'check_response':
                if item.get("method") == "llm_judge":
                    llm_check_batch.append((i + 1, item))
                else:
                    check_result["check_details"][check_idx_str] = check_response_simple(item, result_data)
            else:
                check_result["check_details"][check_idx_str] = {
                    "检查结论": "跳过", 
                    "原因": f"不支持的检查类型: {check_type}", 
                    "详情": ""
                }
        
        # 执行LLM批量检查
        if llm_check_batch:
            llm_results = check_response_llm(llm_check_batch, bench_data, result_data, model_name, api_base, api_key)
            check_result["check_details"].update(llm_results)
        
        # 计算总体结果
        final_result = "Success"
        failed_items = []
        for check_detail in check_result["check_details"].values():
            conclusion = check_detail["检查结论"]
            if conclusion == "不合格":
                final_result = "Failure"
                failed_items.append(check_detail)
            elif conclusion == "不确定":
                final_result = "Unsure"
        
        check_result["overall_result"] = final_result
        check_result["completion_status"] = "completed"
        
        if final_result == 'Failure':
            failed_item_names = [k for k, v in check_result["check_details"].items() if v['检查结论'] == '不合格']
            check_result["error_reason"] = f"失败项: {', '.join(failed_item_names)}"

    except Exception as e:
        check_result["overall_result"] = "Error"
        check_result["error_reason"] = f"任务检查异常: {e}"
        check_result["completion_status"] = "failed"
        check_result["completion_reason"] = f"任务执行异常: {e}"
    
    return check_result


def main():
    """主函数 - 参考check.py的结构"""
    parser = argparse.ArgumentParser(description='民宿预订场景自动评估检查脚本 v2')
    parser.add_argument('--bench', required=True, help='bench.json文件路径')
    parser.add_argument('--result', required=True, help='result.json文件路径')
    parser.add_argument('--model', required=True, help='检查用的模型名称')
    parser.add_argument('--base-url', required=True, help='模型API base URL')
    parser.add_argument('--api-key', required=True, help='模型API密钥')
    parser.add_argument('--output', default='check_result.json', help='输出文件路径')
    parser.add_argument('--work-dir', default='.', help='工作目录（相对于哪个目录执行文件检查）')
    args = parser.parse_args()
    
    # 加载输入文件
    print(f"🚀 启动民宿预订场景自动评估检查脚本 v2 🚀")
    print(f"加载bench文件: {args.bench}")
    bench_data = load_json_file(args.bench)
    if bench_data is None:
        sys.exit(1)
    
    print(f"加载result文件: {args.result}")
    result_data = load_json_file(args.result)
    if result_data is None:
        sys.exit(1)
    
    # 执行检查
    print(f"使用模型 {args.model} 进行检查...")
    
    # 切换到工作目录
    original_cwd = os.getcwd()
    os.chdir(args.work_dir)
    
    try:
        check_result = perform_check(
            bench_data, 
            result_data, 
            args.model, 
            args.base_url, 
            args.api_key,
            args.work_dir
        )
    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)
    
    # 输出结果
    print(f"保存检查结果到: {args.output}")
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(check_result, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    print(f"\n📊 检查完成!")
    print(f"总体结果: {check_result['overall_result']}")
    print(f"检查项数量: {len(check_result['check_details'])}")
    print(f"完成状态: {check_result['completion_status']}")
    
    if check_result['overall_result'] == 'Success':
        print("✅ 所有检查项都通过了!")
    elif check_result['overall_result'] == 'Failure':
        failed_items = [k for k, v in check_result['check_details'].items() 
                       if v['检查结论'] == '不合格']
        print(f"❌ 有 {len(failed_items)} 个检查项未通过:")
        for item in failed_items:
            detail = check_result['check_details'][item]
            print(f"  - {item}: {detail['详情']}")
    elif check_result['overall_result'] == 'Error':
        print(f"❌ 检查过程中出现错误: {check_result['error_reason']}")
    else:
        print(f"⚠️ 部分检查项结果不确定")


if __name__ == "__main__":
    main() 