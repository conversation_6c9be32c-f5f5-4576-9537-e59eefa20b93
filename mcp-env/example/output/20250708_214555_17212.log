2025/07/08 21:45:55 INFO 启动基准测试模式 config=/Users/<USER>/code/baidu/dataeng/mcp-env/eggs/example/input/model_config.json mcpservers=/Users/<USER>/code/baidu/dataeng/mcp-env/eggs/example/input/mcpservers.json bench=/Users/<USER>/code/baidu/dataeng/mcp-env/eggs/example/input/bench.json
2025/07/08 21:45:55 INFO 开始恢复环境 url="https://dataeng-bos-prod.bj.bcebos.com/mcp/73991ffe-05f1-44e4-bfe5-ca57bce05008-67210/output_data/env.tar.gz?authorization=bce-auth-v1%2FALTAKwPzBBVWHFtDyVhB2xFLTj%2F2025-07-03T09%3A11%3A42Z%2F-1%2Fhost%2Fa221eb29ac6528c4749906a0194f55e1b81065eb8670ea78e9ef54cdf46568f8"
2025/07/08 21:45:55 INFO 环境文件下载完成 file=temp_env.tar.gz
2025/07/08 21:45:55 INFO 环境文件解压完成
2025/07/08 21:45:55 INFO 环境恢复完成 result_file=/Users/<USER>/code/baidu/dataeng/mcp-env/eggs/example/output/result.json
