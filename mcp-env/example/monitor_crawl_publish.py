# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监督系统结果发布算子入口
产品文档&技术文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tjIp17bwPd/_cb7F-QsrB/kMJCIpifUvN9Q4
@file: monitor_result_publish.py
@author: x<PERSON><PERSON><PERSON><PERSON>
"""
import copy
import json
import secrets
import shutil
import signal
import string
import sys
import time
from datetime import datetime
from enum import Enum
from functools import wraps
from typing import Optional, List, Dict

import requests
from dataeng_sdk import common_tool
from dataeng_sdk.operator_base import OperatorBase
import os
from werkzeug.datastructures import FileStorage

from thought import extract_thought_response
from thought import check_not_need_thought_prompt
from thought import fulfill_thought_prompt, all_prompt

logger = common_tool.get_logger(__name__)

config = {
    "endpoint_sandbox": {
        "monitor":  "***********:8001",
        "crawl": "************:8081",
    },
    "endpoint_dev": {
        "monitor":  "*************:8001",
        "crawl": "************:8061",
    },
    "endpoint": {
        "monitor":  "************:8001",
        "crawl": "*************:8601",
    },
}

class StrategyStateCode(Enum):
    """
    评估策略任务状态码
    """

    SUCCESS = 0
    WAITING = 1
    RUNNING = 2
    FAILED = 3
    INTERRUPTED = 4
    PENDING = 5
    STARTING = 6
    CRAWL_PENDING = 7
    CRAWL_RUNNING = 8
    EVAL_PENDING = 9
    EVAL_RUNNING = 10
    UNKNOWN = -1

strategy_state_mapping = {
    StrategyStateCode.SUCCESS.value: "运行成功",
    StrategyStateCode.WAITING.value: "待开始",
    StrategyStateCode.RUNNING.value: "运行中",
    StrategyStateCode.FAILED.value: "运行失败",
    StrategyStateCode.INTERRUPTED.value: "手动终止",
    StrategyStateCode.PENDING.value: "排队中",
    StrategyStateCode.STARTING.value: "启动中",
    StrategyStateCode.CRAWL_PENDING.value: "抓取排队中",
    StrategyStateCode.CRAWL_RUNNING.value: "抓取中",
    StrategyStateCode.EVAL_PENDING.value: "评估排队中",
    StrategyStateCode.EVAL_RUNNING.value: "评估中",
    StrategyStateCode.UNKNOWN.value: "未知",
}

def retry_request(retry_times=3, delay=5):
    """
    重试请求装饰器
    Args:
        retry_times:
        delay:

    Returns:

    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            method = kwargs.get("method")
            url = kwargs.get("url")
            json_data = kwargs.get("json")
            params = kwargs.get("params")
            data = kwargs.get("data")
            files = kwargs.get("files")
            logger.info(
                f"Requesting url: {url}, method: {method}, json_data: {json_data}, "
                f"params: {params}, data: {data}, files: {files}"
            )

            attempts = retry_times
            while attempts > 0:
                try:
                    response = func(*args, **kwargs)
                    if response.status_code == 200:
                        return response.json()
                    else:
                        try:
                            data = response.json()
                            logger.error("Request failed: status_code=%d, response_json=%s", response.status_code,
                                         data)
                        except Exception:
                            logger.error("Request failed: status_code=%d, response=%s", response.status_code,
                                          response.text)
                except requests.RequestException as e:
                    logger.error("Request exception: %s", str(e))

                attempts -= 1
                if attempts > 0:
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

@retry_request(retry_times=3, delay=5)
def request_with_retry(method, url, **kwargs):
    """
    重试请求
    Args:
        method:
        url:
        **kwargs:

    Returns:

    """
    return requests.request(method, url, **kwargs)

class Operator(OperatorBase):
    """
    结果发布算子
    """

    def _setup(self):
        """
        准备工作
        """
        logger.info(
            "*********************************************operator setup start"
            "*********************************************"
        )
        # 在这里进行参数校验等准备工作
        # 例如：self.validate_params()
        logger.info("self.task_info: %s", self.task_info)
        logger.info("self.params: %s", self.params)
        logger.info("self.input_files: %s", self.input_files)
        if not self.input_files or len(self.input_files) == 0:
            self.report_error("input files is empty on dataset operator", 0)
        if len(self.input_files) > 1:
            self.report_error("only one input file allowed", 0)

        self.local_env_set = os.getenv("LOCAL_ENV_SET")
        if self.local_env_set is None:
            self.env = os.getenv("AIRFLOW_PLATFORM_ENV")
            logger.info("当前环境为AIRFLOW_PLATFORM_ENV为:{}".format(self.env))
            self.user_name = os.getenv("AIRFLOW_CTX_DAG_OWNER")
            logger.info("当前用户为AIRFLOW_CTX_DAG_OWNER为:{}".format(self.user_name))
            if self.user_name is None:
                self.report_error("AIRFLOW_CTX_DAG_OWNER(user_name) is none", 0)

            self.task_id = self.task_info.get("task_id")
            logger.info("task_id value is: %s", self.task_id)

            strategy_workflow_id = self.workplat_flow_id
            logger.info("strategy_workflow_id value is: %s", strategy_workflow_id)

            # 获取一个临时的目录文件，用于中间文件的生成
            self.temp_current_dir = os.path.dirname(os.path.abspath(__file__))

            if self.global_params is None:
                self.report_error("global_params is none", 0)
            try:
                logger.info("global_params value is: %s", self.global_params)
            except json.JSONDecodeError:
                self.report_error("global_params is invalid json format", 0)

            pool_name_list = self.global_params.get("pool_name")
            if pool_name_list is None:
                self.report_error("pool_name in global_params is none", 0)
            if len(pool_name_list) != 1:
                self.report_error("pool_name length must be 1", 0)
            self.pool_name = pool_name_list[0]

            self.temp_qr_set_name = self.global_params.get("temp_qr_set_name", "未获取到qr_set_name")
            self.temp_qr_set_name = self.temp_qr_set_name.replace(",", "")

            self.project_name = self.global_params.get("project_name")
            if self.project_name is None:
                logger.warning("project_name in global_params is none, use default 'comprehensive'")
                self.project_name = "comprehensive"
        else:
            # 本地算子调试的测试代码
            self.env = self.local_env_set
            logger.info("本地调试设定的环境为:{}".format(self.env))
            self.user_name = "<EMAIL>"
            logger.info("当前用户为:{}".format(self.user_name))

            self.task_id = 26148
            logger.info("task_id value is: %s", self.task_id)

            strategy_workflow_id = 2522
            logger.info("strategy_workflow_id value is: %s", strategy_workflow_id)

            # 获取一个临时的目录文件，用于中间文件的生成
            self.temp_current_dir = os.path.dirname(os.path.abspath(__file__))

            self.global_params = {
                "pool_name": [
                    "EB_4010_DEFAULT_platform_dev"
                ],
                "work_num": {
                    "EB_4010_DEFAULT_platform_dev": 1
                },
                "use_cache": True,
                "crawl_response": True,
                "L0_name": "代码",
                "project_flow_id": 244,
                "models": [
                    {
                        "model_id": "api3",
                        "type": "sandbox",
                        "params": {"userId": 2366140312}
                    }
                ],
                "group_id": 10,
                "group_name": "代码",
                "data_model_type": "reasoning"
            }
            self.pool_name = self.global_params.get("pool_name")[0]

    def _run(self):
        """
        运行函数
        """
        logger.info(
            "**********************************************operator run start"
            "**********************************************"
        )
        is_crawl = self.global_params.get("crawl_response", False)
        # 如果需要抓取，则调用 crawl_data()函数
        if is_crawl:
            output_files = self.crawl_data()
        else:
            # 如果不需要抓取，则调用 no_crawl_data()函数
            output_files = self.not_crawl_data()

        # 一直for循环，每隔5s检查一下状态，如果状态变为评估中时，则退出循环，执行结束
        self.waiting_to_continue()

        return output_files

    def crawl_data(self):
        """
        抓取函数，该函数要做的事情如下：
        1. 对全局参数进行校验解析
        2. 读取输入参数，将输入文件转化成统一调度可以消费的输入文件
        3. 将统一调度可消费的输入文件放到afs系统上，提供一个可以下载的地址
        4. 调用统一调度服务的接口执行任务
        5. 等待任务执行完成
        6. 抓取过程中，需要判断任务的状态，同时将当前的状态写回到监督系统的任务中
        7. 将统一调度服务的执行结果拼合到初始的文件中
        8. 将拼合后的文件放到文件目录中，同时调用监督系统的qr集上传接口上传数据，并将返回的qr_id
        写回到任务中
        9. 通过监督系统的状态修改函数，修改监督系统的状态
        statu是评估排队中，然后状态信息是：等待调度
        """
        # 1. 对全局参数进行校验解析
        crawl_models = self.global_params.get("models")
        if crawl_models is None or not isinstance(crawl_models, list):
            self.report_error("models is invalid", 0)
        if len(crawl_models) == 0:
            self.report_error("models is empty", 0)

        # 如果是pdc类型的模型，需要指定  max_dec_len 字段
        for model in crawl_models:
            model_type = model.get("type")
            if model_type == "pdc":
                params = model.get("params", {})
                max_dec_len = params.get("max_dec_len")
                if max_dec_len is None:
                    self.report_error(f"max_dec_len of pdc model {model.get('model_id')} is not in params")

        L0_name = self.global_params.get("L0_name")

        project_flow_id = self.global_params.get("project_flow_id")
        if project_flow_id is None:
            self.report_error("project_flow_id is none", 0)
        group_id = self.global_params.get("group_id")
        if group_id is None:
            self.report_error("group_id is none", 0)
        group_name = self.global_params.get("group_name")
        if group_name is None:
            self.report_error("group_name is none", 0)
        # 调整状态
        self.change_strategy_task_status(
            task_id=self.task_id,
            status=StrategyStateCode.CRAWL_PENDING.value,
            status_message=strategy_state_mapping[StrategyStateCode.CRAWL_PENDING.value]
        )

        before_input_file_path = self.input_files[0]

        # 如果没有 data_id 字段，则用在数据中生成一个 data_id,值为id字段
        real_input_file_path = self.add_data_id_key_to_input_path(before_input_file_path)
        url = self.get_test_local_file_url(real_input_file_path, "after_input_file")
        logger.info(f"append data_id input the input file, bos file path: {url}")

        # 2. 读取输入参数，将输入参数转化成统一调度可以消费的输入参数
        temp_file = self.transform_monitor_data_to_crawl_data(real_input_file_path)

        # 3. 将统一调度可消费的输入文件放到afs系统上，提供一个可以下载的地址
        if self.local_env_set is None:
            # 正式环境的话，返回一个afs的路径
            temp_url = self.get_file_download_url(temp_file)
        else:
            # 测试环境的话，返回一个本地的路径
            temp_url = self.get_test_local_file_url(temp_file)

        # 4. 调用统一调度服务的接口执行任务
        # create_tasks 的结构：{"model_id": "task_name"}
        created_tasks = {}
        input_url = temp_url
        try:
            for model in crawl_models:
                model_id = model.get("model_id")
                if model_id is None:
                    self.report_error("model_id is none", 0)
                model_type = model.get("type")
                if model_type is None:
                    self.report_error("model_type is none", 0)
                params = model.get("params")
                # 获取并发数
                concurrency = None
                if params is not None:
                    if "concurrency" in params.keys():
                        concurrency = params["concurrency"]
                        del params["concurrency"]

                name = self.generate_crawl_task_name(model_id)
                submitter = self.user_name
                organization = "监督系统"
                task_type = f"{L0_name}_抓取"
                priority_category = "自动评估"
                data_url = input_url
                extra = {}
                # 对沙盒的userId做个解析
                user_id = model.get("user_id")
                if user_id is not None:
                    # user_id = user_id.strip()
                    # split_ids = user_id.split(",")
                    # if len(split_ids) == 1:
                    #     userId = int(split_ids[0])
                    #     extra["userId"] = userId
                    # else:
                    #     userIds = []
                    #     for id in split_ids:
                    #         userIds.append(int(id))
                    #     extra["userIds"] = userIds
                    try:
                        user_id = int(user_id) if user_id else -1  # 空字符串或 None 时设为 -1
                    except (ValueError, TypeError):
                        user_id = -1
                    if user_id > 0:
                        extra["userId"] = user_id
                if concurrency is not None:
                    extra["concurrency"] = concurrency
                    extra["batchsize"] = concurrency
                if self.global_params.get("data_model_type") == "reasoning":
                    # 现在这个用于推理的沙盒url就先写死了
                    extra["url"] = "http://10.11.204.53:8005/debug/eb/chat/new"

                extra["task_id"] = self.task_id
                if self.global_params.get("use_cache", False) is True:
                    extra["use_cache"] = 1
                else:
                    extra["use_cache"] = 0

                if model.get("pool_name") is None:
                    models_info = [
                        {
                            "model_id": model_id,
                            "type": model_type,
                        }
                    ]
                    if concurrency is not None:
                        models_info[0]["concurrency"] = concurrency

                    data = {
                        "name": name,
                        "submitter": submitter,
                        "organization": organization,
                        "task_type": task_type,
                        "priority_category": priority_category,
                        "data_url": data_url,
                        "models_info": models_info,
                        "params": params,
                        "extra": extra
                    }
                else:
                    models_pool = model.get("pool_name")

                    data = {
                        "name": name,
                        "submitter": submitter,
                        "organization": organization,
                        "task_type": task_type,
                        "priority_category": priority_category,
                        "data_url": data_url,
                        "models_pool": models_pool,
                        "params": params,
                        "extra": extra
                    }

                logger.info(f"create crawl task: {data}")

                crawl_url = f"{self.get_crawl_url_host()}/api/task"

                res = request_with_retry(
                    method="POST",
                    url=crawl_url,
                    headers={"Content-Type": "application/json"},
                    json=data,
                )

                if res is None:
                    self.report_error("Failed to create crawl task", 0)
                created_tasks[model_id] = name
        except Exception as e:
            # 如果出现异常，则停止所有创建的任务，并报告错误
            self.stop_crawl_tasks(created_tasks)
            self.report_error(str(e), 0)

        # # 创建一个信号处理函数，用于处理SIGTERM信号
        # def create_handler(data_array):
        #     def handle_sigterm(signum, frame):
        #         logger.info(f"Received SIGTERM (signal {signum}). stop all crawl tasks")
        #         logger.info(f"created crawl tasks is: {data_array}")
        #         self.stop_crawl_tasks(data_array)
        #         sys.exit(143)
        #
        #     return handle_sigterm
        #
        # # 生成带有数组的信号处理函数
        # signal_handler = create_handler(created_tasks)
        # # 绑定 SIGTERM 信号到处理函数
        # signal.signal(signal.SIGTERM, signal_handler)

        logger.info(f"created crawl tasks is: {created_tasks}")
        # 将执行过程中的task信息保存到全局的变量中，便于后续的处理
        self.dup_tasks = copy.deepcopy(created_tasks)

        try:
            finished_msg = []
            while len(self.dup_tasks) > 0:
                time.sleep(10)
                need_remove_keys = []
                current_msg = []
                for model_id, task in self.dup_tasks.items():
                    res = request_with_retry(
                        method="GET",
                        url=f"{self.get_crawl_url_host()}/api/task/{task}",
                        headers={"Content-Type": "application/json"},
                    )
                    if res is None:
                        self.report_error(f"Failed to get crawl task {task} status", 0)
                    else:
                        result = res.get("data")
                        if result is None:
                            self.report_error(f"Failed to get crawl task {task} status attr 'data'", 0)
                        if result.get("status") == 0:
                            # 如果状态等于成功
                            need_remove_keys.append(model_id)
                            finished_msg.append({
                                "model_id": model_id,
                                "scope": result.get("scope", ""),
                                "status": 0,
                                "status_msg": result.get("status_msg", ""),
                                "total": result.get("total"),
                                "crawled": result.get("crawled"),
                                "success_crawled": result.get("success_crawled"),
                                "queue_order": result.get("queue_order"),
                                "created_time": result.get("created_time"),
                                "updated_time": result.get("updated_time"),
                            })
                        elif result.get("status") == 4 or result.get("status") == -1 or result.get("status") == 3:
                            # 如果状态等于失败（4），或者未知（-1），或者已删除(3)，则抛出异常
                            self.report_error(
                                f"task {task} status is {result.get('status')}, "
                                f"response message is '{result}, running failed",
                                0
                            )
                        else:
                            logger.info(
                                f"crawl model is: '{model_id}', "
                                f"task '{task}' status is {result.get('status')}, "
                                f"total data: {result.get('total')}, "
                                f"processed data: {result.get('crawled')}, "
                                f"success processed data: {result.get('success_crawled')}, "
                                f"result url: "
                                f"' {result.get('results_url', 'does not exist, please wait for a moment')} '"
                            )
                            current_msg.append({
                                "model_id": model_id,
                                "scope": result.get("scope", ""),
                                "status": result.get("status"),
                                "status_msg": result.get("status_msg", ""),
                                "total": result.get("total"),
                                "crawled": result.get("crawled"),
                                "success_crawled": result.get("success_crawled"),
                                "queue_order": result.get("queue_order"),
                                "created_time": result.get("created_time"),
                                "updated_time": result.get("updated_time"),
                            })
                            continue
                for key in need_remove_keys:
                    del self.dup_tasks[key]
                concat_msg = self.concat_crawl_status(finished_msg, current_msg)
                self.change_strategy_task_status(
                    task_id=self.task_id,
                    status=StrategyStateCode.CRAWL_RUNNING.value,
                    status_message=concat_msg
                )
            logger.info(f"All crawl tasks are finished, task is {created_tasks}")
        except Exception as e:
            self.report_error(str(e), 0)

        # 7. 将统一调度服务的执行结果拼合到初始的文件中
        result_paths = {}
        for model_id, task in created_tasks.items():
            res = request_with_retry(
                method="GET",
                url=f"{self.get_crawl_url_host()}/api/task/{task}",
                headers={"Content-Type": "application/json"}
            )
            if res is None:
                self.report_error(f"Failed to get crawl task {task} status", 0)
            result_url = res.get("data", {}).get("results_url")
            if result_url is None:
                self.report_error(
                    (
                        f"Failed to get crawl task {task} status attr 'results_url', "
                        f"res is {res}"
                    ),
                0)
            dest_path = os.path.join(self.temp_current_dir, f"{task}.json")
            os.system(f"wget {result_url} -O {dest_path}")
            result_paths[model_id] = dest_path

        original_data = self.transform_crawl_data_to_monitor_data(
            crawl_models=list(created_tasks.keys()),
            result_paths=result_paths,
            input_file=real_input_file_path,
        )

        # 8. 将拼合后的文件放到文件目录中，同时调用监督系统的qr集上传接口上传数据，并将返回的qr_id写回到任务中
        save_result_path = os.path.join(
            self.output_local_path, self.temp_qr_set_name
        )
        save_result_path += ".jsonl"
        # save_result_path = os.path.join(
        #     self.output_local_path, os.path.basename(self.input_files[0])
        # )
        with open(save_result_path, "w") as wf:
            for _, line in original_data.items():
                wf.write(json.dumps(line))
                wf.write("\n")
        logger.info(f"save result to path: {save_result_path}")

        # 将中间文件先上传到bos上
        bos_url = self.get_test_local_file_url(save_result_path)
        logger.info(f"中间结果 bos url: {bos_url}")

        (model_evaluation_id, qr_set_path) = self.upload_qr_result_to_monitor(
            save_result_path,
            self.global_params
        )

        if qr_set_path is not None and qr_set_path != "":
            logger.info(f"qr_set_path is not None, download qr_set_url f'{qr_set_path}'")
            logger.info(f"overwrite file '{save_result_path}'")
            os.system(f"wget {qr_set_path} -O {save_result_path}")

        # 9. 通过监督系统的状态修改函数，修改监督系统的状态
        self.change_strategy_task_status(
            task_id=self.task_id,
            status=StrategyStateCode.EVAL_PENDING.value,
            status_message=strategy_state_mapping[StrategyStateCode.EVAL_PENDING.value],
            model_evaluation_id=model_evaluation_id
        )

        return [save_result_path]

    def concat_crawl_status(
            self,
            finished_msg: List[Dict],
            current_msg: List[Dict],
    ) -> str:
        """
        将当前的状态信息和已经完成的状态信息进行合并，生成最终的状态信息
        """
        all_show_msg = []
        current_time = datetime.now()
        for item in finished_msg:
            msg = (
                f"模型'{item.get('model_id')}'：抓取完成。"
                f"共{item.get('total')}条数据，成功抓取{item.get('success_crawled')}条。"
            )
            all_show_msg.append(msg)
        for item in current_msg:
            created_time = item.get("created_time")
            created_time = datetime.strptime(created_time, "%Y-%m-%d %H:%M:%S")
            elapsed_seconds = int((current_time - created_time).total_seconds())
            # status == 2 表示正在抓取
            if item.get("status") == 2:
                msg = (
                    f"模型'{item.get('model_id')}'：正在抓取。"
                    f"共 {item.get('total')} 条数据，已抓取 {item.get('crawled')} 条。"
                    f"任务创建后已运行 {elapsed_seconds} 秒"
                )
            # status == 1 表示还没开始处理抓取
            elif item.get("status") == 1:
                msg = (
                    f"模型'{item.get('model_id')}'：等待抓取任务预处理。"
                    f"共 {item.get('total')} 条数据，已创建 {item.get('crawled')} 条。"
                    f"任务创建后已运行 {elapsed_seconds} 秒"
                )
            # status == 5 表示排队中
            else:
                msg = (
                    f"模型'{item.get('model_id')}'：排队中。"
                    f"排在第 {item.get('queue_order')} 位。"
                    f"共 {item.get('total')} 条数据，已抓取 {item.get('crawled')} 条。"
                    f"任务创建后已运行 {elapsed_seconds} 秒"
                )
            all_show_msg.append(msg)
        return "\n".join(all_show_msg)

    def transform_monitor_data_to_crawl_data(self, file_path: str):
        """
        将监督系统的输入转化成统一调度的输入格式

        Args:
        file_path: 原始输入文件路径
        Returns:
            str: 转化后的文件路径
        """

        # 校验 data_id 是否重复
        all_data_ids = set()
        with open(file_path, "r") as f:
            for line in f:
                data = json.loads(line)
                data_id = data.get("data_id")
                if data_id is None:
                    self.report_error(
                        f"there is no field named 'data_id' in the data",
                        0
                    )
                if data_id in all_data_ids:
                    self.report_error(
                        f"the data_id of the data must be unique, but there exists duplicate data_id: {data_id}",
                        0
                    )
                all_data_ids.add(data_id)

        base_name = os.path.basename(file_path)
        output_path = os.path.join(
            self.temp_current_dir,
            f"temp_{base_name}",
        )
        with open(file_path, "r") as rf:
            with open(output_path, "w") as wf:
                for line in rf:
                    line = line.strip()
                    data = json.loads(line)
                    save_data = {}
                    data_type = data.get("type", "static")
                    # 从 data_id 和 id中获取id，优先 data_id
                    save_data["id"] = data.get("data_id")
                    request_response_param = data.get("request_response_param")
                    save_data["data_model_type"] = self.global_params.get("data_model_type")
                    if request_response_param is not None:
                        save_data["request_response_param"] = request_response_param
                    if data_type == "static":
                        save_data["type"] = "static"
                        save_data["system"] = data.get("system")
                        save_data["context"] = data.get("context")
                        save_data["query"] = data.get("query")
                        request_response_query = data.get("request_response_query")
                        if request_response_query is not None and request_response_query != "":
                            save_data["query"] = request_response_query
                        if self.global_params.get("data_model_type") == "reasoning":
                            save_data["prompt"] = all_prompt["thought_header"]
                            if check_not_need_thought_prompt(save_data["query"]):
                                # save_data["query"] = fulfill_thought_prompt(
                                #     all_prompt["thought"],
                                #     save_data["query"],
                                # )
                                save_data["text_after_process"] = save_data["query"]
                    elif data_type == "dynamic":
                        save_data["type"] = "dynamic"
                        save_data["system"] = data.get("system")
                        save_data["query_list"] = data.get("query_list")
                        if self.global_params.get("data_model_type") == "reasoning":
                            save_data["prompt"] = all_prompt["thought_header"]
                    else:
                        self.report_error(
                            f"the type of the data must be 'static' or 'dynamic', data id is: {save_data['id']}",
                            0
                        )
                    wf.write(json.dumps(save_data))
                    wf.write("\n")

        return output_path

    def add_data_id_key_to_input_path(self, file_path) -> str:
        """
        给监控系统的数据添加data_id字段
        """
        # 复制原文件
        all_data = []

        after_file_path = os.path.join(
            self.temp_current_dir,
            f"deal_id_{os.path.basename(file_path)}",
        )

        with open(file_path, "r") as rf:
            for line in rf:
                line = line.strip()
                data = json.loads(line)
                data_id = data.get("data_id")
                if data_id is not None:
                    logger.info(f"data_id exists, skip to generate data_id")
                    return file_path
                else:
                    data["data_id"] = data["id"]
                all_data.append(data)

        with open(after_file_path, "w") as wf:
            for line in all_data:
                wf.write(json.dumps(line))
                wf.write("\n")

        return after_file_path

    def get_file_download_url(self, file_path):
        """
        对于中间文件获取一个可以下载的文件地址
        """
        base_name = os.path.basename(file_path)
        shutil.move(file_path, common_tool.get_output_path() +
                    "/" + base_name)

        downloadUrl = common_tool.get_output_url() + "/" + base_name
        return downloadUrl

    def get_test_local_file_url(self, file_path, prefix_info="test_local_file") -> str:
        """
        获取本地的测试下载文件。要求：和输入的文件要一致
        Args:
            file_path: 之后可能会有用

        Returns:

        """

        url = f"{self.get_monitor_url_host()}/api/v1/monitor/common/upload_file"
        temp_open_file = open(file_path, 'rb')
        dataset = FileStorage(
            stream=temp_open_file,
            filename=os.path.basename(file_path),
            content_type='text/plain'
        )

        data = {
            "task_id": self.task_id,
            "prefix_info": prefix_info,
        }
        res = request_with_retry(
            method="POST",
            url=url,
            data=data,
            files={
                "dataset": (os.path.basename(file_path), dataset, "text/plain"),
            }
        )
        if res is None:
            self.report_error("upload qr result failed", 0)
        download_url = res.get("data", {}).get("url")
        if not download_url or not isinstance(download_url, str):
            self.report_error("failed to get download url", 0)

        return download_url

    def transform_crawl_data_to_monitor_data(
            self,
            crawl_models: list,
            result_paths: dict,
            input_file: str
    ) -> dict:
        """
        将调度系统的输入转化成监督系统的输入格式
        """
        original_data = {}
        # 读取原始文件的所有内容，存储到内存中，存一个dict，每行是一个json
        with open(input_file, "r") as f:
            for line in f:
                cur_data = json.loads(line)
                data_id = cur_data.get("data_id")
                if data_id is None:
                    self.report_error(f"Failed to get crawl task 'data_id' of line {line}", 0)
                cur_data["responses"] = {}
                for model in crawl_models:
                    cur_data["responses"][model] = None
                data_type = cur_data.get("type")
                if data_type is None or data_type == "":
                    data_type = "static"
                if data_type == "static":
                    original_data[f"{data_id}_"] = cur_data
                elif data_type == "dynamic":
                    turns = len(cur_data.get("query_list", []))
                    cur_data["dynamic_context"] = {}
                    ground_truth_list = cur_data.get("ground_truth_list", None)
                    for model in crawl_models:
                        cur_data["dynamic_context"][model] = {}
                    for i in range(turns):
                        new_data = copy.deepcopy(cur_data)
                        new_data["turn_id"] = i
                        if ground_truth_list is not None:
                            if len(ground_truth_list) > i:
                                new_data["ground_truth"] = ground_truth_list[i]
                            else:
                                logger.warning(f"Failed to find ground truth for turn {i} of data_id: {data_id}")
                        original_data[f"{data_id}_{i}"] = new_data

        # 遍历每个结果文件，将结果文件中的数据与原始数据进行合并
        for model_id, path in result_paths.items():
            with open(path, "r") as f:
                for line in f:
                    cur_data = json.loads(line)
                    data_type = cur_data.get("type")
                    id = cur_data.get("id")
                    if id is None:
                        self.report_error(f"Failed to get crawl task 'id' of line {line} from crawl", 0)
                    real_id = f"{id}_{cur_data.get('turn_id', '')}"
                    if real_id not in original_data.keys():
                        logger.warning(f"Failed to find real id '{real_id}' in original data")
                        continue
                    original_data[real_id]["responses"][model_id] = cur_data.get("output")
                    if data_type == "dynamic":
                        original_data[real_id]["query"] = cur_data.get("query")
                        if cur_data.get("data_model_type") == "reasoning":
                            # 如果是推理模型，这个 dynamic_context 的response中解析出去除thought的response
                            new_dynamic_context = cur_data.get("dynamic_context")
                            if new_dynamic_context is None:
                                new_dynamic_context = []
                            for item in new_dynamic_context:
                                if item.get("role") == "assistant":
                                    temp_output = item.get("content")
                                    _, response = extract_thought_response(temp_output)
                                    item["content"] = response
                            original_data[real_id]["dynamic_context"][model_id] = new_dynamic_context
                        else:
                            original_data[real_id]["dynamic_context"][model_id] = cur_data.get("dynamic_context")
        return original_data


    def not_crawl_data(self):
        """
        对于不需要抓取的函数，需要做下面几件事情：
        1. 将输入文件保存输入目录，用于下一个算子的消费
        2. 通过监督系统的状态修改函数，修改监督系统的状态
        statu是评估排队中，然后状态信息是：等待调度
        3. 一直for循环，每隔5s检查一下状态，如果状态变为评估中时，则退出循环，执行结束
        Returns:

        """

        # 1. 将输入文件保存输入目录，用于下一个算子的消费
        output_files = []
        for input_file in self.input_files:
            output_file = os.path.join(
                self.output_local_path, os.path.basename(input_file)
            )
            shutil.copy(
                src=input_file,
                dst=output_file
            )
            output_files.append(output_file)
        # 2. 通过监督系统的状态修改函数，修改监督系统的状态
        res = self.change_strategy_task_status(
                task_id=self.task_id,
                status=StrategyStateCode.EVAL_PENDING.value,
                status_message="等待评估调度"
        )
        if res is None:
            self.report_error("update task status failed", 0)

        return output_files


    def waiting_to_continue(self):
        """
        等待任务排队，然后向下执行
        1. 一直for循环，每隔5s检查一下状态，如果状态变为评估中时，则退出循环，执行结束
        Returns:

        """
        while True:
            time.sleep(5)
            res = self.occupy_pool(
                task_id=self.task_id,
                pool_name=self.pool_name
            )
            if res.get("code") != 0:
                logger.info(
                    f"Failed to occupy pool, status code: {res.get('code')}, "
                    f"status message: {res.get('message')}"
                )
                continue
            else:
                status_res = self.change_strategy_task_status(
                    task_id=self.task_id,
                    status=StrategyStateCode.EVAL_RUNNING.value,
                    status_message=strategy_state_mapping[StrategyStateCode.EVAL_RUNNING.value]
                )
                if status_res is None:
                    self.report_error("update task status to eval running failed", 0)
                break

    def change_strategy_task_status(
            self,
            task_id: int,
            status: int,
            status_message: str,
            model_evaluation_id: int=None
    ) -> Optional[dict]:
        """
        修改策略任务的状态
        Args:
            task_id:
            status:
            status_message:
            model_evaluation_id:

        Returns:

        """
        url = f"{self.get_monitor_url_host()}/api/v1/monitor/strategy/status"
        data = {
            "task_id": task_id,
            "status": status,
            "status_message": status_message
        }
        if model_evaluation_id is not None:
            data["model_evaluation_id"] = model_evaluation_id
        res = request_with_retry(
            method="POST",
            url=url,
            json=data,
        )
        return res

    def get_strategy_task_status(self, task_id: int) -> (bool, dict):
        """
        获取策略任务的状态
        Args:
            task_id:

        Returns:

        """
        url = f"{self.get_monitor_url_host()}/api/v1/monitor/strategy/status"
        params = {
            "task_id": task_id,
        }
        retry_times = 3
        while retry_times > 0:
            response = requests.get(
                url=url,
                params=params,
                headers={"Content-Type": "application/json"},
            )
            if response.status_code != 200:
                logger.error(
                    "Failed to get crawl task status, status code: %d, status message: %s",
                    response.status_code,
                    response.text,
                )
                retry_times -= 1
            else:
                return True, response.json()
            time.sleep(5)
        self.report_error("get task status failed", 0)

    def occupy_pool(
            self,
            task_id: int,
            pool_name: str
    ):
        """
        获取资源池槽位，用户控制并发
        Args:
            task_id:
            pool_name:

        Returns:

        """
        url = f"{self.get_monitor_url_host()}/api/v1/monitor/strategy/occupy_slot"
        params = {
            "task_id": task_id,
            "pool_name": pool_name,
        }
        res = request_with_retry(
            method="GET",
            url=url,
            params=params,
        )
        if res is None:
            self.report_error("occupy slot interface get error", 0)
        return res

    def upload_qr_result_to_monitor(
            self,
            output_path: str,
            global_params: dict
    ) -> (int, str):
        """
        上传qr结果到监督系统中
        Args:
            output_path:
            global_params:

        Returns:

        """
        project_name = self.project_name
        project_flow_id = global_params["project_flow_id"]
        submitter = self.user_name
        qr_set_name = os.path.basename(output_path).rsplit(".", 1)[0]
        temp_open_file = open(output_path, 'rb')
        dataset = FileStorage(
            stream=temp_open_file,
            filename=os.path.basename(output_path),
            content_type='text/plain'
        )
        group_id = global_params["group_id"]
        group_name = global_params["group_name"]
        query_response_type = "crawlupload"
        url = f"{self.get_monitor_url_host()}/api/v1/monitor/data/query_resp_upload"
        data = {
            "project_name": project_name,
            "project_flow_id": project_flow_id,
            "submitter": submitter,
            "group_id": group_id,
            "group_name": group_name,
            "query_response_type": query_response_type,
            "qr_set_name": qr_set_name,
            "model_type": self.global_params.get("data_model_type")
        }
        res = request_with_retry(
            method="POST",
            url=url,
            data=data,
            files={
                "dataset": (os.path.basename(output_path), dataset, "text/plain"),
            }
        )
        if res is None:
            self.report_error("upload qr result failed", 0)
        logger.info(f"upload data result is: {res}")
        model_evaluation_id = res.get("data", {}).get("model_evaluation_id")
        model_evaluation_ids = res.get("data", {}).get("model_evaluation_ids")
        qr_set_path = res.get("data", {}).get("qr_set_path")

        if model_evaluation_id is not None:
            return (model_evaluation_id, qr_set_path)

        if model_evaluation_ids is not None:
            if len(model_evaluation_ids) != 1:
                self.report_error(
                    f"model_evaluation_ids should get only one element, it is {model_evaluation_ids}",
                    0
                )
            return (model_evaluation_ids[0], "")

        logger.error(f"model_evaluation_id is None, model_evaluation_ids is {model_evaluation_ids}")


    def get_monitor_url_host(self) -> str:
        """
        获取监督系统URL的主机地址。
        Args:
            无
        Returns:
            str: 返回监控URL的主机地址。
        根据当前环境变量（env）的不同，返回相应的监控URL主机地址。
        - 如果环境变量为 'sandbox'，则返回开发环境的监控URL。
        - 如果环境变量为 'test'，则返回测试环境的监控URL。
        - 如果环境变量为其他值，则返回生产环境的监控URL。
        注意：需要确保在配置文件中正确设置了对应环境的监控URL。
        """
        if self.env == "sandbox":
            host = f"http://{config.get('endpoint_sandbox').get('monitor')}"
        elif self.env == "test":
            host = f"http://{config.get('endpoint_dev').get('monitor')}"
        else:
            host = (
                f"http://{config.get('endpoint').get('monitor')}"
            )
        return host

    def get_crawl_url_host(self) -> str:
        """
        Args:
            无
        Returns:
            str: 返回用于抓取数据的URL的主机地址。
        Raises:
            无
        功能说明:
            根据当前环境变量（env）返回相应的爬取数据URL的主机地址。
        注意事项:
            - 如果环境变量为 'sandbox'，则返回开发环境的URL。
            - 如果环境变量为 'test'，则返回测试环境的URL。
            - 如果环境变量为其他值，则返回生产环境的URL。
            - 需要确保在配置文件中正确设置了对应环境的URL。
        """
        if self.env == "sandbox":
            host = f"http://{config.get('endpoint_sandbox').get('crawl')}"
        elif self.env == "test":
            host = f"http://{config.get('endpoint_dev').get('crawl')}"
        else:
            host = (
                f"http://{config.get('endpoint').get('crawl')}"
            )
        return host

    def generate_crawl_task_name(self, model_id) -> str:
        """
        生成统一调度的任务名字
        Args:
            model_id:

        Returns:

        """
        strategy_id = self.workplat_flow_id
        task_id = self.task_info.get("task_id")
        rand_str = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(16))
        transformed_model_id = str(model_id).replace(":", "_")
        return f"monitor_{strategy_id}_{task_id}_{transformed_model_id}_{rand_str}"

    def stop_crawl_tasks(self, task_names: dict):
        """
        停止统一调度抓取任务
        Args:
            task_names:

        Returns:

        """
        for model_id, task_name in task_names.items():
            url = f"{self.get_crawl_url_host()}/api/task/{task_name}"
            data = {
                "method": "finish"
            }
            request_with_retry(
                method="PUT",
                url=url,
                json=data,
            )

    def get_data_id(self, data: dict):
        """
            获取数据的ID，如果存在则返回data_id，否则返回id。如果都不存在，则报错。
        参数：
            data (dict) - 包含data_id或者id的字典，可以是一个空字典，但不能为None
        返回值（int）：
            如果存在data_id或者id，则返回对应的ID；如果都不存在，则报错
        """
        if data.get("data_id") is not None:
            return data.get("data_id")
        elif data.get("id") is not None:
            return data.get("id")
        else:
            self.report_error("there is no data_id or id in the data, please check your input data")

    def _teardown(self):
        """
        关闭函数，用于在自动评估完成后执行一些清理工作。

        Args:
            无参数。

        Returns:
            无返回值。

        """
        logger.info(
            "*******************************************operator teardown start"
            "********************************************"
        )
        logger.info("file store completing...")

    def _failed_post_process(self):
        """
        当手动打断或者执行失败的后处理逻辑
        """
        logger.info("Interrupt or error happened, do post process")

        dup_tasks = getattr(self, 'dup_tasks', None)
        if dup_tasks is not None:
            logger.info("dup_tasks is not None, do stop crawl tasks")
            self.stop_crawl_tasks(dup_tasks)

if __name__ == "__main__":
    user_op = Operator(sys.argv)
    user_op.execute()
