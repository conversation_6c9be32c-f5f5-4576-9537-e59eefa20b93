import os
import fal_client
import tkinter as tk
from tkinter import messagebox
import webbrowser

# 设置 fal.ai API 密钥
os.environ["FAL_KEY"] = "68b64ff8-2961-4cb6-b5bb-4483c2aff8a2:70d4042353d4ce47a49937a69a764125"  # 替换为你在 fal.ai 注册后获得的密钥

# 生成图像的函数
def generate_image(prompt):
    try:
        response = fal_client.run(
            "fal-ai/flux-pro/v1.1-ultra",
            arguments={
                "prompt": prompt,
                "image_size": "square",
                "num_inference_steps": 30,
                "guidance_scale": 7.5
            }
        )
        return response["images"][0]["url"]
    except Exception as e:
        return f"错误: {str(e)}"

# GUI 界面
def create_gui():
    window = tk.Tk()
    window.title("fal.ai 图像生成器")
    window.geometry("400x200")

    # 输入框
    tk.Label(window, text="请输入图像描述:").pack(pady=10)
    prompt_entry = tk.Entry(window, width=50)
    prompt_entry.pack(pady=5)

    # 生成按钮
    def on_generate():
        prompt = prompt_entry.get()
        if not prompt:
            messagebox.showwarning("警告", "请输入描述！")
            return
        result = generate_image(prompt)
        if result.startswith("错误"):
            messagebox.showerror("错误", result)
        else:
            messagebox.showinfo("成功", f"图像生成成功！点击确定打开图像。")
            webbrowser.open(result)

    tk.Button(window, text="生成图像", command=on_generate).pack(pady=20)

    window.mainloop()

# 运行程序
if __name__ == "__main__":
    create_gui()