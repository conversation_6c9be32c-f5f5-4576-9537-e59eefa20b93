import os
import sys
import json
import requests
import uuid

url = "https://rlenv-dev.baidu-int.com/tool_proxy"
def session_init():
    headers = {
            'Content-Type': 'application/json',
            'X-Tool-Name': 'mcp_session_init'
            }    
    payload = json.dumps({
        "server_ids": [130],
        "env_id": 161,
        "timeout_minutes": 5
        })
    result = requests.request("POST", url, headers=headers, data=payload).json()
    session_id = result["data"]["session_id"]
    return session_id

def tool_call(session_id):
    u1 = uuid.uuid1()
    headers = { 
            'Content-Type': 'application/json',
            'X-Tool-Name': 'mcp_tool_call'
            }
    payload = json.dumps({
        "arguments": "{\"path\":\"./file_system_output/task_34/new_website/css\"}",
        "message_content": "",
        "name": "filesystem__read_file",
        "call_id": u1,
        "session_id": session_id
        })
    result = requests.request("POST", url, headers=headers, data=payload).json() 
    message = result["message"]
    return message

if __name__ == '__main__':
    for i in range(128):
        session_id = session_init()
        print(session_id)
        message = tool_call(session_id)
        print(message)
