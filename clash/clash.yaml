# With https://blog.tyasaka.xyz/
# 作者Telegram:  @christine_tys
mixed-port: 7890
redir-port: 7891
tproxy-port: 1536
allow-lan: true
mode: rule
geodata-mode: true
geodata-loader: standard
unified-delay: true
log-level: info
ipv6: true
external-controller: 0.0.0.0:9090
tcp-concurrent: true
enable-process: true
find-process-mode: strict
global-client-fingerprint: chrome
keep-alive-interval: 30
geo-auto-update: true
geo-update-interval: 24
geox-url:
  geoip: "https://github.com/Loyalsoldier/v2ray-rules-dat/releases/latest/download/geoip.dat"
  geosite: "https://github.com/Loyalsoldier/v2ray-rules-dat/releases/latest/download/geosite.dat"
  mmdb: "https://github.com/Loyalsoldier/v2ray-rules-dat/releases/latest/download/country.mmdb"
profile:
  store-selected: true
  store-fake-ip: true
sniffer:
  enable: true
  force-dns-mapping: true
  parse-pure-ip: true
  override-destination: true
  sniff:
    HTTP:
      ports: [80, 8080-8880]
      override-destination: true
    TLS:
      ports: [443, 8443]
    QUIC:
      ports: [443, 8443]
  force-domain:
    - +.v2ex.com
  skip-domain:
    - "dlg.io.mi.com"
    - "+.push.apple.com"
    - "+.apple.com"
tun:
  enable: false
  device: MihomoCore
  stack: mixed
  mtu: 9000
  dns-hijack:
    - "any:53"
    - "tcp://any:53"
  auto-route: true
  auto-detect-interface: true
dns:
  enable: true
  ipv6: false
  default-nameserver: [*********, ************]
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  use-hosts: true
  nameserver: ['https://doh.pub/dns-query', 'https://dns.alidns.com/dns-query']
  fallback: ['https://doh.dns.sb/dns-query', 'https://dns.cloudflare.com/dns-query', 'https://dns.twnic.tw/dns-query', 'tls://*******:853']
  fallback-filter: { geoip: true, ipcidr: [240.0.0.0/4, 0.0.0.0/32] }

# 处理代理集合:ipv6优先案例
  p: &p
    {type: http, udp: true, ip-version: ipv6-prefer, smux: {enable: true}}
    
#	订阅组合
  u: &u
    use:
    - 1.provider1
    - 2.provider2
    - 3.provider3
    - 4.provider4

proxy-providers:
  1.provider1:
    <<: *p  
    type: http
# 节点文件 p1对应provider1
    path: ./proxy_provider/p1.yaml
# 订阅 链接
    url: https://times1738922731.subtangniu.top:9606/v2b/catnet/api/v1/client/subscribe?token=8fc3da35544bd3babd552719db019cd3
# 自动更新时间 21600(秒) / 3600 = 6小时
    interval: 21600
# 节点名称前缀 p1
    override:
      additional-prefix: "p1 |"
      
  2.provider2:
    <<: *p
    type: http
    path: ./proxy_provider/p2.yaml
    url: https://pqjc.site/api/v1/client/subscribe?token=f48e547dac51fd7f6071f461d8020af4
    interval: 21600
    override:
      additional-prefix: "p2 |"

  3.provider3:
    <<: *p
    type: file
    path: /Users/<USER>/code/_my/clash/changfeng.yaml
    override:
      additional-prefix: "p3 |"
  4.provider4:
    <<: *p
    type: http
    path: ./proxy_provider/p4.yaml
    url: https://api.dler.io/sub?target=clash&new_name=true&url=https%3A%2F%2Fproxy.v2gh.com%2Fhttps%3A%2F%2Fraw.githubusercontent.com%2FPawdroid%2FFree-servers%2Fmain%2Fsub&insert=false&config=https%3A%2F%2Fraw.githubusercontent.com%2FACL4SSR%2FACL4SSR%2Fmaster%2FClash%2Fconfig%2FACL4SSR_Online.ini
    interval: 21600
    override:
      additional-prefix: "p4 |"
proxy-groups:
  - {name: 🎯Mode, type: select, proxies: [🚀Manual, ♨️AutoSelect, ♨️AutoSelect1, ♨️AutoSelect2, ♻️ SELECT1,♻️ SELECT2,♻️ SELECT3,♻️ SELECT4,🍃DirectMode]}
  - {name: 🚀Manual, type: select, proxies: [], <<: *u}
  - {name: ♻️ SELECT1, type: select, proxies: [🚀Manual], use: [1.provider1]}
  - {name: ♻️ SELECT2, type: select, proxies: [🚀Manual], use: [2.provider2]}
  - {name: ♻️ SELECT3, type: select, proxies: [🚀Manual], use: [3.provider3]}
  - {name: ♻️ SELECT4, type: select, proxies: [🚀Manual], use: [4.provider4]}
  - {name: 🎮️Steam,type: select, proxies: [🎯Mode,🍃DirectMode,♨️AutoSelect]}
  - {name: 🪁Microsoft, type: select, proxies: [🎯Mode,🍃DirectMode,♨️AutoSelect]}
  - {name: 💬Telegram, type: select, proxies: [🎯Mode,♨️AutoSelect]}
  - {name: 🧭Google, type: select, proxies: [🎯Mode,♨️AutoSelect]}
  - {name: 🎏Final, type: select, proxies: [🎯Mode,]}
  - {name: 🍃DirectMode, type: select, proxies: [DIRECT]}
  # 自动选择案例
  - {name: ♨️AutoSelect, type: url-test, interval: 180, url: 'https://cp.cloudflare.com/generate_204', proxies: [], <<: *u}
  # provider1的自动选择
  - {name: ♨️AutoSelect1, type: url-test, interval: 180, url: 'https://cp.cloudflare.com/generate_204', use: [1.provider1]}
  # provider2的自动选择
  - {name: ♨️AutoSelect2, type: url-test, interval: 180, url: 'https://cp.cloudflare.com/generate_204', use: [2.provider2]}
  # provider3的自动选择
  - {name: ♨️AutoSelect3, type: url-test, interval: 180, url: 'https://cp.cloudflare.com/generate_204', use: [3.provider3]}
  # provider4的自动选择
  - {name: ♨️AutoSelect4, type: url-test, interval: 180, url: 'https://cp.cloudflare.com/generate_204', use: [4.provider4]}
  

rule-providers:
# 规则组合
  Telegram:
    type: http
    behavior: classical
    format: yaml
    path: ./rule/Telegram.yaml
    url: "https://blog.tyasaka.xyz/beta/Telegram.yaml"
    interval: 43200
  Google:
    type: http
    behavior: classical
    format: yaml
    path: ./rule/Google.yaml
    url: "https://blog.tyasaka.xyz/beta/Google.yaml"
    interval: 43200
  Steam:
    type: http
    format: yaml
    behavior: classical
    url: "https://blog.tyasaka.xyz/beta/steam.yaml"
    path: ./rule/steam.yaml
    interval: 43200
  SteamCN: 
    type: http
    behavior: classical
    format: yaml
    url: "https://blog.tyasaka.xyz/beta/steamcn.yaml"
    path: ./rule/steamcn.yaml
    interval: 43200
  Microsoft:
    type: http
    behavior: classical
    format: yaml
    path: ./rule/Microsoft.yaml
    url: "https://blog.tyasaka.xyz/beta/Microsoft.yaml"
    interval: 43200
  RE:
    type: http
    behavior: classical
    format: yaml
    url: "https://blog.tyasaka.xyz/beta/re.yaml"
    path: ./rule/re.yaml
    interval: 43200
  Emby:
    type: http
    behavior: classical
    format: yaml
    url: "https://blog.tyasaka.xyz/beta/emby.yaml"
    path: ./rule/emby.yaml
    interval: 43200
  FCM:
    type: http
    behavior: classical
    format: yaml
    url: "https://blog.tyasaka.xyz/beta/fcm.yaml"
    path: ./rule/fcm.yaml
    interval: 43200

rules:

# 解决Telegram持续转圈问题，可能引发闪退
  - IP-CIDR,10.0.0.0/8,🍃DirectMode
  - IP-CIDR,**********/12,🍃DirectMode
  - IP-CIDR,***********/16,🍃DirectMode
  - DOMAIN-SUFFIX,baidu-int.com,🍃DirectMode
  - DOMAIN-SUFFIX,baidu.com,🍃DirectMode
  # - DOMAIN-SUFFIX,xiaohumini.site,🍃DirectMode
  - IP-CIDR,*************/31,REJECT
# - Rule Group 应用规则，从上往下匹配
  - RULE-SET,SteamCN,🍃DirectMode
  - RULE-SET,Google,🧭Google
  - RULE-SET,Microsoft,🪁Microsoft
  - RULE-SET,Telegram,💬Telegram
  - RULE-SET,Steam,🎮️Steam
  - RULE-SET,FCM,🍃DirectMode
  - RULE-SET,RE,REJECT
  # GEO Rule
  - GEOSITE,CN,🍃DirectMode
  - GEOIP,CN,🍃DirectMode
  - MATCH,🎏Final