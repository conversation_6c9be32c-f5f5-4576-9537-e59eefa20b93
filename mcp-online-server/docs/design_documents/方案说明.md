MCP-支持RL-在线环境方案设计

# 背景
[训练场景使用MCP的需求](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tjIp17bwPd/bHU7z_AIW4/ZZLWBKOQ7zKzrI?t=mention&mt=doc&dt=doc)

# 整体方案架构设计
## 系统架构图


[流程图]
|优势|说明|
|-|-|
|安全隔离|完全的文件系统、网络、进程隔离|
|资源控制|精确的CPU、内存限制和监控|
|标准化环境|统一的运行时环境，避免环境差异|
|弹性扩容|基于K8s的自动扩缩容能力|
|日志集中|统一的日志收集和监控体系|
|故障恢复|容器级别的自动重启和故障恢复|

## 核心组件说明
### MCP在线服务
* **API网关**: 统一的接口入口，负责请求路由和参数验证
* **Session管理器**: 管理会话生命周期，关联容器和环境
* **工具调用管理器**: 支持同步和异步两种工具调用方式，管理调用状态
* **容器代理**: 封装容器管理平台API调用
* **状态监控器**: 实时监控容器状态和健康检查
* **日志收集器**: 收集和存储容器执行日志

### K8s容器实例
* **环境初始化模块**: 从BOS下载并解压环境包
* **MCP Runtime**: 加载和管理MCP服务器实例
* **任务轮询器**: 定期轮询MCP在线服务获取待执行的工具调用任务
* **工具执行引擎**: 执行具体的MCP工具调用
* **环境管理器**: 管理执行前后的环境快照和变更
* **状态上报模块**: 向MCP在线服务上报执行状态和结果
* **API客户端**: 与MCP在线服务的API交互（不直连数据库）

# 流程设计
## 准备流程
### MCP服务器注册流程
```
sequenceDiagram
    participant Client as 客户端/管理系统
    participant MCP as MCP在线服务
    participant MySQL as MySQL数据库

    Note over Client,MySQL: MCP服务器注册流程（简化验证）

    Client->>MCP: POST /api/v1/mcp-servers/register<br/>{"server_name": "filesystem", "command": "npx", "args": [...]}
    
    MCP->>MCP: 验证服务器配置参数格式
    
    alt 配置验证失败
        MCP-->>Client: 错误响应: 配置格式错误
    else 配置验证成功        
        MCP->>MySQL: 插入MCP服务器记录<br/>INSERT INTO mcp_servers<br/>(server_name, command, args, status='registered')
        MySQL-->>MCP: 插入成功
        
        MCP-->>Client: 返回注册结果<br/>{"server_name": "filesystem", "status": "registered"}
    end
```
### 环境初始化流程
```
sequenceDiagram
    participant Client as 客户端/管理系统
    participant MCP as MCP在线服务
    participant MySQL as MySQL数据

    Note over Client,MySQL: 环境初始化流程

    Client->>MCP: POST /api/v1/env/init<br/>{"name": "dev-env", "environment_dependency": [...]}
    
    MCP->>MCP: 验证环境配置参数
    
    MCP->>MCP: 创建临时初始化目录<br/>./init/temp_{uuid}/
    
    loop 处理environment_dependency
        MCP->>MCP: 根据类型创建文件/目录/数据库<br/>或下载远程文件
    end
    
    MCP->>MCP: 打包环境目录<br/>tar -czf mcp_env_{md5}.tar.gz ./

    MCP->>MCP: 计算环境包MD5哈希值   
     
    MCP->>MCP: 上传环境压缩包<br/>PUT /envs/mcp_env_{md5}.tar.gz<br\>返回BOS下载链接
    
    MCP->>MySQL: 插入环境记录<br/>INSERT INTO environments<br/>(env_md5, bos_url, environment_dependency)
    MySQL-->>MCP: 插入成功
    
    MCP->>MCP: 清理临时目录
    
    MCP-->>Client: 返回初始化结果<br/>{"env_md5": "...", "bos_url": "..."}
```
### Session初始化流程
```
sequenceDiagram
    participant RL as RL训练系统
    participant MCP as MCP在线服务
    participant MySQL as MySQL数据库
    participant ContainerAPI as 容器管理平台
    participant Container as K8s容器实例
    
    Note over RL,Container: Session初始化流程

    RL->>MCP: POST /api/v1/mcp/session/init<br/>{"server_ids": [1, 2], "env_id": 123}
    
    MCP->>MCP: 生成随机session_id
    
    MCP->>MySQL: 验证服务器和环境存在性<br/>SELECT * FROM mcp_servers WHERE id IN (...)<br/>SELECT * FROM environments WHERE id = ...
    MySQL-->>MCP: 返回服务器和环境配置
    
    MCP->>MCP: 构建容器初始化配置<br/>- 环境BOS URL<br/>- MCP服务器配置JSON<br/>- Session ID等
    
    MCP->>ContainerAPI: POST /containers/create<br/>{"image": "mcp-runtime", "env_vars": {"SESSION_ID": "...", "MCP_API_BASE": "...", ...}}
    ContainerAPI-->>MCP: 返回job_id
    
    MCP->>MySQL: 插入session记录<br/>INSERT INTO sessions<br/>(session_id, job_id, env_id, server_ids, status='initializing')
    MySQL-->>MCP: 插入成功
    
    Note over MCP,Container: 容器启动和初始化（异步）
    
    Container->>Container: 容器启动完成<br/>- 下载BOS环境包<br/>- 解压到工作目录<br/>- 加载MCP服务器
    
    Container->>MCP: POST /api/v1/internal/session/ready<br/>{"session_id": "...", "available_tools": [...]}
    
    MCP->>MySQL: 更新session状态<br/>UPDATE sessions SET container_status='running', session_status='running', available_tools=... WHERE session_id=...
    
    Note over MCP: 等待容器就绪
    
    loop 轮询等待容器就绪
        MCP->>ContainerAPI: GET /containers/{job_id}/status
        ContainerAPI-->>MCP: {"status": "running"}
        MCP->>MySQL: SELECT available_tools FROM sessions WHERE session_id=...
        MySQL-->>MCP: 返回available_tools状态
        
        alt 容器running且available_tools已设置
            MCP-->>RL: 返回session信息<br/>{"session_id": "...", "status": "running", "available_tools": [...]}
        else 继续等待
            MCP->>MCP: 等待2秒后继续轮询<br/>（设置超时时间，如5分钟）
        end
    end
```
## 工具调用流程
### 同步调用流程
```
sequenceDiagram
    participant RL as RL训练系统
    participant MCP as MCP在线服务
    participant MySQL as MySQL数据库
    participant Container as K8s容器实例

    Note over RL,Container: 同步工具调用流程

    RL->>MCP: POST /api/v1/tools/call<br/>{"session_id": "...", "name": "filesystem__read_file", "arguments": {...}, "id": "call_xxx"}
    
    MCP->>MySQL: 查询session信息<br/>验证session_id有效性和状态
    MySQL-->>MCP: 返回session配置
    
    MCP->>MySQL: 创建工具调用记录<br/>INSERT INTO tool_calls (call_id, session_id, tool_name, arguments, status='pending')
    MySQL-->>MCP: 插入成功
    
    Note over MCP,Container: 等待容器执行完成（同步等待）
    
    loop 等待执行完成
        Container->>MCP: GET /api/v1/internal/tool-calls/pending?session_id=...
        MCP->>MySQL: SELECT * FROM tool_calls WHERE session_id=... AND status='pending'
        MySQL-->>MCP: 返回pending的tool_call列表
        MCP-->>Container: 返回待执行任务<br/>[{"call_id": "call_xxx", "tool_name": "...", "arguments": {...}}]
        
        Container->>MCP: POST /api/v1/internal/tool-calls/claim<br/>{"call_id": "call_xxx"}
        MCP->>MySQL: UPDATE tool_calls SET status='running', started_at=NOW(3) WHERE call_id=...
        
        Container->>Container: 执行MCP工具调用<br/>包含环境快照和BOS上传
        
                    Container->>MCP: POST /api/v1/internal/tool-calls/complete<br/>{"call_id": "call_xxx", "result": {...}, "status": "success", "started_at": "..."}
            
            MCP->>MySQL: 更新调用记录<br/>UPDATE tool_calls SET result=..., status='success', started_at=..., completed_at=NOW(3), total_time_ms=..., queue_time_ms=...
        
        MCP->>MySQL: SELECT * FROM tool_calls WHERE call_id=...
        MySQL-->>MCP: 返回最终执行结果
    end
    
    MCP-->>RL: 返回执行结果<br/>{"call_id": "call_xxx", "status": "success", "result": {...}, "old_env_md5": "...", "new_env_md5": "..."}
```


### ~~异步调用流程~~
```
sequenceDiagram
    participant RL as RL训练系统
    participant MCP as MCP在线服务
    participant MySQL as MySQL数据库
    participant Container as K8s容器实例
    Note over RL,Container: 异步工具调用流程

    RL->>MCP: POST /api/v1/tools/call_async<br/>{"session_id": "...", "name": "filesystem__read_file", "arguments": {...}, "id": "call_xxx"}
    
    MCP->>MySQL: 查询session信息<br/>验证session_id有效性和状态
    MySQL-->>MCP: 返回session配置
    
    MCP->>MySQL: 创建工具调用记录<br/>INSERT INTO tool_calls (call_id, session_id, tool_name, arguments, status='pending')
    MySQL-->>MCP: 插入成功
    
    MCP-->>RL: 立即返回接受状态<br/>{"call_id": "call_xxx", "status": "pending", "message": "工具调用已提交"}
    
    Note over Container: 容器轮询和执行（异步）
    
    loop 容器轮询pending任务
        Container->>MCP: GET /api/v1/internal/tool-calls/pending?session_id=...
        MCP->>MySQL: SELECT * FROM tool_calls WHERE session_id=... AND status='pending'
        MySQL-->>MCP: 返回pending的tool_call列表
        MCP-->>Container: 返回待执行任务<br/>[{"call_id": "call_xxx", "tool_name": "...", "arguments": {...}}]
        
        alt 有待执行任务
            Container->>MCP: POST /api/v1/internal/tool-calls/claim<br/>{"call_id": "call_xxx"}
            MCP->>MySQL: UPDATE tool_calls SET status='running', started_at=NOW(3) WHERE call_id=...
            
            Container->>Container: 执行前环境快照<br/>计算当前环境MD5
            
            Container->>Container: 执行MCP工具调用<br/>调用对应的MCP服务器工具
            
            Container->>Container: 执行后环境快照<br/>计算新环境MD5
            
            alt 环境发生变化
                Container->>Container: 打包新环境状态<br/>tar -czf mcp_env_{new_md5}.tar.gz
                Container->>Container: 上传新环境包到bos<br/>PUT /envs/mcp_env_{new_md5}.tar.gz
            end
            
            Container->>MCP: POST /api/v1/internal/tool-calls/complete<br/>{"call_id": "call_xxx", "result": {...}, "old_env_md5": "...", "new_env_md5": "...", "status": "success", "started_at": "..."}
            
            MCP->>MySQL: 更新调用记录<br/>UPDATE tool_calls SET result=..., old_env_md5=..., new_env_md5=..., status='success', started_at=..., completed_at=NOW(3), total_time_ms=..., queue_time_ms=...
            
            alt 环境有变化需要更新environments表
                MCP->>MySQL: 插入新环境记录或更新引用<br/>INSERT INTO environments (...) 或 UPDATE sessions SET env_id=... WHERE session_id=...
            end
            
        else 无待执行任务
            Container->>Container: 等待3秒后继续轮询
        end
    end
    
    Note over RL,MCP: RL系统查询结果（独立请求）
    
    RL->>MCP: GET /api/v1/tools/call/status?call_id=call_xxx
    MCP->>MySQL: SELECT * FROM tool_calls WHERE call_id=...
    MySQL-->>MCP: 返回调用状态和结果
    
    alt 执行完成
        MCP-->>RL: 返回执行结果<br/>{"call_id": "call_xxx", "status": "success", "result": {...}, "old_env_md5": "...", "new_env_md5": "..."}
    else 仍在执行
        MCP-->>RL: 返回执行状态<br/>{"call_id": "call_xxx", "status": "running", "message": "工具执行中..."}
    end
```
# 数据库设计
```
-- MCP服务器表: 存储注册的MCP服务器配置信息
CREATE TABLE obj_register_mcp_server (
    server_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    server_name VARCHAR(64) UNIQUE NOT NULL DEFAULT "" COMMENT 'MCP服务器名称，全局唯一，如filesystem、sqlite等',
    command VARCHAR(256) NOT NULL DEFAULT "" COMMENT '启动命令，如npx、python等',
    args JSON NOT NULL COMMENT '启动参数数组，JSON格式存储',
    --version VARCHAR(64) DEFAULT "" COMMENT '服务器版本号',
    desc TEXT DEFAULT "" COMMENT '服务器功能描述',
    server_code_bos_url VARCHAR(256) NOT NULL DEFAULT "" COMMENT '用户上传的mcp server code的地址',
    --status ENUM('registered', 'active', 'deprecated', 'failed') DEFAULT 'registered' COMMENT '服务器状态：registered-已注册，active-活跃，deprecated-已废弃，failed-失败',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    INDEX idx_server_name (server_name) COMMENT '服务器名称索引',
    INDEX idx_status (status) COMMENT '状态索引'
) COMMENT = 'MCP服务器注册表，存储所有可用的MCP服务器配置';

-- 环境表: 存储MCP执行环境的配置和状态
CREATE TABLE obj_mcp_env (
    env_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    env_md5 VARCHAR(32) UNIQUE NOT NULL COMMENT '环境MD5哈希值，用于唯一标识环境版本',
    name VARCHAR(128) COMMENT '环境名称，用户友好的标识',
    description TEXT COMMENT '环境描述信息',
    bos_url VARCHAR(512) NOT NULL COMMENT 'BOS存储的环境压缩包下载地址',
    env_dependency JSON COMMENT '环境依赖配置，JSON格式存储文件、目录、数据库等依赖信息',
    file_size BIGINT COMMENT '环境压缩包文件大小，单位字节',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    INDEX idx_env_md5 (env_md5) COMMENT '环境MD5索引',
    INDEX idx_status (status) COMMENT '状态索引'
) COMMENT = 'MCP执行环境表，存储环境配置和BOS地址信息';

-- 会话状态表: 存储MCP会话的生命周期状态
CREATE TABLE obj_session (
    session_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键，会话ID',
    job_id VARCHAR(128) NOT NULL DEFAULT "" COMMENT '容器平台返回的作业ID，用于容器管理',
    env_id BIGINT NOT NULL DEFAULT "0" COMMENT '关联的环境ID，外键引用environments表的id字段',
    server_ids JSON NOT NULL DEFAULT "" COMMENT '关联的MCP服务器ID列表，JSON数组格式，存储mcp_servers表的id字段',
    mcp_tools JSON COMMENT '容器启动后上报的可用工具列表，包含工具名称、描述、参数等',
    container_status ENUM('pending', 'running', 'stopped', 'timeout', 'failed') DEFAULT 'pending' COMMENT '容器状态：pending-等待中，running-运行中，stopped-已停止，timeout-超时，failed-失败',
    --session_status ENUM('initializing', 'running', 'stopping', 'stopped', 'failed') DEFAULT 'initializing' COMMENT '会话状态：initializing-初始化中，running-运行中，stopping-停止中，stopped-已停止，failed-失败',
    --log_url VARCHAR(512) COMMENT '日志存储路径或URL',
    stopped_at TIMESTAMP(3) NULL COMMENT '停止时间，用于计算会话持续时间',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    INDEX idx_job_id (job_id) COMMENT '作业ID索引',
    INDEX idx_container_status (container_status) COMMENT '容器状态索引'
) COMMENT = 'MCP会话管理表，跟踪会话生命周期和容器状态';

-- 工具调用记录表: 存储所有MCP工具调用的详细记录
CREATE TABLE obj_tool_call_task (
    task_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键，任务ID',
    call_id VARCHAR(64) UNIQUE NOT NULL COMMENT '调用唯一标识符，UUID格式',
    session_id VARCHAR(64) NOT NULL COMMENT '关联的会话ID，外键引用sessions表',
    tool_name VARCHAR(128) NOT NULL COMMENT '工具名称，格式为server_name__tool_name，如filesystem__read_file',
    arguments JSON COMMENT '工具调用参数，JSON格式存储',
    result JSON COMMENT '工具执行结果，JSON格式存储',
    old_env_md5 VARCHAR(32) COMMENT '执行前环境MD5值',
    new_env_md5 VARCHAR(32) COMMENT '执行后环境MD5值，用于跟踪环境变化',
    old_env_url VARCHAR(512) COMMENT '执行前环境包url',
    new_env_url VARCHAR(512) COMMENT '执行后环境包url',
    status ENUM('pending', 'running', 'success', 'failed', 'timeout') DEFAULT 'pending' COMMENT '执行状态：pending-等待执行，running-执行中，success-成功，failed-失败，timeout-超时',
    error_message TEXT COMMENT '错误信息，失败时记录具体错误内容',
    started_at TIMESTAMP(3) NULL COMMENT '开始执行时间',
    completed_at TIMESTAMP(3) NULL COMMENT '完成时间',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    INDEX idx_call_id (call_id) COMMENT '调用ID索引',
    INDEX idx_session_id (session_id) COMMENT '会话ID索引',
    INDEX idx_tool_name (tool_name) COMMENT '工具名称索引',
    INDEX idx_status (status) COMMENT '状态索引',
    INDEX idx_created_at (created_at) COMMENT '创建时间索引',
) COMMENT = 'MCP工具调用记录表，存储所有工具调用的详细信息和执行结果';

```
# 接口设计
## 1. MCP服务注册接口
```
，接口名称: MCP服务器注册
路径: /api/v1/mcp/server/register
方法: POST
请求体: 
{
    "server_name": "filesystem",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem@2025.3.28", "./"],
    "description": "文件系统操作工具",
    "mock_mcp_server_code_url":"https://s3.bos.com/bucket_1/test.py"
}
响应体: 
{
    "code": 0,
    "message": "success",
    "data": {
        "server_id": 123,
        "server_name": "filesystem",
        "status": "registered"
    }
}
```
## 2. 环境初始化接口
```
接口名称: 环境初始化
路径: /api/v1/mcp/env/init
方法: POST
请求体: 
{
    "name": "dev-environment-v1",
    "description": "开发环境",
    "environment_dependency": [
        {
            "path": "./test_output/",
            "type": "directory",
            "content": ""
        },
        {
            "path": "./test.db",
            "type": "db",
            "content": "CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT);"
        },
        {
            "path": "./output/test.docx",
            "type": "url",
            "content": "https://s3.bos.com/bucket_1/test.docx"
        }
    ]
}
响应体: 
{
    "code": 0,
    "message": "success",
    "data": {
        "env_id": 123,
    }
}
```
## 3. Session初始化接口
```
接口名称: Session初始化
路径: /api/v1/mcp/session/init
方法: POST
请求体: 
{
    "server_ids": [1, 2],  // MCP服务器ID数组，对应mcp_servers表的id字段
    "env_id": 123,  // 环境ID，对应environments表的id字段
    "timeout_minutes": 60  // 会话超时时间，可选参数，默认60分钟
}
响应体: 
{
    "code": 0,
    "message": "success",
    "data": {
        "session_id": "sess_xyz789",
        "mcp_tools": [
            {
                "name": "filesystem__read_file",
                "description": "读取文件内容",
                "parameters": {...}
            },
            {
                "name": "sqlite__query",
                "description": "执行SQL查询",
                "parameters": {...}
            }
        ]
    }
}
```
## ~~4. 工具列表接口~~
```
接口名称: 获取可用工具列表
路径: /api/v1/mcp/tool/list
方法: GET
查询参数: session_id=sess_xyz789
响应体: 
{
    "code": 0,
    "message": "success",
    "data": {
        "session_id": "sess_xyz789",
        "available_tools": [
            {
                "name": "filesystem__read_file",
                "description": "读取文件内容",
                "parameters": {...}
            },
            {
                "name": "sqlite__query",
                "description": "执行SQL查询",
                "parameters": {...}
            }
        ]
    }
}
```
## 5. 工具调用接口
```
接口名称: 同步执行MCP工具
路径: /api/v1/mcp/tool/call
方法: POST

 //超时时间，默认30秒
请求体: 
{
    "session_id": "sess_xyz789",
    "name": "filesystem__read_file",
    "arguments": {
        "path": "./test.txt"
    },
    "call_id": "call_abc123",
    "timeout_seconds": 30 
}

// status: success/failed/timeout
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "call_id": "call_abc123",
        "status": "success", 
        "result": {
            "content": "文件内容...",
            "encoding": "utf-8"
        },
        "old_env_md5": "abc123def456",
        "new_env_md5": "def456ghi789",
        "old_env_url": "https://s3.bos.com/bucket_1/test.docx",
        "new_env_url": "https://s3.bos.com/bucket_1/test.docx",
    }
}
```
## ~~5.1. 异步工具调用接口~~
```
接口名称: 异步提交MCP工具调用
路径: /api/v1/mcp/tool/call_async
方法: POST
说明: 适用于执行时间较长或不确定的工具调用，如复杂计算、大文件处理、网络请求等
请求体: 
{
    "session_id": "sess_xyz789",
    "name": "filesystem__read_file",
    "arguments": {
        "path": "./test.txt"
    },
    "id": "call_abc123"
}
响应体: 
{
    "code": 0,
    "message": "success",
    "data": {
        "call_id": "call_abc123",
        "status": "pending",
        "message": "工具调用已提交，请通过status接口查询结果"
    }
}
```
## ~~5.2. 工具调用状态查询接口~~
```
接口名称: 查询工具调用状态
路径: /api/v1/mcp/tool/call/status
方法: GET
查询参数: call_id=call_abc123
响应体: 
{
    "code": 0,
    "message": "success",
    "data": {
        "call_id": "call_abc123",
        "status": "success",  // pending/running/success/failed/timeout
        "result": {
            "content": "文件内容...",
            "encoding": "utf-8"
        },
        "old_env_md5": "abc123def456",
        "new_env_md5": "def456ghi789",
        "execution_time_ms": 150,
        "total_time_ms": 1250,
        "queue_time_ms": 1100,
        "created_at": "2024-01-15T10:30:00.123Z",
        "started_at": "2024-01-15T10:30:01.223Z",
        "completed_at": "2024-01-15T10:30:01.373Z"
    }
}
```
## 6. Session停止接口
```
接口名称: 停止Session
路径: /api/v1/mcp/session/stop
方法: POST
请求体: 
{
    "session_id": "sess_xyz789",
    "force": false,
}
响应体: 
{
    "code": 0,
    "message": "success",
    "data": {
        "session_id": "sess_xyz789",
        "status": "stopping",
        "message": "容器停止请求已发送"
    }
}
```
## ~~7. Session状态查询接口~~
```
接口名称: 查询Session状态
路径: /api/v1/mcp/session/status
方法: GET
查询参数: session_id=sess_xyz789
响应体: 
{
    "code": 0,
    "message": "success",
    "data": {
        "session_id": "sess_xyz789",
        "container_status": "running",
        "session_status": "running",
        "env_id": 123,
        "current_env_md5": "def456ghi789",
        "uptime_seconds": 3600,
        "last_activity": "2024-01-15T10:30:00Z"
    }
}
```
## 8. 内部API接口（容器调用）
### 8.1 会话就绪通知
```
接口名称: 容器会话就绪通知
路径: /api/v1/mcp/internal/session/ready
方法: POST
请求体: 
{
    "session_id": "sess_xyz789",
    "mcp_tools": [
        {
            "name": "filesystem__read_file",
            "description": "读取文件内容",
            "parameters": {...}
        }
    ]
}
响应体: 
{
    "code": 0,
    "message": "会话状态已更新"
}
```
### 8.2 轮询待执行任务
```
接口名称: 获取待执行的工具调用任务
路径: /api/v1/mcp/internal/tool-call-task/status/pending
方法: GET
查询参数: session_id=sess_xyz789
响应体: 
{
    "code": 0,
    "message": "success",
    "data": {
        "tasks": [
            {
                "call_id": "call_abc123",
                "tool_name": "filesystem__read_file",
                "arguments": {
                    "path": "./test.txt"
                },
                "created_at": "2024-01-15T10:30:00Z"
            }
        ]
    }
}
```
### 8.3 声明任务执行
```
接口名称: 声明开始执行任务
路径: /api/v1/mcp/internal/tool-call-task/claim
方法: POST
请求体: 
{
    "call_id": "call_abc123",
    "worker_id": "container_worker_123"
}
响应体: 
{
    "code": 0,
    "message": "任务已声明，开始执行"
}
```
### 8.4 完成任务执行
```
接口名称: 完成任务执行并更新结果
路径: /api/v1/mcp/internal/tool-call-task/complete
方法: POST
请求体: 
{
    "call_id": "call_abc123",
    "status": "success",  // success/failed
    "result": {
        "content": "文件内容...",
        "encoding": "utf-8"
    },
    "old_env_md5": "abc123def456",
    "new_env_md5": "def456ghi789",
    "started_at": "2024-01-15T10:30:01.223456Z",  // 开始执行时间
    "error_message": null
}
响应体: 
{
    "code": 0,
    "message": "任务执行结果已更新"
}
```
# 容器管理平台接口规范
基于现有容器管理平台，需要封装以下接口：

## 容器WEB API周期管理
[dataeng_k8s交互服务接口设计](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tjIp17bwPd/_cb7F-QsrB/Ma2PHTQ84wYzZ0?t=mention&mt=doc&dt=doc)

# 可靠性
### 故障恢复
1. 容器健康检查: 每2秒检查容器状态
2. 自动重启: 容器异常退出自动重启（最多重试3次）
3. 自动环境恢复：从BOS拉取最近检查点环境数据

### 数据一致性保障
1. 事务控制：关键操作如session初始化和tool_call，状态更新使用事务，保证原子性
2. 数据备份：DB 1主2从
3. 幂等性设计：call_id 唯一幂等值

### 安全性设计
1. session并发控制：可配置最大并发session数量
2. 接口aksk权限校验

# 可观测性
* **配置BLS日志采集**
    * **访问日志**: API调用记录、响应时间、状态码
    * **业务日志**: Session生命周期、工具调用详情
    * **系统日志**: 服务启动、BRCC配置变更、异常错误
    * **容器日志**: 容器内MCP工具执行日志

* **Prometheus+Grafana监控告警配置**
    * session初始化耗时超过30s or 创建失败
    * 活动session数量> 最大限值80%
    * 工具调用失败or超时
    * 容器异常关闭


# 工作量&排期
### 阶段1: 0709交付 
> 提供基础可供联调的环境，整体数据流跑通完成自测验证
|任务|子任务|工作量（人日）|
|-|-|-|
|核心服务框架搭建|代码仓库初始化搭建GDP服务框架配置数据库连接和ORM表初始化基础RAL依赖服务配置引入|1|
|MCP服务器和环境管理@卢宇|实现MCP服务器注册接口实现环境初始化接口环境打包上传等|1|
|容器管理和Session初始化功能|封装容器管理平台API实现Session初始化流程容器状态监控和日志收集Session生命周期管理|1|
|工具调用|实现工具调用接口(/api/v1/tools/call)|2|
|容器内部逻辑|开发容器内MCP Runtime环境快照和状态同步内部API接口实现|2|

### 阶段2：可靠性和监控保障
|任务|子任务|工作量（人日）|
|-|-|-|
|RL-ENV proxy接入|RL-ENV proxy接入|0.5|
|安全加固|AKSK接口安全加固|0.5|
|故障恢复|容器故障自动恢复、自动重试Session限流习惯能压测|1|
|监控配置+运维开发|集成Prometheus/Grafana监控实现关键指标采集配置告警规则和通知性能基准测试|2|

