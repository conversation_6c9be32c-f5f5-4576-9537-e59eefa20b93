MCP-支持RL-在线环境方案设计

# 背景
[训练场景使用MCP的需求](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tjIp17bwPd/bHU7z_AIW4/ZZLWBKOQ7zKzrI?t=mention&mt=doc&dt=doc)

![](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=2384dab678cc42298b830b25d1192701&docGuid=8G2dzYUfWxsl-T "")

# 选型说明（server执行容器）
基于新的需求，统一采用K8s容器执行方案：

|优势|说明|
|-|-|
|安全隔离|完全的文件系统、网络、进程隔离|
|资源控制|精确的CPU、内存限制和监控|
|标准化环境|统一的运行时环境，避免环境差异|
|弹性扩容|基于K8s的自动扩缩容能力|
|日志集中|统一的日志收集和监控体系|
|故障恢复|容器级别的自动重启和故障恢复|

# 整体方案架构设计

## 系统架构图

```mermaid
graph TB
    subgraph "RL训练系统"
        A1["Session初始化"]
        A3["工具调用"]
    end
    
    subgraph "MCP在线服务引擎"
        B1["Session初始化<br/>生命周期管理"]
        B2["MCP工具调用管理器<br/>• 同步/异步调用<br/>• 状态监控<br/>• based on K8s-API"]
        B3["容器管理代理<br/>生命周期控制"]
        B4["环境状态同步<br/>快照管理"]
    end
    
    subgraph "容器管理平台 (K8s)"
        C1["MCP容器1<br/>• Runtime<br/>• Tool Exec"]
        C2["MCP容器2<br/>• Runtime<br/>• Tool Exec"]
        C3["MCP容器3<br/>• Runtime<br/>• Tool Exec"]
    end
    
    subgraph "外部服务"
        D1["MySQL<br/>数据库"]
        D2["BOS<br/>对象存储"]
        D3["监控告警<br/>系统"]
        D4["日志<br/>系统"]
    end
    
    %% 主要数据流
    A1 --> B1
    A3 --> B2
    
    B1 --> B3
    B2 --> B3
    B3 --> C1
    B3 --> C2
    B3 --> C3
    
    %% 容器轮询调用（虚线表示轮询）
    C1 -.-> B2
    C2 -.-> B2
    C3 -.-> B2
    
    %% 外部服务连接
    B1 --> D1
    B2 --> D1
    B4 --> D2
    B2 --> D3
    B2 --> D4
    
    %% 样式定义
    classDef systemStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef serviceStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef containerStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef storageStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class A1,A2,A3,A4,A5 systemStyle
    class B1,B2,B3,B4 serviceStyle
    class C1,C2,C3 containerStyle
    class D1,D2,D3,D4 storageStyle
```

**注意**: 容器只能主动调用MCP在线服务API，MCP在线服务不能主动调用容器

## 核心组件说明

### MCP在线服务
- **API网关**: 统一的接口入口，负责请求路由和参数验证
- **Session管理器**: 管理会话生命周期，关联容器和环境
- **工具调用管理器**: 支持同步和异步两种工具调用方式，管理调用状态
- **容器代理**: 封装容器管理平台API调用
- **状态监控器**: 实时监控容器状态和健康检查
- **日志收集器**: 收集和存储容器执行日志

### K8s容器实例
- **环境初始化模块**: 从BOS下载并解压环境包
- **MCP Runtime**: 加载和管理MCP服务器实例
- **任务轮询器**: 定期轮询MCP在线服务获取待执行的工具调用任务
- **工具执行引擎**: 执行具体的MCP工具调用
- **环境管理器**: 管理执行前后的环境快照和变更
- **状态上报模块**: 向MCP在线服务上报执行状态和结果
- **API客户端**: 与MCP在线服务的API交互（不直连数据库）

# 流程设计

## MCP服务器注册流程
```
sequenceDiagram
    participant Client as 客户端/管理系统
    participant MCP as MCP在线服务
    participant MySQL as MySQL数据库

    Note over Client,MySQL: MCP服务器注册流程（简化验证）

    Client->>MCP: POST /api/v1/mcp-servers/register<br/>{"server_name": "filesystem", "command": "npx", "args": [...]}
    
    MCP->>MCP: 验证服务器配置参数格式
    
    alt 配置验证失败
        MCP-->>Client: 错误响应: 配置格式错误
    else 配置验证成功        
        MCP->>MySQL: 插入MCP服务器记录<br/>INSERT INTO mcp_servers<br/>(server_name, command, args, status='registered')
        MySQL-->>MCP: 插入成功
        
        MCP-->>Client: 返回注册结果<br/>{"server_name": "filesystem", "status": "registered"}
    end
```

## 环境初始化流程
```
sequenceDiagram
    participant Client as 客户端/管理系统
    participant MCP as MCP在线服务
    participant MySQL as MySQL数据库
    participant BOS as BOS存储

    Note over Client,BOS: 环境初始化流程

    Client->>MCP: POST /api/v1/env/init<br/>{"name": "dev-env", "environment_dependency": [...]}
    
    MCP->>MCP: 验证环境配置参数
    
    MCP->>MCP: 创建临时初始化目录<br/>./init/temp_{uuid}/
    
    loop 处理environment_dependency
        MCP->>MCP: 根据类型创建文件/目录/数据库<br/>或下载远程文件
    end
    
    MCP->>MCP: 打包环境目录<br/>tar -czf mcp_env_{md5}.tar.gz ./

    MCP->>MCP: 计算环境包MD5哈希值   
     
    MCP->>BOS: 上传环境压缩包<br/>PUT /envs/mcp_env_{md5}.tar.gz
    BOS-->>MCP: 返回BOS下载链接
    
    MCP->>MySQL: 插入环境记录<br/>INSERT INTO environments<br/>(env_md5, bos_url, environment_dependency)
    MySQL-->>MCP: 插入成功
    
    MCP->>MCP: 清理临时目录
    
    MCP-->>Client: 返回初始化结果<br/>{"env_md5": "...", "bos_url": "..."}
```

## Session初始化流程
```
sequenceDiagram
    participant RL as RL训练系统
    participant MCP as MCP在线服务
    participant MySQL as MySQL数据库
    participant ContainerAPI as 容器管理平台
    participant Container as K8s容器实例
    participant BOS as BOS存储
    
    Note over RL,BOS: Session初始化流程

    RL->>MCP: POST /api/v1/session/init<br/>{"server_ids": [1, 2], "env_id": 123}
    
    MCP->>MCP: 生成随机session_id
    
    MCP->>MySQL: 验证服务器和环境存在性<br/>SELECT * FROM mcp_servers WHERE id IN (...)<br/>SELECT * FROM environments WHERE id = ...
    MySQL-->>MCP: 返回服务器和环境配置
    
    MCP->>MCP: 构建容器初始化配置<br/>- 环境BOS URL<br/>- MCP服务器配置JSON<br/>- Session ID等
    
    MCP->>ContainerAPI: POST /containers/create<br/>{"image": "mcp-runtime", "env_vars": {"SESSION_ID": "...", "MCP_API_BASE": "...", ...}}
    ContainerAPI-->>MCP: 返回job_id
    
    MCP->>MySQL: 插入session记录<br/>INSERT INTO sessions<br/>(session_id, job_id, env_id, server_ids, status='initializing')
    MySQL-->>MCP: 插入成功
    
    Note over MCP,Container: 容器启动和初始化（异步）
    
    Container->>Container: 容器启动完成<br/>- 下载BOS环境包<br/>- 解压到工作目录<br/>- 加载MCP服务器
    
    Container->>MCP: POST /api/v1/internal/session/ready<br/>{"session_id": "...", "available_tools": [...]}
    
    MCP->>MySQL: 更新session状态<br/>UPDATE sessions SET container_status='running', session_status='running', available_tools=... WHERE session_id=...
    
    Note over MCP: 等待容器就绪
    
    loop 轮询等待容器就绪
        MCP->>ContainerAPI: GET /containers/{job_id}/status
        ContainerAPI-->>MCP: {"status": "running"}
        MCP->>MySQL: SELECT available_tools FROM sessions WHERE session_id=...
        MySQL-->>MCP: 返回available_tools状态
        
        alt 容器running且available_tools已设置
            MCP-->>RL: 返回session信息<br/>{"session_id": "...", "status": "running", "available_tools": [...]}
        else 继续等待
            MCP->>MCP: 等待2秒后继续轮询<br/>（设置超时时间，如5分钟）
        end
    end
```

## MCP工具调用流程

### 同步调用流程
```
sequenceDiagram
    participant RL as RL训练系统
    participant MCP as MCP在线服务
    participant MySQL as MySQL数据库
    participant Container as K8s容器实例
    participant BOS as BOS存储

    Note over RL,BOS: 同步工具调用流程

    RL->>MCP: POST /api/v1/tools/call<br/>{"session_id": "...", "name": "filesystem__read_file", "arguments": {...}, "id": "call_xxx"}
    
    MCP->>MySQL: 查询session信息<br/>验证session_id有效性和状态
    MySQL-->>MCP: 返回session配置
    
    MCP->>MySQL: 创建工具调用记录<br/>INSERT INTO tool_calls (call_id, session_id, tool_name, arguments, status='pending')
    MySQL-->>MCP: 插入成功
    
    Note over MCP,Container: 等待容器执行完成（同步等待）
    
    loop 等待执行完成
        Container->>MCP: GET /api/v1/internal/tool-calls/pending?session_id=...
        MCP->>MySQL: SELECT * FROM tool_calls WHERE session_id=... AND status='pending'
        MySQL-->>MCP: 返回pending的tool_call列表
        MCP-->>Container: 返回待执行任务<br/>[{"call_id": "call_xxx", "tool_name": "...", "arguments": {...}}]
        
        Container->>MCP: POST /api/v1/internal/tool-calls/claim<br/>{"call_id": "call_xxx"}
        MCP->>MySQL: UPDATE tool_calls SET status='running', started_at=NOW(3) WHERE call_id=...
        
        Container->>Container: 执行MCP工具调用<br/>包含环境快照和BOS上传
        
                    Container->>MCP: POST /api/v1/internal/tool-calls/complete<br/>{"call_id": "call_xxx", "result": {...}, "status": "success", "started_at": "..."}
            
            MCP->>MySQL: 更新调用记录<br/>UPDATE tool_calls SET result=..., status='success', started_at=..., completed_at=NOW(3), total_time_ms=..., queue_time_ms=...
        
        MCP->>MySQL: SELECT * FROM tool_calls WHERE call_id=...
        MySQL-->>MCP: 返回最终执行结果
    end
    
    MCP-->>RL: 返回执行结果<br/>{"call_id": "call_xxx", "status": "success", "result": {...}, "old_env_md5": "...", "new_env_md5": "..."}
```

### 异步调用流程
```
sequenceDiagram
    participant RL as RL训练系统
    participant MCP as MCP在线服务
    participant MySQL as MySQL数据库
    participant Container as K8s容器实例
    participant BOS as BOS存储

    Note over RL,BOS: 异步工具调用流程

    RL->>MCP: POST /api/v1/tools/call_async<br/>{"session_id": "...", "name": "filesystem__read_file", "arguments": {...}, "id": "call_xxx"}
    
    MCP->>MySQL: 查询session信息<br/>验证session_id有效性和状态
    MySQL-->>MCP: 返回session配置
    
    MCP->>MySQL: 创建工具调用记录<br/>INSERT INTO tool_calls (call_id, session_id, tool_name, arguments, status='pending')
    MySQL-->>MCP: 插入成功
    
    MCP-->>RL: 立即返回接受状态<br/>{"call_id": "call_xxx", "status": "pending", "message": "工具调用已提交"}
    
    Note over Container,BOS: 容器轮询和执行（异步）
    
    loop 容器轮询pending任务
        Container->>MCP: GET /api/v1/internal/tool-calls/pending?session_id=...
        MCP->>MySQL: SELECT * FROM tool_calls WHERE session_id=... AND status='pending'
        MySQL-->>MCP: 返回pending的tool_call列表
        MCP-->>Container: 返回待执行任务<br/>[{"call_id": "call_xxx", "tool_name": "...", "arguments": {...}}]
        
        alt 有待执行任务
            Container->>MCP: POST /api/v1/internal/tool-calls/claim<br/>{"call_id": "call_xxx"}
            MCP->>MySQL: UPDATE tool_calls SET status='running', started_at=NOW(3) WHERE call_id=...
            
            Container->>Container: 执行前环境快照<br/>计算当前环境MD5
            
            Container->>Container: 执行MCP工具调用<br/>调用对应的MCP服务器工具
            
            Container->>Container: 执行后环境快照<br/>计算新环境MD5
            
            alt 环境发生变化
                Container->>Container: 打包新环境状态<br/>tar -czf mcp_env_{new_md5}.tar.gz
                Container->>BOS: 上传新环境包<br/>PUT /envs/mcp_env_{new_md5}.tar.gz
                BOS-->>Container: 返回上传成功
            end
            
            Container->>MCP: POST /api/v1/internal/tool-calls/complete<br/>{"call_id": "call_xxx", "result": {...}, "old_env_md5": "...", "new_env_md5": "...", "status": "success", "started_at": "..."}
            
            MCP->>MySQL: 更新调用记录<br/>UPDATE tool_calls SET result=..., old_env_md5=..., new_env_md5=..., status='success', started_at=..., completed_at=NOW(3), total_time_ms=..., queue_time_ms=...
            
            alt 环境有变化需要更新environments表
                MCP->>MySQL: 插入新环境记录或更新引用<br/>INSERT INTO environments (...) 或 UPDATE sessions SET env_id=... WHERE session_id=...
            end
            
        else 无待执行任务
            Container->>Container: 等待3秒后继续轮询
        end
    end
    
    Note over RL,MCP: RL系统查询结果（独立请求）
    
    RL->>MCP: GET /api/v1/tools/call/status?call_id=call_xxx
    MCP->>MySQL: SELECT * FROM tool_calls WHERE call_id=...
    MySQL-->>MCP: 返回调用状态和结果
    
    alt 执行完成
        MCP-->>RL: 返回执行结果<br/>{"call_id": "call_xxx", "status": "success", "result": {...}, "old_env_md5": "...", "new_env_md5": "..."}
    else 仍在执行
        MCP-->>RL: 返回执行状态<br/>{"call_id": "call_xxx", "status": "running", "message": "工具执行中..."}
    end
```

# 数据库设计

```sql
-- MCP服务器表: 存储注册的MCP服务器配置信息
CREATE TABLE mcp_servers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    server_name VARCHAR(64) UNIQUE NOT NULL COMMENT 'MCP服务器名称，全局唯一，如filesystem、sqlite等',
    command VARCHAR(256) NOT NULL COMMENT '启动命令，如npx、python等',
    args JSON NOT NULL COMMENT '启动参数数组，JSON格式存储',
    version VARCHAR(64) COMMENT '服务器版本号',
    description TEXT COMMENT '服务器功能描述',
    status ENUM('registered', 'active', 'deprecated', 'failed') DEFAULT 'registered' COMMENT '服务器状态：registered-已注册，active-活跃，deprecated-已废弃，failed-失败',
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    INDEX idx_server_name (server_name) COMMENT '服务器名称索引',
    INDEX idx_status (status) COMMENT '状态索引'
) COMMENT = 'MCP服务器注册表，存储所有可用的MCP服务器配置';

-- 环境表: 存储MCP执行环境的配置和状态
CREATE TABLE environments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    env_md5 VARCHAR(32) UNIQUE NOT NULL COMMENT '环境MD5哈希值，用于唯一标识环境版本',
    name VARCHAR(128) COMMENT '环境名称，用户友好的标识',
    description TEXT COMMENT '环境描述信息',
    bos_url VARCHAR(512) NOT NULL COMMENT 'BOS存储的环境压缩包下载地址',
    environment_dependency JSON COMMENT '环境依赖配置，JSON格式存储文件、目录、数据库等依赖信息',
    file_size BIGINT COMMENT '环境压缩包文件大小，单位字节',
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    status ENUM('active', 'archived', 'failed') DEFAULT 'active' COMMENT '环境状态：active-活跃，archived-已归档，failed-失败',
    INDEX idx_env_md5 (env_md5) COMMENT '环境MD5索引',
    INDEX idx_status (status) COMMENT '状态索引'
) COMMENT = 'MCP执行环境表，存储环境配置和BOS地址信息';

-- 会话状态表: 存储MCP会话的生命周期状态
CREATE TABLE sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    session_id VARCHAR(64) UNIQUE NOT NULL COMMENT '会话唯一标识，UUID格式',
    job_id VARCHAR(128) NOT NULL COMMENT '容器平台返回的作业ID，用于容器管理',
    env_id BIGINT NOT NULL COMMENT '关联的环境ID，外键引用environments表的id字段',
    server_ids JSON NOT NULL COMMENT '关联的MCP服务器ID列表，JSON数组格式，存储mcp_servers表的id字段',
    available_tools JSON COMMENT '容器启动后上报的可用工具列表，包含工具名称、描述、参数等',
    container_status ENUM('pending', 'running', 'stopped', 'failed') DEFAULT 'pending' COMMENT '容器状态：pending-等待中，running-运行中，stopped-已停止，failed-失败',
    session_status ENUM('initializing', 'running', 'stopping', 'stopped', 'failed') DEFAULT 'initializing' COMMENT '会话状态：initializing-初始化中，running-运行中，stopping-停止中，stopped-已停止，failed-失败',
    log_url VARCHAR(512) COMMENT '日志存储路径或URL',
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    stopped_at TIMESTAMP(6) NULL COMMENT '停止时间，用于计算会话持续时间',
    INDEX idx_session_id (session_id) COMMENT '会话ID索引',
    INDEX idx_job_id (job_id) COMMENT '作业ID索引',
    INDEX idx_env_id (env_id) COMMENT '环境ID索引',
    INDEX idx_container_status (container_status) COMMENT '容器状态索引',
    INDEX idx_session_status (session_status) COMMENT '会话状态索引',
    INDEX idx_created_at (created_at) COMMENT '创建时间索引'
) COMMENT = 'MCP会话管理表，跟踪会话生命周期和容器状态';

-- 工具调用记录表: 存储所有MCP工具调用的详细记录
CREATE TABLE tool_calls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    call_id VARCHAR(64) UNIQUE NOT NULL COMMENT '调用唯一标识符，UUID格式',
    session_id VARCHAR(64) NOT NULL COMMENT '关联的会话ID，外键引用sessions表',
    tool_name VARCHAR(128) NOT NULL COMMENT '工具名称，格式为server_name__tool_name，如filesystem__read_file',
    arguments JSON COMMENT '工具调用参数，JSON格式存储',
    result JSON COMMENT '工具执行结果，JSON格式存储',
    old_env_md5 VARCHAR(32) COMMENT '执行前环境MD5值',
    new_env_md5 VARCHAR(32) COMMENT '执行后环境MD5值，用于跟踪环境变化',
    execution_time_ms INT COMMENT '工具实际执行耗时，单位毫秒，不包含排队等待时间',
    total_time_ms INT COMMENT '端到端总耗时，单位毫秒，从创建到完成的总时间',
    queue_time_ms INT COMMENT '排队等待时间，单位毫秒，从创建到开始执行的时间',
    status ENUM('pending', 'running', 'success', 'failed', 'timeout') DEFAULT 'pending' COMMENT '执行状态：pending-等待执行，running-执行中，success-成功，failed-失败，timeout-超时',
    error_message TEXT COMMENT '错误信息，失败时记录具体错误内容',
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    started_at TIMESTAMP(6) NULL COMMENT '开始执行时间',
    completed_at TIMESTAMP(6) NULL COMMENT '完成时间',
    INDEX idx_call_id (call_id) COMMENT '调用ID索引',
    INDEX idx_session_id (session_id) COMMENT '会话ID索引',
    INDEX idx_tool_name (tool_name) COMMENT '工具名称索引',
    INDEX idx_status (status) COMMENT '状态索引',
    INDEX idx_created_at (created_at) COMMENT '创建时间索引',
    INDEX idx_started_at (started_at) COMMENT '开始执行时间索引',
    INDEX idx_completed_at (completed_at) COMMENT '完成时间索引',
    INDEX idx_old_env_md5 (old_env_md5) COMMENT '执行前环境MD5索引',
    INDEX idx_new_env_md5 (new_env_md5) COMMENT '执行后环境MD5索引'
) COMMENT = 'MCP工具调用记录表，存储所有工具调用的详细信息和执行结果';

-- 容器监控日志表: 存储容器运行时的日志信息
CREATE TABLE container_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    session_id VARCHAR(64) NOT NULL COMMENT '关联的会话ID，外键引用sessions表',
    job_id VARCHAR(128) NOT NULL COMMENT '容器作业ID，用于关联具体容器实例',
    log_level ENUM('DEBUG', 'INFO', 'WARN', 'ERROR') DEFAULT 'INFO' COMMENT '日志级别：DEBUG-调试，INFO-信息，WARN-警告，ERROR-错误',
    log_content TEXT NOT NULL COMMENT '日志内容，原始日志信息',
    timestamp TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT '日志时间戳',
    INDEX idx_session_id (session_id) COMMENT '会话ID索引',
    INDEX idx_job_id (job_id) COMMENT '作业ID索引',
    INDEX idx_log_level (log_level) COMMENT '日志级别索引',
    INDEX idx_timestamp (timestamp) COMMENT '时间戳索引，用于时间范围查询'
) COMMENT = '容器监控日志表，存储容器运行时产生的各类日志信息';

```

# 接口设计

## 1. MCP服务器注册接口
```yaml
接口名称: MCP服务器注册
路径: /api/v1/mcp-servers/register
方法: POST
请求体: {
    "server_name": "filesystem",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem@2025.3.28", "./"],
    "version": "2025.3.28",
    "description": "文件系统操作工具"
}
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "server_id": 123,
        "server_name": "filesystem",
        "status": "registered"
    }
}
```

## 2. 环境初始化接口
```yaml
接口名称: 环境初始化
路径: /api/v1/env/init
方法: POST
请求体: {
    "name": "dev-environment-v1",
    "description": "开发环境",
    "environment_dependency": [
        {
            "path": "./test_output/",
            "type": "directory",
            "content": ""
        },
        {
            "path": "./test.db",
            "type": "db",
            "content": "CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT);"
        },
        {
            "path": "./output/test.docx",
            "type": "url",
            "content": "https://s3.bos.com/bucket_1/test.docx"
        }
    ]
}
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "env_id": 123,
        "env_md5": "abc123def456",
        "bos_url": "https://bos.bj.bcebos.com/bucket/envs/mcp_env_abc123def456.tar.gz"
    }
}
```

## 3. Session初始化接口
```yaml
接口名称: Session初始化
路径: /api/v1/session/init
方法: POST
请求体: {
    "server_ids": [1, 2],  // MCP服务器ID数组，对应mcp_servers表的id字段
    "env_id": 123,  // 环境ID，对应environments表的id字段
    "timeout_minutes": 60  // 会话超时时间，可选参数，默认60分钟
}
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "session_id": "sess_xyz789",
        "job_id": "job_container_123",
        "status": "running",
        "available_tools": [
            {
                "name": "filesystem__read_file",
                "description": "读取文件内容",
                "parameters": {...}
            },
            {
                "name": "sqlite__query",
                "description": "执行SQL查询",
                "parameters": {...}
            }
        ]
    }
}
```

## 4. 工具列表接口
```yaml
接口名称: 获取可用工具列表
路径: /api/v1/tools/list
方法: GET
查询参数: session_id=sess_xyz789
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "session_id": "sess_xyz789",
        "available_tools": [
            {
                "name": "filesystem__read_file",
                "description": "读取文件内容",
                "parameters": {...}
            },
            {
                "name": "sqlite__query",
                "description": "执行SQL查询",
                "parameters": {...}
            }
        ]
    }
}
```

## 5. 同步工具调用接口
```yaml
接口名称: 同步执行MCP工具
路径: /api/v1/tools/call
方法: POST
说明: 适用于执行时间较短(< 30秒)的工具调用，如文件读取、简单查询等
请求体: {
    "session_id": "sess_xyz789",
    "name": "filesystem__read_file",
    "arguments": {
        "path": "./test.txt"
    },
    "id": "call_abc123",
    "timeout_seconds": 30  // 可选，超时时间，默认30秒
}
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "call_id": "call_abc123",
        "status": "success",  // success/failed/timeout
        "result": {
            "content": "文件内容...",
            "encoding": "utf-8"
        },
        "old_env_md5": "abc123def456",
        "new_env_md5": "def456ghi789",
        "execution_time_ms": 150,
        "total_time_ms": 1250,
        "queue_time_ms": 1100,
        "created_at": "2024-01-15T10:30:00.123Z",
        "started_at": "2024-01-15T10:30:01.223Z",
        "completed_at": "2024-01-15T10:30:01.373Z"
    }
}
```

## 5.1. 异步工具调用接口
```yaml
接口名称: 异步提交MCP工具调用
路径: /api/v1/tools/call_async
方法: POST
说明: 适用于执行时间较长或不确定的工具调用，如复杂计算、大文件处理、网络请求等
请求体: {
    "session_id": "sess_xyz789",
    "name": "filesystem__read_file",
    "arguments": {
        "path": "./test.txt"
    },
    "id": "call_abc123"
}
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "call_id": "call_abc123",
        "status": "pending",
        "message": "工具调用已提交，请通过status接口查询结果"
    }
}
```

## 5.2. 工具调用状态查询接口
```yaml
接口名称: 查询工具调用状态
路径: /api/v1/tools/call/status
方法: GET
查询参数: call_id=call_abc123
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "call_id": "call_abc123",
        "status": "success",  // pending/running/success/failed/timeout
        "result": {
            "content": "文件内容...",
            "encoding": "utf-8"
        },
        "old_env_md5": "abc123def456",
        "new_env_md5": "def456ghi789",
        "execution_time_ms": 150,
        "total_time_ms": 1250,
        "queue_time_ms": 1100,
        "created_at": "2024-01-15T10:30:00.123Z",
        "started_at": "2024-01-15T10:30:01.223Z",
        "completed_at": "2024-01-15T10:30:01.373Z"
    }
}
```

## 6. Session停止接口
```yaml
接口名称: 停止Session
路径: /api/v1/session/stop
方法: POST
请求体: {
    "session_id": "sess_xyz789",
    "force": false
}
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "session_id": "sess_xyz789",
        "status": "stopping",
        "message": "容器停止请求已发送"
    }
}
```

## 7. Session状态查询接口
```yaml
接口名称: 查询Session状态
路径: /api/v1/session/status
方法: GET
查询参数: session_id=sess_xyz789
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "session_id": "sess_xyz789",
        "container_status": "running",
        "session_status": "running",
        "env_id": 123,
        "current_env_md5": "def456ghi789",
        "uptime_seconds": 3600,
        "last_activity": "2024-01-15T10:30:00Z"
    }
}
```

## 8. 内部API接口（容器调用）

### 8.1 会话就绪通知
```yaml
接口名称: 容器会话就绪通知
路径: /api/v1/internal/session/ready
方法: POST
请求体: {
    "session_id": "sess_xyz789",
    "available_tools": [
        {
            "name": "filesystem__read_file",
            "description": "读取文件内容",
            "parameters": {...}
        }
    ]
}
响应体: {
    "code": 0,
    "message": "会话状态已更新"
}
```

### 8.2 轮询待执行任务
```yaml
接口名称: 获取待执行的工具调用任务
路径: /api/v1/internal/tool-calls/pending
方法: GET
查询参数: session_id=sess_xyz789&limit=10
响应体: {
    "code": 0,
    "message": "success",
    "data": {
        "tasks": [
            {
                "call_id": "call_abc123",
                "tool_name": "filesystem__read_file",
                "arguments": {
                    "path": "./test.txt"
                },
                "created_at": "2024-01-15T10:30:00Z"
            }
        ]
    }
}
```

### 8.3 声明任务执行
```yaml
接口名称: 声明开始执行任务
路径: /api/v1/internal/tool-calls/claim
方法: POST
请求体: {
    "call_id": "call_abc123",
    "worker_id": "container_worker_123"
}
响应体: {
    "code": 0,
    "message": "任务已声明，开始执行"
}
```

### 8.4 完成任务执行
```yaml
接口名称: 完成任务执行并更新结果
路径: /api/v1/internal/tool-calls/complete
方法: POST
请求体: {
    "call_id": "call_abc123",
    "status": "success",  // success/failed
    "result": {
        "content": "文件内容...",
        "encoding": "utf-8"
    },
    "old_env_md5": "abc123def456",
    "new_env_md5": "def456ghi789",
    "execution_time_ms": 150,
    "started_at": "2024-01-15T10:30:01.223456Z",  // 开始执行时间
    "error_message": null
}
响应体: {
    "code": 0,
    "message": "任务执行结果已更新"
}
```

# 容器管理平台接口规范

基于现有容器管理平台，需要封装以下接口：

## 容器生命周期管理
```yaml
# 容器初始化
POST /containers/create
请求体: {
    "image": "mcp-runtime:latest",
    "resources": {"cpu": "1", "memory": "2Gi"},
    "env_vars": {
        "MCP_ENV_URL": "https://bos.../mcp_env_xxx.tar.gz",
        "MCP_CONFIG": "base64编码的MCP服务器配置"
    }
}
响应: {"job_id": "job_123"}

# 容器状态查询
GET /containers/{job_id}/status
响应: {"status": "running", "health": "healthy"}

# 容器日志拉取
GET /containers/{job_id}/logs?since=timestamp
响应: {"logs": [...]}

# 容器销毁
DELETE /containers/{job_id}
响应: {"status": "terminating"}
```

# 监控和可靠性保障

## 监控体系

### 1. 系统层监控
- **服务可用性**: API接口响应时间、成功率、QPS监控
- **资源使用**: CPU、内存、磁盘、网络IO监控
- **数据库性能**: 连接池状态、慢查询、锁等待监控
- **存储状态**: BOS上传下载成功率、延迟监控

### 2. 业务层监控
- **Session管理**: 
  - Session创建成功率
  - Session平均存活时间
  - 并发Session数量
  - Session状态转换异常监控
- **容器管理**:
  - 容器启动成功率和启动时间
  - 容器健康检查失败率
  - 容器资源使用率分布
  - 容器异常退出统计
- **工具调用**:
  - 同步/异步工具调用成功率分布（按工具类型）
  - 工具执行时间分布（区分同步和异步）
  - 端到端耗时分析（total_time_ms）
  - 排队等待时间分析（queue_time_ms）  
  - 实际执行时间分析（execution_time_ms）
  - 同步调用超时率监控
  - 异步调用排队时长统计
  - 环境变更频率统计
  - 错误类型分析

### 3. 告警策略
```yaml
# 关键告警
- API可用性 < 99.5%
- Session创建失败率 > 5%
- 容器启动失败率 > 10%
- 数据库连接池耗尽
- BOS上传失败率 > 1%

# 预警告警  
- API响应时间 > P95 500ms
- 并发Session数 > 阈值80%
- 容器平均启动时间 > 60s
- 磁盘使用率 > 80%
```

## 可靠性保障

### 1. 故障恢复机制
- **容器故障恢复**:
  - 容器健康检查: 每30秒检查容器状态
  - 自动重启: 容器异常退出自动重启（最多3次）
  - 环境恢复: 从BOS重新下载环境包恢复状态
  - 优雅降级: 容器不可用时返回明确错误信息

- **数据一致性保障**:
  - 事务控制: 关键操作使用数据库事务
  - 状态同步: 定期同步容器状态到数据库
  - 数据备份: 关键数据定期备份
  - 幂等设计: 工具调用支持重试和幂等

### 2. 限流和熔断
- **接口限流**:
  - Session创建: 每用户每分钟最多5个
  - 同步工具调用: 每Session每秒最多5次（考虑同步等待开销）
  - 异步工具调用: 每Session每秒最多10次
  - 全局QPS限制: 避免系统过载

- **熔断机制**:
  - 容器平台接口熔断: 失败率>50%时暂停调用
  - 数据库连接熔断: 连接超时时使用备用策略
  - BOS上传熔断: 失败时使用本地缓存

### 3. 资源管理
- **Session生命周期管理**:
  - 超时回收: 空闲超过1小时自动停止
  - 资源限制: 单用户最多并发3个Session
  - 优先级调度: 重要任务优先分配资源

- **存储管理**:
  - 环境包清理: 超过7天未使用的环境包自动清理
  - 日志轮转: 容器日志按大小和时间轮转
  - 临时文件清理: 定期清理临时目录

### 4. 安全保障
- **访问控制**:
  - API Token认证
  - Session隔离: 不同Session无法访问彼此数据
  - 容器网络隔离: 容器只能访问授权的外部服务

- **数据安全**:
  - 敏感信息加密存储
  - BOS访问权限控制
  - 日志脱敏处理

## 日志体系

### 1. 日志分类
- **访问日志**: API调用记录、响应时间、状态码
- **业务日志**: Session生命周期、工具调用详情
- **系统日志**: 服务启动、配置变更、异常错误
- **容器日志**: 容器内MCP工具执行日志

### 2. 日志格式
```json
{
    "timestamp": "2024-01-15T10:30:00.123Z",
    "level": "INFO",
    "service": "mcp-online",
    "trace_id": "req_abc123",
    "session_id": "sess_xyz789",
    "operation": "tool_call",
    "details": {
        "tool_name": "filesystem__read_file",
        "execution_time_ms": 150,
        "status": "success"
    }
}
```

### 3. 日志存储和查询
- **存储策略**: 热数据7天，温数据30天，冷数据90天
- **索引策略**: 按时间、Session ID、操作类型建立索引
- **查询接口**: 提供日志查询API支持问题定位

# 性能优化

## 1. 缓存策略
- **环境包缓存**: 常用环境包在本地缓存
- **工具元数据缓存**: MCP服务器工具信息缓存
- **Session状态缓存**: 热点Session状态Redis缓存

## 2. 并发优化
- **异步处理**: 容器状态监控、日志收集异步执行
- **连接池**: 数据库连接池、HTTP连接池优化
- **批量操作**: 日志写入、状态更新批量处理

## 3. 容器优化
- **镜像优化**: 精简容器镜像，减少启动时间
- **预热机制**: 预创建容器池，减少冷启动时间
- **资源调度**: 智能调度算法，提高资源利用率


# 工作拆解

## 第一阶段：基础功能开发（7天）

### Day 1-2: 核心服务框架搭建
**负责人**: 后端开发
**工作内容**:
- 搭建Go/Python Web服务框架
- 配置数据库连接和ORM
- 实现基础的API路由和中间件
- 配置日志系统和基础监控

**交付物**:
- 可运行的Web服务框架
- 数据库表结构创建脚本
- 基础配置文件和部署脚本

### Day 3-4: MCP服务器和环境管理
**负责人**: 后端开发
**工作内容**:
- 实现MCP服务器注册接口
- 实现环境初始化接口
- 集成BOS SDK，实现文件上传下载
- 环境打包和MD5计算逻辑

**交付物**:
- MCP服务器注册功能
- 环境初始化和存储功能
- BOS集成和文件管理模块

### Day 5-6: 容器管理和Session功能
**负责人**: 后端开发 + 容器平台对接
**工作内容**:
- 封装容器管理平台API
- 实现Session初始化流程
- 容器状态监控和日志收集
- Session生命周期管理

**交付物**:
- 容器管理API封装
- Session初始化和管理功能
- 状态监控和日志收集模块

### Day 7: 工具调用和容器内部逻辑
**负责人**: 后端开发 + 容器开发
**工作内容**:
- 实现同步工具调用接口(/api/v1/tools/call)
- 实现异步工具调用接口(/api/v1/tools/call_async)  
- 实现工具调用状态查询接口(/api/v1/tools/call/status)
- 开发容器内MCP Runtime
- 环境快照和状态同步
- 内部API接口实现

**交付物**:
- 同步和异步工具调用完整流程
- 容器内MCP执行环境
- 环境状态管理功能

## 第二阶段：可靠性和监控保障（3天）

### Day 8: 监控体系建设
**负责人**: 运维开发 + 后端开发
**工作内容**:
- 集成Prometheus/Grafana监控
- 实现关键指标采集
- 配置告警规则和通知
- 性能基准测试

**交付物**:
- 完整的监控面板
- 告警规则配置
- 性能基准报告

### Day 9: 故障恢复和限流熔断
**负责人**: 后端开发
**工作内容**:
- 实现容器故障自动恢复
- 接口限流和熔断机制
- 数据一致性保障逻辑
- 异常处理和错误恢复

**交付物**:
- 故障恢复机制
- 限流熔断功能
- 异常处理完善

### Day 10: 安全加固和性能优化
**负责人**: 全团队
**工作内容**:
- 安全加固和权限控制
- 性能优化和缓存策略
- 压力测试和调优
- 生产环境部署准备

**交付物**:
- 安全策略实施
- 性能优化报告
- 生产环境部署方案
- 完整的运维手册

## 关键里程碑

1. **Day 2**: 基础框架可运行
2. **Day 4**: 环境管理功能完成
3. **Day 6**: Session管理功能完成  
4. **Day 7**: 端到端工具调用打通
5. **Day 9**: 监控和可靠性功能完成
6. **Day 10**: 生产环境就绪

## 风险控制

### 技术风险
- **容器平台API变更**: 及早对接确认接口稳定性
- **BOS存储限制**: 预估存储容量和并发限制
- **MCP协议兼容性**: 测试多种MCP服务器兼容性

### 进度风险  
- **并行开发依赖**: 明确接口契约，支持并行开发
- **测试时间不足**: 每日集成测试，及早发现问题
- **性能调优时间**: 预留性能调优和问题修复时间

### 质量保证
- **代码审查**: 关键模块代码审查
- **自动化测试**: 单元测试和集成测试覆盖
- **灰度发布**: 小流量验证后逐步放量