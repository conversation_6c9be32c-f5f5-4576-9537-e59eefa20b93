# 镜像功能使用说明

## 概述

新增了镜像管理功能，允许在session_init时指定不同的镜像配置，而不是使用默认的镜像配置。

## 数据库表结构

### obj_image 表

```sql
CREATE TABLE obj_image (
    image_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    image_path VARCHAR(256) NOT NULL DEFAULT "" COMMENT '镜像地址，如mcp/mcp-rumtime:latest',
    image_description TEXT COMMENT '镜像描述',
    container_port INT DEFAULT 0 COMMENT '容器端口，如8080',
    container_env JSON COMMENT '容器环境变量，JSON格式存储, 如{"PYTHONPATH": "/opt/python3.10/bin/python3.10"}',
    container_command VARCHAR(256) NOT NULL DEFAULT "" COMMENT '容器启动命令，如npx、python等',
    container_args JSON COMMENT '容器启动参数数组，JSON格式存储',
    container_mounts JSON COMMENT '容器挂载卷，JSON格式存储, 如{"input": "/home/<USER>/input", "output": "/home/<USER>/output"}',
    container_resources JSON COMMENT '容器资源配置，JSON格式存储, 如{"cpu": 1, "memory": {"value": 1, "type": "G"}}',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    is_delete tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    INDEX idx_image_path (image_path) COMMENT '镜像地址索引'
) COMMENT = 'MCP服务器注册表，存储服务器镜像配置';
```

## API 接口

### 1. 镜像初始化接口

**接口地址**: `POST /api/v1/mcp/image/init`

**请求参数**:
```json
{
    "image_path": "mcp/custom-runtime:latest",
    "image_description": "自定义MCP运行时镜像",
    "container_port": 8080,
    "container_env": {
        "PYTHONPATH": "/opt/python3.10/bin/python3.10",
        "NODE_ENV": "production"
    },
    "container_command": "python",
    "container_args": {
        "args": ["--stdio", "--config", "/path/to/config.json"]
    },
    "container_mounts": {
        "input": "/home/<USER>/input",
        "output": "/home/<USER>/output"
    },
    "container_resources": {
        "cpu": 1,
        "memory": {
            "value": 1,
            "type": "G"
        }
    }
}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "image_id": 123,
        "image_path": "mcp/custom-runtime:latest",
        "status": "initialized",
        "description": "自定义MCP运行时镜像"
    }
}
```

### 2. Session初始化接口（支持镜像）

**接口地址**: `POST /api/v1/mcp/session/init`

**请求参数**:
```json
{
    "server_ids": [1, 2, 3],
    "env_id": 1,
    "image_id": 123,
    "timeout_minutes": 30,
    "session_code": "optional-session-code",
    "use_server_prefix": "true"
}
```

**说明**:
- `image_id`: 可选参数，指定要使用的镜像ID
- 如果不指定`image_id`，将使用默认的镜像配置
- 如果指定了`image_id`，系统会验证镜像是否存在，并使用该镜像的配置来创建容器

## 使用流程

### 1. 创建自定义镜像配置

```bash
curl -X POST http://localhost:8080/api/v1/mcp/image/init \
  -H "Content-Type: application/json" \
  -d '{
    "image_path": "mcp/python-runtime:latest",
    "image_description": "Python MCP运行时",
    "container_command": "python",
    "container_args": {
        "args": ["-m", "mcp_server"]
    },
    "container_env": {
        "PYTHONPATH": "/opt/python3.10/bin/python3.10"
    }
  }'
```

### 2. 使用自定义镜像创建Session

```bash
curl -X POST http://localhost:8080/api/v1/mcp/session/init \
  -H "Content-Type: application/json" \
  -d '{
    "server_ids": [1, 2],
    "env_id": 1,
    "image_id": 123,
    "timeout_minutes": 30
  }'
```

## 注意事项

1. **镜像路径唯一性**: 每个镜像路径在系统中必须是唯一的
2. **容器参数格式**: `container_args` 应该是JSON格式，包含一个`args`数组
3. **资源配置**: `container_resources` 用于指定容器的CPU和内存限制
4. **环境变量**: `container_env` 用于设置容器的环境变量
5. **向后兼容**: 如果不指定`image_id`，系统会使用默认的镜像配置，保持向后兼容性

## 错误处理

- 如果指定的`image_id`不存在，会返回错误信息
- 如果镜像路径已存在，创建镜像时会返回错误
- 所有数据库操作失败都会有相应的错误日志和返回信息
