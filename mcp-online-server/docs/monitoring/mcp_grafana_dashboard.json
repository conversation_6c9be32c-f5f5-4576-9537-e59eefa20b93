{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_session_running_total{namespace=~\"$namespace\"} or mcp_session_running_total) + sum(mcp_tool_call_running_total{namespace=~\"$namespace\"} or mcp_tool_call_running_total)", "format": "table", "instant": true, "interval": "", "legendFormat": "系统总负载", "refId": "A"}], "title": "系统总负载", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 4, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 0}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "(sum(rate(mcp_tool_call_status_change_total{status=\"success\",namespace=~\"$namespace\"}[5m]) or rate(mcp_tool_call_status_change_total{status=\"success\"}[5m])) / sum(rate(mcp_tool_call_status_change_total{namespace=~\"$namespace\"}[5m]) or rate(mcp_tool_call_status_change_total[5m]))) * 100", "format": "table", "instant": true, "interval": "", "legendFormat": "成功率", "refId": "A"}], "title": "Tool Call成功率", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 2}, {"color": "red", "value": 5}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "rate(mcp_tool_call_execution_duration_seconds_sum[5m]) / rate(mcp_tool_call_execution_duration_seconds_count[5m]) or rate(mcp_tool_call_running_to_success_duration_seconds_sum[5m]) / rate(mcp_tool_call_running_to_success_duration_seconds_count[5m]) or vector(0)", "format": "table", "instant": true, "interval": "", "legendFormat": "平均响应时间", "refId": "A"}], "title": "平均响应时间", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_session_running_total{namespace=~\"$namespace\"} or mcp_session_running_total)", "format": "table", "instant": true, "interval": "", "legendFormat": "活跃Session", "refId": "A"}], "title": "活跃Session数", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_session_pending_total{namespace=~\"$namespace\"} or mcp_session_pending_total)", "interval": "", "legendFormat": "Pending", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_session_running_total{namespace=~\"$namespace\"} or mcp_session_running_total)", "interval": "", "legendFormat": "Running", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_session_stopped_total{namespace=~\"$namespace\"} or mcp_session_stopped_total)", "interval": "", "legendFormat": "Stopped", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_session_timeout_total{namespace=~\"$namespace\"} or mcp_session_timeout_total)", "interval": "", "legendFormat": "Timeout", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_session_failed_total{namespace=~\"$namespace\"} or mcp_session_failed_total)", "interval": "", "legendFormat": "Failed", "refId": "E"}], "title": "Session状态趋势", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_tool_call_pending_total{namespace=~\"$namespace\"} or mcp_tool_call_pending_total)", "interval": "", "legendFormat": "Pending", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_tool_call_running_total{namespace=~\"$namespace\"} or mcp_tool_call_running_total)", "interval": "", "legendFormat": "Running", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_tool_call_success_total{namespace=~\"$namespace\"} or mcp_tool_call_success_total)", "interval": "", "legendFormat": "Success", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_tool_call_failed_total{namespace=~\"$namespace\"} or mcp_tool_call_failed_total)", "interval": "", "legendFormat": "Failed", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_tool_call_timeout_total{namespace=~\"$namespace\"} or mcp_tool_call_timeout_total)", "interval": "", "legendFormat": "Timeout", "refId": "E"}], "title": "Tool Call状态趋势", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0.5}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 14}, "id": 7, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_session_long_running_total{namespace=~\"$namespace\"} or mcp_session_long_running_total)", "format": "table", "instant": true, "interval": "", "legendFormat": "长时间运行Session", "refId": "A"}], "title": "长时间运行Session告警", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0.5}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 14}, "id": 8, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_tool_call_long_pending_total{namespace=~\"$namespace\"} or mcp_tool_call_long_pending_total)", "format": "table", "instant": true, "interval": "", "legendFormat": "长时间Pending", "refId": "A"}], "title": "长时间Pending Tool Call告警", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0.5}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 14}, "id": 9, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_tool_call_long_running_total{namespace=~\"$namespace\"} or mcp_tool_call_long_running_total)", "format": "table", "instant": true, "interval": "", "legendFormat": "长时间Running", "refId": "A"}], "title": "长时间Running Tool Call告警", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 14}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "histogram_quantile(0.50, sum(rate(mcp_tool_call_execution_duration_seconds_bucket{namespace=~\"$namespace\"}[5m]) or rate(mcp_tool_call_execution_duration_seconds_bucket[5m])) by (le))", "interval": "", "legendFormat": "P50", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "histogram_quantile(0.95, sum(rate(mcp_tool_call_execution_duration_seconds_bucket{namespace=~\"$namespace\"}[5m]) or rate(mcp_tool_call_execution_duration_seconds_bucket[5m])) by (le))", "interval": "", "legendFormat": "P95", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(mcp_tool_call_execution_duration_seconds_bucket{namespace=~\"$namespace\"}[5m]) or rate(mcp_tool_call_execution_duration_seconds_bucket[5m])) by (le))", "interval": "", "legendFormat": "P99", "refId": "C"}], "title": "Tool Call执行时间分位数", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 36, "style": "dark", "tags": ["MCP", "监控"], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "Prometheus"}, "hide": 0, "includeAll": false, "label": "数据源", "multi": false, "name": "ds", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": false, "text": "dataeng-prod", "value": "dataeng-prod"}, "datasource": {"type": "prometheus", "uid": "${ds}"}, "definition": "label_values({__name__=~\"mcp_.*\"}, namespace)", "hide": 0, "includeAll": true, "label": "环境", "multi": true, "name": "namespace", "options": [], "query": {"query": "label_values({__name__=~\"mcp_.*\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "MCP Online Server 监控", "uid": "mcp-online-server-monitoring", "version": 1, "weekStart": ""}