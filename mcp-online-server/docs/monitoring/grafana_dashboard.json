{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"type": "prometheus", "uid": "${ds}"}, "exemplar": true, "expr": "sum(mcp_session_running_total) + sum(mcp_tool_call_running_total)", "format": "table", "instant": true, "interval": "", "legendFormat": "系统总负载", "refId": "A"}], "title": "系统总负载", "type": "stat"}, {"id": 2, "title": "Tool Call成功率", "type": "stat", "targets": [{"expr": "(rate(mcp_tool_call_status_change_total{status=\"success\"}[5m]) / rate(mcp_tool_call_status_change_total[5m])) * 100", "legendFormat": "成功率"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "平均响应时间", "type": "stat", "targets": [{"expr": "rate(mcp_tool_call_execution_duration_seconds_sum[5m]) / rate(mcp_tool_call_execution_duration_seconds_count[5m])", "legendFormat": "响应时间"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 2}, {"color": "red", "value": 5}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "活跃Session数", "type": "stat", "targets": [{"expr": "mcp_session_running_total", "legendFormat": "活跃Session"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Session状态分布", "type": "piechart", "targets": [{"expr": "mcp_session_pending_total", "legendFormat": "Pending"}, {"expr": "mcp_session_running_total", "legendFormat": "Running"}, {"expr": "mcp_session_stopped_total", "legendFormat": "Stopped"}, {"expr": "mcp_session_timeout_total", "legendFormat": "Timeout"}, {"expr": "mcp_session_failed_total", "legendFormat": "Failed"}], "options": {"legend": {"displayMode": "list", "placement": "right"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Session状态变化趋势", "type": "timeseries", "targets": [{"expr": "rate(mcp_session_status_change_total{status=\"pending\"}[1m])", "legendFormat": "Pending/min"}, {"expr": "rate(mcp_session_status_change_total{status=\"running\"}[1m])", "legendFormat": "Running/min"}, {"expr": "rate(mcp_session_status_change_total{status=\"stopped\"}[1m])", "legendFormat": "Stopped/min"}, {"expr": "rate(mcp_session_status_change_total{status=\"failed\"}[1m])", "legendFormat": "Failed/min"}, {"expr": "rate(mcp_session_status_change_total{status=\"timeout\"}[1m])", "legendFormat": "Timeout/min"}], "fieldConfig": {"defaults": {"unit": "ops"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Tool Call状态分布", "type": "barchart", "targets": [{"expr": "mcp_tool_call_pending_total", "legendFormat": "Pending"}, {"expr": "mcp_tool_call_running_total", "legendFormat": "Running"}, {"expr": "mcp_tool_call_success_total", "legendFormat": "Success"}, {"expr": "mcp_tool_call_failed_total", "legendFormat": "Failed"}, {"expr": "mcp_tool_call_timeout_total", "legendFormat": "Timeout"}], "options": {"orientation": "horizontal"}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "Tool Call执行时间分位数", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(mcp_tool_call_execution_duration_seconds_bucket[5m]))", "legendFormat": "P50"}, {"expr": "histogram_quantile(0.90, rate(mcp_tool_call_execution_duration_seconds_bucket[5m]))", "legendFormat": "P90"}, {"expr": "histogram_quantile(0.95, rate(mcp_tool_call_execution_duration_seconds_bucket[5m]))", "legendFormat": "P95"}, {"expr": "histogram_quantile(0.99, rate(mcp_tool_call_execution_duration_seconds_bucket[5m]))", "legendFormat": "P99"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "长时间运行Session告警", "type": "stat", "targets": [{"expr": "mcp_session_long_running_total", "legendFormat": "长时间运行"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 0.5}]}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 24}}, {"id": 10, "title": "长时间Pending Tool Call", "type": "stat", "targets": [{"expr": "mcp_tool_call_long_pending_total", "legendFormat": "长时间Pending"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 0.5}]}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 24}}, {"id": 11, "title": "长时间Running Tool Call", "type": "stat", "targets": [{"expr": "mcp_tool_call_long_running_total", "legendFormat": "长时间Running"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 0.5}]}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 24}}, {"id": 12, "title": "各工具执行频率", "type": "table", "targets": [{"expr": "sum by (tool_name) (rate(mcp_tool_call_execution_total[5m]))", "legendFormat": "{{tool_name}}"}], "fieldConfig": {"defaults": {"unit": "ops"}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "schemaVersion": 30, "version": 1}