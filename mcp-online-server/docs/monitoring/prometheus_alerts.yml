groups:
  - name: mcp_session_alerts
    rules:
      # Alert when failed session count increases rapidly
      - alert: MCPFailedSessionsIncreasing
        expr: increase(mcp_session_status_change_total{status="failed"}[10s]) > 3
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "MCP Failed Sessions Increasing"
          description: "Failed sessions have increased by more than 3 in the last 10 seconds"
          
      # Alert when timeout session count increases rapidly
      - alert: MCPTimeoutSessionsIncreasing
        expr: increase(mcp_session_status_change_total{status="timeout"}[10s]) > 3
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "MCP Timeout Sessions Increasing"
          description: "Timeout sessions have increased by more than 3 in the last 10 seconds"
          
      # Alert when sessions remain in running state for too long
      - alert: MCPLongRunningSessionsDetected
        expr: mcp_session_long_running_total > 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "MCP Long Running Sessions Detected"
          description: "{{ $value }} sessions have been running for more than 10 minutes"

  - name: mcp_tool_call_alerts
    rules:
      # Alert when unprocessed tool calls remain in pending state for too long
      - alert: MCPLongPendingToolCallsDetected
        expr: mcp_tool_call_long_pending_total > 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "MCP Long Pending Tool Calls Detected"
          description: "{{ $value }} tool calls have been pending for more than 5 minutes"

      # Alert when tool calls remain in running state for too long
      - alert: MCPLongRunningToolCallsDetected
        expr: mcp_tool_call_long_running_total > 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "MCP Long Running Tool Calls Detected"
          description: "{{ $value }} tool calls have been running for more than 5 minutes"
          
      # Alert when failed tool call count increases rapidly
      - alert: MCPFailedToolCallsIncreasing
        expr: increase(mcp_tool_call_status_change_total{status="failed"}[10s]) > 5
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "MCP Failed Tool Calls Increasing"
          description: "Failed tool calls have increased by more than 5 in the last 10 seconds"
          
      # Alert when timeout tool call count increases rapidly
      - alert: MCPTimeoutToolCallsIncreasing
        expr: increase(mcp_tool_call_status_change_total{status="timeout"}[10s]) > 5
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "MCP Timeout Tool Calls Increasing"
          description: "Timeout tool calls have increased by more than 5 in the last 10 seconds"

# Prometheus Query Examples for Monitoring Dashboards

# Session Monitoring Queries:
# 1. Current session counts by status:
#    sum by (status) (mcp_session_*_total)
#
# 2. Session status change rate:
#    rate(mcp_session_status_change_total[5m])
#
# 3. Average time from pending to running:
#    rate(mcp_session_pending_to_running_duration_seconds_sum[5m]) / rate(mcp_session_pending_to_running_duration_seconds_count[5m])
#
# 4. Average time from running to stopped:
#    rate(mcp_session_running_to_stopped_duration_seconds_sum[5m]) / rate(mcp_session_running_to_stopped_duration_seconds_count[5m])
#
# 5. Average session active duration:
#    rate(mcp_session_active_duration_seconds_sum[5m]) / rate(mcp_session_active_duration_seconds_count[5m])
#
# 6. 95th percentile session active duration:
#    histogram_quantile(0.95, sum(rate(mcp_session_active_duration_seconds_bucket[5m])) by (le))

# Tool Call Monitoring Queries:
# 1. Current tool call counts by status:
#    sum by (status) (mcp_tool_call_*_total)
#
# 2. Tool call status change rate:
#    rate(mcp_tool_call_status_change_total[5m])
#
# 3. Average scheduling time (pending to running):
#    rate(mcp_tool_call_pending_to_running_duration_seconds_sum[5m]) / rate(mcp_tool_call_pending_to_running_duration_seconds_count[5m])
#
# 4. Average execution time (running to success):
#    rate(mcp_tool_call_running_to_success_duration_seconds_sum[5m]) / rate(mcp_tool_call_running_to_success_duration_seconds_count[5m])
#
# 5. Tool call execution rate by tool name:
#    sum by (tool_name) (rate(mcp_tool_call_execution_total[5m]))
#
# 6. Average tool call execution time by tool name:
#    sum by (tool_name) (rate(mcp_tool_call_execution_duration_seconds_sum[5m])) / sum by (tool_name) (rate(mcp_tool_call_execution_duration_seconds_count[5m]))
#
# 7. 95th percentile tool call execution time by tool name:
#    histogram_quantile(0.95, sum by (tool_name, le) (rate(mcp_tool_call_execution_duration_seconds_bucket[5m])))
