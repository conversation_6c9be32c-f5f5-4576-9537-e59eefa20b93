# MCP Grafana Dashboard 修复说明

## 修复的问题

### 1. 平均响应时间 "No Data" 问题
**问题**: 平均响应时间显示 "No data"
**原因**:
1. 查询期望 namespace 标签存在，但实际 metrics 可能没有 namespace 标签
2. 需要提供备用查询和默认值

**修复**:
```promql
# 修复前
rate(mcp_tool_call_execution_duration_seconds_sum{namespace=~"$namespace"}[5m]) / rate(mcp_tool_call_execution_duration_seconds_count{namespace=~"$namespace"}[5m])

# 修复后 - 使用多重备用查询和默认值
rate(mcp_tool_call_execution_duration_seconds_sum[5m]) / rate(mcp_tool_call_execution_duration_seconds_count[5m]) or rate(mcp_tool_call_running_to_success_duration_seconds_sum[5m]) / rate(mcp_tool_call_running_to_success_duration_seconds_count[5m]) or vector(0)
```

### 2. 状态趋势图环境区分问题
**问题**: Session状态趋势和Tool Call状态趋势图中，每个状态显示6个线条，没有区分环境
**原因**: 查询没有按namespace过滤，也没有使用sum聚合合并多个节点的数据
**修复**: 所有状态查询都添加了namespace过滤和sum聚合，同时提供备用查询以处理没有namespace标签的情况

#### Session状态趋势修复示例:
```promql
# 修复前
mcp_session_pending_total

# 修复后 - 带备用查询
sum(mcp_session_pending_total{namespace=~"$namespace"} or mcp_session_pending_total)
```

#### Tool Call状态趋势修复示例:
```promql
# 修复前
mcp_tool_call_pending_total

# 修复后 - 带备用查询
sum(mcp_tool_call_pending_total{namespace=~"$namespace"} or mcp_tool_call_pending_total)
```

### 3. 添加环境变量配置
**新增**: namespace 变量，支持按环境过滤
- 默认值: dataeng-prod
- 支持多选: 是
- 支持全选: 是
- 数据源查询: `label_values({__name__=~"mcp_.*"}, namespace)` (更通用的查询)

### 4. Namespace 标签兼容性问题
**问题**: Metrics 可能没有 namespace 标签，导致所有查询返回空结果
**原因**: 应用层面的 metrics 默认不包含 namespace 标签，这些标签通常由 Prometheus 抓取配置或 Kubernetes 服务发现添加
**修复**: 所有查询都使用 `or` 操作符提供备用查询，确保即使没有 namespace 标签也能显示数据

#### 修复模式:
```promql
# 通用修复模式
sum(metric_name{namespace=~"$namespace"} or metric_name)

# 这样的查询会：
# 1. 首先尝试使用 namespace 过滤
# 2. 如果没有 namespace 标签，则使用原始 metric
# 3. 确保在任何情况下都有数据显示
```

## 修复的图表列表

1. **系统总负载** - 添加namespace过滤
2. **Tool Call成功率** - 添加namespace过滤和sum聚合
3. **平均响应时间** - 添加namespace过滤
4. **活跃Session数** - 添加namespace过滤和sum聚合
5. **Session状态趋势** - 所有状态(Pending/Running/Stopped/Timeout/Failed)添加namespace过滤和sum聚合
6. **Tool Call状态趋势** - 所有状态(Pending/Running/Success/Failed/Timeout)添加namespace过滤和sum聚合
7. **长时间运行Session告警** - 添加namespace过滤和sum聚合
8. **长时间Pending Tool Call告警** - 添加namespace过滤和sum聚合
9. **长时间Running Tool Call告警** - 添加namespace过滤和sum聚合
10. **Tool Call执行时间分位数** - 添加namespace过滤和sum聚合

## 修复效果

1. **解决No Data问题**:
   - 平均响应时间现在使用多重备用查询，确保有数据显示
   - 所有图表都有备用查询，即使没有namespace标签也能工作

2. **环境隔离**:
   - 可以通过namespace变量选择特定环境(dataeng-prod, dataeng-dev等)
   - 如果环境中没有namespace标签，会显示所有数据

3. **数据聚合**:
   - 多个节点的数据被正确聚合，避免重复显示
   - 使用sum聚合函数合并同类型的metrics

4. **统一显示**:
   - 每个状态只显示一条线，而不是每个节点一条线
   - 提供了向后兼容性，适用于有或没有namespace标签的环境

5. **鲁棒性增强**:
   - 查询具有容错能力，不会因为缺少特定标签而失败
   - 使用 `or vector(0)` 为某些查询提供默认值

## 使用说明

1. 导入修复后的Dashboard JSON文件
2. 在Dashboard顶部选择要监控的环境(namespace)
3. 可以选择单个环境或多个环境进行对比
4. 所有图表将根据选择的环境显示相应的数据
