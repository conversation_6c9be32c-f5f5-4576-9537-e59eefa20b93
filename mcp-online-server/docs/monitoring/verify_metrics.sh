#!/bin/bash

# MCP Online Server Metrics Verification Script
# 用于验证metrics实现是否正常工作

set -e

METRICS_ENDPOINT="http://localhost:8080/metrics"
TEMP_FILE="/tmp/mcp_metrics_output.txt"

echo "=== MCP Online Server Metrics Verification ==="
echo "Metrics endpoint: $METRICS_ENDPOINT"
echo

# Function to check if a metric exists
check_metric() {
    local metric_name=$1
    local description=$2
    
    if grep -q "^$metric_name" "$TEMP_FILE"; then
        echo "✅ $description: $metric_name"
        # Show the actual value
        grep "^$metric_name" "$TEMP_FILE" | head -1
    else
        echo "❌ $description: $metric_name (NOT FOUND)"
        return 1
    fi
    echo
}

# Function to check if a metric family exists (for histograms/counters with labels)
check_metric_family() {
    local metric_prefix=$1
    local description=$2
    
    if grep -q "^$metric_prefix" "$TEMP_FILE"; then
        echo "✅ $description: $metric_prefix*"
        # Show sample values
        grep "^$metric_prefix" "$TEMP_FILE" | head -3
    else
        echo "❌ $description: $metric_prefix* (NOT FOUND)"
        return 1
    fi
    echo
}

# Fetch metrics
echo "Fetching metrics from $METRICS_ENDPOINT..."
if curl -s "$METRICS_ENDPOINT" > "$TEMP_FILE"; then
    echo "✅ Successfully fetched metrics"
    echo "Total metrics lines: $(wc -l < "$TEMP_FILE")"
else
    echo "❌ Failed to fetch metrics from $METRICS_ENDPOINT"
    echo "Please ensure the MCP server is running and accessible"
    exit 1
fi
echo

echo "=== Session Metrics Verification ==="

# Session status gauges
check_metric "mcp_session_pending_total" "Session Pending Count"
check_metric "mcp_session_running_total" "Session Running Count"
check_metric "mcp_session_stopped_total" "Session Stopped Count"
check_metric "mcp_session_timeout_total" "Session Timeout Count"
check_metric "mcp_session_failed_total" "Session Failed Count"
check_metric "mcp_session_long_running_total" "Long Running Sessions Count"

# Session histograms
check_metric_family "mcp_session_pending_to_running_duration_seconds" "Session Pending→Running Duration"
check_metric_family "mcp_session_running_to_stopped_duration_seconds" "Session Running→Stopped Duration"
check_metric_family "mcp_session_running_to_timeout_duration_seconds" "Session Running→Timeout Duration"
check_metric_family "mcp_session_active_duration_seconds" "Session Active Duration"

# Session counters
check_metric_family "mcp_session_status_change_total" "Session Status Change Counter"

echo "=== Tool Call Metrics Verification ==="

# Tool call status gauges
check_metric "mcp_tool_call_pending_total" "Tool Call Pending Count"
check_metric "mcp_tool_call_running_total" "Tool Call Running Count"
check_metric "mcp_tool_call_success_total" "Tool Call Success Count"
check_metric "mcp_tool_call_failed_total" "Tool Call Failed Count"
check_metric "mcp_tool_call_timeout_total" "Tool Call Timeout Count"
check_metric "mcp_tool_call_long_pending_total" "Long Pending Tool Calls Count"
check_metric "mcp_tool_call_long_running_total" "Long Running Tool Calls Count"

# Tool call histograms
check_metric_family "mcp_tool_call_pending_to_running_duration_seconds" "Tool Call Pending→Running Duration"
check_metric_family "mcp_tool_call_running_to_success_duration_seconds" "Tool Call Running→Success Duration"
check_metric_family "mcp_tool_call_execution_duration_seconds" "Tool Call Execution Duration by Tool"

# Tool call counters
check_metric_family "mcp_tool_call_status_change_total" "Tool Call Status Change Counter"
check_metric_family "mcp_tool_call_execution_total" "Tool Call Execution Counter by Tool"

echo "=== Summary ==="
total_metrics=$(grep -c "^mcp_" "$TEMP_FILE" || echo "0")
echo "Total MCP metrics found: $total_metrics"

if [ "$total_metrics" -gt 0 ]; then
    echo "✅ MCP metrics implementation is working correctly!"
    echo
    echo "Sample MCP metrics:"
    grep "^mcp_" "$TEMP_FILE" | head -10
else
    echo "❌ No MCP metrics found. Please check the implementation."
    exit 1
fi

# Cleanup
rm -f "$TEMP_FILE"

echo
echo "=== Next Steps ==="
echo "1. Configure Prometheus to scrape $METRICS_ENDPOINT"
echo "2. Import alert rules from docs/prometheus_alerts.yml"
echo "3. Set up Grafana dashboards using queries from docs/metrics_implementation.md"
echo "4. Monitor logs for metrics collection status"
