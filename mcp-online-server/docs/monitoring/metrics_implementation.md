# MCP Online Server Metrics Implementation

This document describes the comprehensive monitoring and alerting capabilities implemented for the MCP (Model Control Protocol) online server.

## Overview

The implementation adds session-level and tool call-level metrics to track the MCP service lifecycle, providing real-time monitoring and alerting capabilities through Prometheus.

## Architecture

### Components

1. **Metrics Collection**: Custom Prometheus metrics integrated with the existing GDP framework
2. **Database Polling**: Periodic collection of status counts from the database
3. **Real-time Instrumentation**: Status change tracking in business logic
4. **Alert Rules**: Prometheus alert configurations for various failure scenarios

### Files Added/Modified

- `model/metrics/session_metrics.go` - Session-level metrics definitions
- `model/metrics/toolcall_metrics.go` - Tool call-level metrics definitions
- `model/metrics/collector.go` - Database polling for status counts
- `model/metrics/instrumentation.go` - Helper functions for business logic
- `model/metrics/metrics.go` - Main initialization
- `httpserver/module/metrics.go` - Integration with existing metrics system
- `docs/prometheus_alerts.yml` - Alert rule configurations

## Session Monitoring Metrics

### Real-time Status Counts (Gauges)

- `mcp_session_pending_total` - Current number of sessions in pending status
- `mcp_session_running_total` - Current number of sessions in running status
- `mcp_session_stopped_total` - Current number of sessions in stopped status
- `mcp_session_timeout_total` - Current number of sessions in timeout status
- `mcp_session_failed_total` - Current number of sessions in failed status

### Transition Time Metrics (Histograms)

- `mcp_session_pending_to_running_duration_seconds` - Time for pending→running transition
- `mcp_session_running_to_stopped_duration_seconds` - Time for running→stopped transition
- `mcp_session_running_to_timeout_duration_seconds` - Time for running→timeout transition
- `mcp_session_active_duration_seconds` - Total session active time

### Status Change Counters

- `mcp_session_status_change_total{status}` - Total status changes by status type

## Tool Call Monitoring Metrics

### Real-time Status Counts (Gauges)

- `mcp_tool_call_pending_total` - Current number of tool calls in pending status
- `mcp_tool_call_running_total` - Current number of tool calls in running status
- `mcp_tool_call_success_total` - Current number of tool calls in success status
- `mcp_tool_call_failed_total` - Current number of tool calls in failed status
- `mcp_tool_call_timeout_total` - Current number of tool calls in timeout status

### Execution Time Metrics (Histograms)

- `mcp_tool_call_pending_to_running_duration_seconds` - Scheduling time
- `mcp_tool_call_running_to_success_duration_seconds` - Execution time

### Tool-specific Metrics

- `mcp_tool_call_execution_total{tool_name, status}` - Executions by tool and status
- `mcp_tool_call_execution_duration_seconds{tool_name}` - Execution time by tool

### Status Change Counters

- `mcp_tool_call_status_change_total{status}` - Total status changes by status type

## Alert Configurations

### Session Alerts

1. **MCPFailedSessionsIncreasing**: Failed sessions increase by >3 in 10 seconds
2. **MCPTimeoutSessionsIncreasing**: Timeout sessions increase by >3 in 10 seconds
3. **MCPLongRunningSessionsDetected**: >5% of sessions run longer than 10 minutes

### Tool Call Alerts

1. **MCPUnprocessedToolCallsDetected**: Tool calls pending for >5 minutes
2. **MCPFailedToolCallsIncreasing**: Failed tool calls increase by >5 in 10 seconds
3. **MCPTimeoutToolCallsIncreasing**: Timeout tool calls increase by >5 in 10 seconds

## Implementation Details

### Metrics Collection Strategy

1. **Database Polling**: Every 15 seconds, query database for current status counts
2. **Real-time Events**: Record status changes and transitions as they occur
3. **Histogram Buckets**: Optimized for typical session/tool call durations

### Integration Points

The metrics system integrates with:
- GDP framework's existing Prometheus metrics
- Session business logic for status transitions
- Tool call business logic for execution tracking
- Background monitoring services

### Performance Considerations

- Metrics collection runs in background goroutines
- Database queries are optimized with proper indexing
- Histogram buckets are sized for expected workloads
- No blocking operations in business logic paths

## Usage Examples

### Prometheus Queries

```promql
# Current session distribution
sum by (status) (mcp_session_*_total)

# Session failure rate
rate(mcp_session_status_change_total{status="failed"}[5m])

# Average session duration
rate(mcp_session_active_duration_seconds_sum[5m]) / 
rate(mcp_session_active_duration_seconds_count[5m])

# Tool call success rate by tool
sum by (tool_name) (rate(mcp_tool_call_execution_total{status="success"}[5m])) /
sum by (tool_name) (rate(mcp_tool_call_execution_total[5m]))
```

### Grafana Dashboard Panels

1. **Session Status Overview**: Pie chart of current session counts
2. **Session Timeline**: Time series of session status changes
3. **Tool Call Performance**: Heatmap of execution times by tool
4. **Alert Status**: Current alert states and history

## Maintenance

### Adding New Metrics

1. Define metric in appropriate `*_metrics.go` file
2. Register in `RegisterXXXMetrics()` function
3. Add instrumentation calls in business logic
4. Update documentation and alert rules

### Modifying Alert Thresholds

Edit `docs/prometheus_alerts.yml` and apply to Prometheus configuration.

### Troubleshooting

- Check metrics collector logs for database connection issues
- Verify metrics registration in `/metrics` endpoint
- Monitor collector goroutine health in application logs
