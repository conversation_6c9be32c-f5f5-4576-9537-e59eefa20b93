# MCP Online Server 监控覆盖检查清单

## 分布式无状态部署特性

✅ **数据库作为单一数据源**: 所有metrics通过数据库查询获取，确保多节点间数据一致性
✅ **无本地状态依赖**: 不依赖节点本地状态，支持任意节点重启和扩缩容
✅ **实时状态同步**: 15秒间隔的数据库轮询确保metrics及时更新
✅ **分布式日志记录**: 每个节点独立记录metrics收集日志，便于问题排查

## Session 监控覆盖

### 实时状态计数 (Gauges)
✅ `mcp_session_pending_total` - 当前pending状态的session数量
✅ `mcp_session_running_total` - 当前running状态的session数量  
✅ `mcp_session_stopped_total` - 当前stopped状态的session数量
✅ `mcp_session_timeout_total` - 当前timeout状态的session数量
✅ `mcp_session_failed_total` - 当前failed状态的session数量
✅ `mcp_session_long_running_total` - 运行超过10分钟的session数量

### 状态转换时间 (Histograms)
✅ `mcp_session_pending_to_running_duration_seconds` - pending→running转换时间
✅ `mcp_session_running_to_stopped_duration_seconds` - running→stopped转换时间
✅ `mcp_session_running_to_timeout_duration_seconds` - running→timeout转换时间
✅ `mcp_session_active_duration_seconds` - session总活跃时间

### 状态变更计数 (Counters)
✅ `mcp_session_status_change_total{status}` - 按状态分组的状态变更总数

### 业务逻辑集成点
✅ Session创建时记录metrics (`Insert`)
✅ 状态更新时记录metrics (`UpdateJobIDAndStatus`, `UpdateMcpToolsAndStatus`, `UpdateStatusWithError`)
✅ 支持所有session状态转换的metrics记录

## Tool Call 监控覆盖

### 实时状态计数 (Gauges)
✅ `mcp_tool_call_pending_total` - 当前pending状态的tool call数量
✅ `mcp_tool_call_running_total` - 当前running状态的tool call数量
✅ `mcp_tool_call_success_total` - 当前success状态的tool call数量
✅ `mcp_tool_call_failed_total` - 当前failed状态的tool call数量
✅ `mcp_tool_call_timeout_total` - 当前timeout状态的tool call数量
✅ `mcp_tool_call_long_pending_total` - pending超过5分钟的tool call数量
✅ `mcp_tool_call_long_running_total` - running超过5分钟的tool call数量

### 执行时间 (Histograms)
✅ `mcp_tool_call_pending_to_running_duration_seconds` - 调度时间
✅ `mcp_tool_call_running_to_success_duration_seconds` - 执行时间

### 工具特定指标
✅ `mcp_tool_call_execution_total{tool_name, status}` - 按工具名和状态的执行计数
✅ `mcp_tool_call_execution_duration_seconds{tool_name}` - 按工具名的执行时间

### 状态变更计数 (Counters)
✅ `mcp_tool_call_status_change_total{status}` - 按状态分组的状态变更总数

### 业务逻辑集成点
✅ Tool call创建时记录metrics (`Insert`)
✅ 状态更新时记录metrics (`UpdateStatus`, `UpdateStatusInTx`)
✅ 支持所有tool call状态转换的metrics记录

## 告警配置覆盖

### Session 告警
✅ **MCPFailedSessionsIncreasing**: 失败session在10秒内增加超过3个
✅ **MCPTimeoutSessionsIncreasing**: 超时session在10秒内增加超过3个
✅ **MCPLongRunningSessionsDetected**: 有session运行超过10分钟

### Tool Call 告警
✅ **MCPLongPendingToolCallsDetected**: 有tool call pending超过5分钟
✅ **MCPLongRunningToolCallsDetected**: 有tool call运行超过5分钟
✅ **MCPFailedToolCallsIncreasing**: 失败tool call在10秒内增加超过5个
✅ **MCPTimeoutToolCallsIncreasing**: 超时tool call在10秒内增加超过5个

## 技术实现覆盖

### Metrics 收集架构
✅ **GDP框架集成**: 与现有gmetrics系统无缝集成
✅ **Prometheus兼容**: 所有metrics符合Prometheus规范
✅ **自动注册**: 服务启动时自动注册所有metrics
✅ **优雅关闭**: 支持metrics收集器的优雅停止

### 数据库查询优化
✅ **批量查询**: 使用GROUP BY减少数据库查询次数
✅ **索引友好**: 查询条件使用已有索引字段
✅ **错误处理**: 完善的数据库错误处理和日志记录

### 代码集成模式
✅ **循环依赖避免**: 使用helper包避免循环依赖
✅ **接口抽象**: 通过接口实现metrics记录的解耦
✅ **零侵入性**: 业务逻辑只需调用简单的helper函数

## 分布式部署验证

### 多节点一致性
✅ **数据源统一**: 所有节点从同一数据库获取metrics数据
✅ **无状态设计**: 不依赖节点间状态同步
✅ **独立运行**: 每个节点独立收集和报告metrics

### 性能考虑
✅ **轻量级查询**: 15秒间隔的简单COUNT查询
✅ **异步处理**: metrics收集在后台goroutine中进行
✅ **非阻塞**: 不影响主业务逻辑性能

### 故障恢复
✅ **容错设计**: 单个节点metrics收集失败不影响其他节点
✅ **自动重试**: 数据库连接失败时自动重试
✅ **降级处理**: metrics收集失败时服务仍可正常运行

## 监控查询示例

### 关键业务指标
- 当前系统负载: `sum(mcp_session_running_total) + sum(mcp_tool_call_running_total)`
- 成功率: `rate(mcp_tool_call_status_change_total{status="success"}[5m]) / rate(mcp_tool_call_status_change_total[5m])`
- 平均响应时间: `rate(mcp_tool_call_execution_duration_seconds_sum[5m]) / rate(mcp_tool_call_execution_duration_seconds_count[5m])`

### 异常检测
- 长时间运行检测: `mcp_session_long_running_total > 0 or mcp_tool_call_long_running_total > 0`
- 失败率异常: `increase(mcp_session_status_change_total{status="failed"}[10s]) > 3`
- 系统阻塞检测: `mcp_tool_call_long_pending_total > 0`

## 部署后验证步骤

1. ✅ 验证metrics endpoint可访问: `curl http://localhost:8080/metrics`
2. ✅ 确认所有metrics正常注册和更新
3. ✅ 测试多节点环境下metrics一致性
4. ✅ 验证告警规则触发正常
5. ✅ 确认日志记录完整且有用

## 总结

本实现提供了完整的MCP在线服务监控覆盖，特别针对多节点分布式无状态部署进行了优化。所有关键业务指标都有对应的metrics和告警，确保系统运行状态的全面可观测性。
