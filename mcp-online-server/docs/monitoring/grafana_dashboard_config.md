# Grafana Dashboard 配置指南

## 🎯 Dashboard 总览

建议创建一个综合性的Dashboard，包含以下几个主要部分：
1. **系统概览** - 关键指标总览
2. **Session监控** - Session生命周期监控
3. **Tool Call监控** - Tool Call执行监控
4. **告警状态** - 当前告警和异常状态

## 📈 Panel 配置详情

### 1. 系统概览 (Row 1)

#### Panel 1.1: 系统总负载 (Stat)
```promql
# Query
sum(mcp_session_running_total) + sum(mcp_tool_call_running_total)

# 配置
- Visualization: Stat
- Title: "系统总负载"
- Unit: short
- Color: Green (正常) / Yellow (>50) / Red (>100)
- Thresholds: 50, 100
```

#### Panel 1.2: Tool Call成功率 (Stat)
```promql
# Query
(
  rate(mcp_tool_call_status_change_total{status="success"}[5m]) / 
  rate(mcp_tool_call_status_change_total[5m])
) * 100

# 配置
- Visualization: Stat
- Title: "Tool Call成功率"
- Unit: percent (0-100)
- Color: Red (<90%) / Yellow (90-95%) / Green (>95%)
- Thresholds: 90, 95
```

#### Panel 1.3: 平均响应时间 (Stat)
```promql
# Query
rate(mcp_tool_call_execution_duration_seconds_sum[5m]) / 
rate(mcp_tool_call_execution_duration_seconds_count[5m])

# 配置
- Visualization: Stat
- Title: "平均响应时间"
- Unit: seconds
- Color: Green (<2s) / Yellow (2-5s) / Red (>5s)
- Thresholds: 2, 5
```

#### Panel 1.4: 活跃Session数 (Stat)
```promql
# Query
mcp_session_running_total

# 配置
- Visualization: Stat
- Title: "活跃Session数"
- Unit: short
- Color: Blue
```

### 2. Session监控 (Row 2)

#### Panel 2.1: Session状态分布 (Pie Chart)
```promql
# Queries (多个query)
mcp_session_pending_total    # Legend: Pending
mcp_session_running_total    # Legend: Running  
mcp_session_stopped_total    # Legend: Stopped
mcp_session_timeout_total    # Legend: Timeout
mcp_session_failed_total     # Legend: Failed

# 配置
- Visualization: Pie chart
- Title: "Session状态分布"
- Legend: Right side
- Colors: Blue, Green, Gray, Orange, Red
```

#### Panel 2.2: Session状态变化趋势 (Time Series)
```promql
# Queries
rate(mcp_session_status_change_total{status="pending"}[1m])   # Legend: Pending/min
rate(mcp_session_status_change_total{status="running"}[1m])   # Legend: Running/min
rate(mcp_session_status_change_total{status="stopped"}[1m])   # Legend: Stopped/min
rate(mcp_session_status_change_total{status="failed"}[1m])    # Legend: Failed/min
rate(mcp_session_status_change_total{status="timeout"}[1m])   # Legend: Timeout/min

# 配置
- Visualization: Time series
- Title: "Session状态变化趋势"
- Unit: ops/min
- Y-axis: Left
- Legend: Bottom
```

#### Panel 2.3: Session持续时间分布 (Heatmap)
```promql
# Query
rate(mcp_session_active_duration_seconds_bucket[5m])

# 配置
- Visualization: Heatmap
- Title: "Session持续时间分布"
- X-axis: Time
- Y-axis: Duration (seconds)
- Color scheme: Spectral
```

#### Panel 2.4: 长时间运行Session告警 (Stat)
```promql
# Query
mcp_session_long_running_total

# 配置
- Visualization: Stat
- Title: "长时间运行Session"
- Unit: short
- Color: Green (0) / Red (>0)
- Thresholds: 0.5
- Alert: 当值 > 0 时显示红色
```

### 3. Tool Call监控 (Row 3)

#### Panel 3.1: Tool Call状态分布 (Bar Chart)
```promql
# Queries
mcp_tool_call_pending_total   # Legend: Pending
mcp_tool_call_running_total   # Legend: Running
mcp_tool_call_success_total   # Legend: Success
mcp_tool_call_failed_total    # Legend: Failed
mcp_tool_call_timeout_total   # Legend: Timeout

# 配置
- Visualization: Bar chart
- Title: "Tool Call状态分布"
- Orientation: Horizontal
- Colors: Blue, Yellow, Green, Red, Orange
```

#### Panel 3.2: Tool Call执行时间趋势 (Time Series)
```promql
# Queries
histogram_quantile(0.50, rate(mcp_tool_call_execution_duration_seconds_bucket[5m]))  # Legend: P50
histogram_quantile(0.90, rate(mcp_tool_call_execution_duration_seconds_bucket[5m]))  # Legend: P90
histogram_quantile(0.95, rate(mcp_tool_call_execution_duration_seconds_bucket[5m]))  # Legend: P95
histogram_quantile(0.99, rate(mcp_tool_call_execution_duration_seconds_bucket[5m]))  # Legend: P99

# 配置
- Visualization: Time series
- Title: "Tool Call执行时间分位数"
- Unit: seconds
- Y-axis: Left
- Legend: Right
```

#### Panel 3.3: 各工具执行情况 (Table)
```promql
# Query
sum by (tool_name) (rate(mcp_tool_call_execution_total[5m]))

# 配置
- Visualization: Table
- Title: "各工具执行频率"
- Columns: Tool Name, Executions/min
- Sort: By executions desc
```

#### Panel 3.4: Tool Call异常监控 (Time Series)
```promql
# Queries
mcp_tool_call_long_pending_total   # Legend: 长时间Pending
mcp_tool_call_long_running_total   # Legend: 长时间Running
rate(mcp_tool_call_status_change_total{status="failed"}[1m])    # Legend: 失败率/min
rate(mcp_tool_call_status_change_total{status="timeout"}[1m])   # Legend: 超时率/min

# 配置
- Visualization: Time series
- Title: "Tool Call异常监控"
- Unit: short
- Y-axis: Left
- Colors: Orange, Red, Dark Red, Purple
```

### 4. 性能分析 (Row 4)

#### Panel 4.1: Session转换时间分析 (Time Series)
```promql
# Queries
histogram_quantile(0.95, rate(mcp_session_pending_to_running_duration_seconds_bucket[5m]))  # Legend: Pending→Running P95
histogram_quantile(0.95, rate(mcp_session_running_to_stopped_duration_seconds_bucket[5m]))  # Legend: Running→Stopped P95

# 配置
- Visualization: Time series
- Title: "Session转换时间分析"
- Unit: seconds
- Y-axis: Left
```

#### Panel 4.2: Tool Call调度延迟 (Time Series)
```promql
# Query
histogram_quantile(0.95, rate(mcp_tool_call_pending_to_running_duration_seconds_bucket[5m]))

# 配置
- Visualization: Time series
- Title: "Tool Call调度延迟 (P95)"
- Unit: seconds
- Y-axis: Left
```

### 5. 告警状态 (Row 5)

#### Panel 5.1: 当前告警状态 (Stat Panel Grid)

创建多个小的Stat Panel：

```promql
# 失败Session增长告警
increase(mcp_session_status_change_total{status="failed"}[10s]) > 3

# 超时Session增长告警  
increase(mcp_session_status_change_total{status="timeout"}[10s]) > 3

# 长时间运行Session告警
mcp_session_long_running_total > 0

# 长时间Pending Tool Call告警
mcp_tool_call_long_pending_total > 0

# 长时间Running Tool Call告警
mcp_tool_call_long_running_total > 0

# 失败Tool Call增长告警
increase(mcp_tool_call_status_change_total{status="failed"}[10s]) > 5

# 超时Tool Call增长告警
increase(mcp_tool_call_status_change_total{status="timeout"}[10s]) > 5
```

每个Panel配置：
- Visualization: Stat
- Unit: short
- Color: Green (0) / Red (>0)
- Size: Small

## 🎨 Dashboard 全局设置

### 时间范围设置
- Default: Last 1 hour
- Quick ranges: 5m, 15m, 1h, 6h, 24h
- Refresh: 30s

### 变量设置 (可选)
```
# 实例变量 (如果有多个实例)
Name: instance
Type: Query
Query: label_values(mcp_session_pending_total, instance)
Multi-value: true
Include All: true
```

### 告警设置
在每个关键Panel上设置告警：
1. Tool Call成功率 < 90%
2. 平均响应时间 > 5s
3. 长时间运行Session > 0
4. 长时间Pending Tool Call > 0

## 📱 移动端优化
- 使用响应式布局
- 关键指标放在顶部
- 简化复杂图表的移动端显示
