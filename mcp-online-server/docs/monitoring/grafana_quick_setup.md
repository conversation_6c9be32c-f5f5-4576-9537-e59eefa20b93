# MCP Grafana Dashboard 快速配置指南

## 🚀 快速导入

### 1. 导入Dashboard

1. 登录Grafana管理界面
2. 点击左侧菜单的 "+" > "Import"
3. 选择 "Upload JSON file"
4. 上传 `docs/mcp_grafana_dashboard.json` 文件
5. 在导入页面：
   - 选择您的Prometheus数据源
   - 确认Dashboard名称为 "MCP Online Server 监控"
   - 点击 "Import"

### 2. 验证数据源

确保您的Prometheus已经配置为抓取MCP服务的metrics：

```yaml
# prometheus.yml 配置示例
scrape_configs:
  - job_name: 'mcp-online-server'
    static_configs:
      - targets: ['your-mcp-server:8080']  # 替换为您的MCP服务地址
    metrics_path: '/metrics'
    scrape_interval: 15s
```

### 3. 检查Metrics可用性

在导入Dashboard之前，先在Prometheus UI中验证metrics是否可用：

```promql
# 检查基础metrics
mcp_session_running_total
mcp_tool_call_running_total
mcp_tool_call_status_change_total
```

## 📊 Dashboard 内容

导入的Dashboard包含以下Panel：

### 第一行 - 系统概览
- **系统总负载**: 当前运行的Session和Tool Call总数
- **Tool Call成功率**: 最近5分钟的成功率百分比
- **平均响应时间**: Tool Call平均执行时间
- **活跃Session数**: 当前运行中的Session数量

### 第二行 - 状态趋势
- **Session状态趋势**: 各状态Session数量的时间序列图
- **Tool Call状态趋势**: 各状态Tool Call数量的时间序列图

### 第三行 - 告警监控
- **长时间运行Session告警**: 运行超过10分钟的Session数量
- **长时间Pending Tool Call告警**: Pending超过5分钟的Tool Call数量
- **长时间Running Tool Call告警**: Running超过5分钟的Tool Call数量
- **Tool Call执行时间分位数**: P50/P95/P99执行时间趋势

## ⚠️ 告警配置

### 关键告警规则

为以下Panel配置告警：

1. **Tool Call成功率** < 90%
   ```
   Alert Condition: (rate(mcp_tool_call_status_change_total{status="success"}[5m]) / rate(mcp_tool_call_status_change_total[5m])) * 100 < 90
   ```

2. **长时间运行Session** > 0
   ```
   Alert Condition: mcp_session_long_running_total > 0
   ```

3. **长时间Pending Tool Call** > 0
   ```
   Alert Condition: mcp_tool_call_long_pending_total > 0
   ```

4. **平均响应时间** > 5秒
   ```
   Alert Condition: rate(mcp_tool_call_execution_duration_seconds_sum[5m]) / rate(mcp_tool_call_execution_duration_seconds_count[5m]) > 5
   ```

### 配置告警步骤

1. 点击Panel右上角的编辑图标
2. 切换到 "Alert" 选项卡
3. 点击 "Create Alert"
4. 设置告警条件和阈值
5. 配置评估间隔（建议60秒）
6. 设置通知渠道
7. 保存告警配置

## 🔧 自定义配置

### 修改刷新间隔

Dashboard默认30秒自动刷新，可以在右上角时间选择器旁修改。

### 添加变量过滤

如果有多个MCP实例，可以添加实例变量：

1. Dashboard设置 > Variables > Add variable
2. 配置：
   - Name: `instance`
   - Type: `Query`
   - Query: `label_values(mcp_session_pending_total, instance)`
   - Multi-value: true
   - Include All: true

### 调整时间范围

默认显示最近1小时数据，可以：
- 在右上角时间选择器中修改
- 在Dashboard设置中修改默认时间范围

## 🐛 故障排查

### 无数据显示

1. **检查Prometheus连接**：
   ```bash
   curl http://your-prometheus:9090/api/v1/query?query=up
   ```

2. **检查MCP metrics端点**：
   ```bash
   curl http://your-mcp-server:8080/metrics | grep mcp_
   ```

3. **验证metrics名称**：
   在Prometheus UI中测试查询：`{__name__=~"mcp_.*"}`

### Panel显示错误

1. 检查PromQL语法是否正确
2. 确认metrics标签名称匹配
3. 验证时间范围内是否有数据

### 告警不触发

1. 检查告警条件表达式
2. 验证评估间隔设置
3. 确认通知渠道配置正确

## 📈 性能优化

### 减少查询负载

1. 适当增加刷新间隔（30s → 60s）
2. 使用recording rules预计算复杂查询
3. 限制历史数据查询范围

### 优化复杂查询

对于高频使用的复杂查询，可以在Prometheus中配置recording rules：

```yaml
# prometheus_rules.yml
groups:
  - name: mcp_recording_rules
    rules:
      - record: mcp:tool_call_success_rate
        expr: (rate(mcp_tool_call_status_change_total{status="success"}[5m]) / rate(mcp_tool_call_status_change_total[5m])) * 100
      
      - record: mcp:avg_response_time
        expr: rate(mcp_tool_call_execution_duration_seconds_sum[5m]) / rate(mcp_tool_call_execution_duration_seconds_count[5m])
```

## 📱 移动端访问

Dashboard支持响应式布局，可以在移动设备上正常访问。关键指标会自动调整布局以适应小屏幕。

## 🔄 定期维护

1. **定期检查**：每周检查Dashboard是否正常显示
2. **告警调优**：根据实际情况调整告警阈值
3. **备份配置**：定期导出Dashboard JSON配置进行备份
4. **更新维护**：随着MCP服务更新，可能需要添加新的metrics或调整现有Panel
