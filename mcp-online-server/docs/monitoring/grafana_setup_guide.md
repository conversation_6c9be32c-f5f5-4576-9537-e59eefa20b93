# Grafana 配置步骤指南

## 前提条件

1. 已安装并运行Grafana服务
2. 已配置Prometheus数据源
3. MCP Online Server已部署并正常运行，metrics端点可访问

## 配置步骤

### 1. 配置Prometheus数据源

如果尚未配置Prometheus数据源，请先完成以下步骤：

1. 登录Grafana管理界面
2. 点击左侧菜单的 "Configuration" (⚙️) > "Data sources"
3. 点击 "Add data source"
4. 选择 "Prometheus"
5. 配置数据源：
   - Name: `Prometheus`
   - URL: 输入Prometheus服务地址，例如 `http://prometheus:9090`
   - Access: 选择 "Server" 或 "Browser" 根据您的网络配置
6. 点击 "Save & Test" 确保连接成功

### 2. 导入Dashboard

#### 方法1: 使用JSON文件导入

1. 登录Grafana管理界面
2. 点击左侧菜单的 "+" > "Import"
3. 点击 "Upload JSON file" 或直接粘贴JSON内容
4. 选择 `docs/grafana_dashboard.json` 文件
5. 选择Prometheus数据源
6. 点击 "Import"

#### 方法2: 手动创建Dashboard

1. 登录Grafana管理界面
2. 点击左侧菜单的 "+" > "Dashboard"
3. 点击 "Add new panel"
4. 按照 `docs/grafana_dashboard_config.md` 中的配置说明，逐个创建Panel
5. 保存Dashboard

### 3. 配置告警

为关键指标配置告警：

1. 在Dashboard中，点击要设置告警的Panel右上角的编辑图标
2. 切换到 "Alert" 选项卡
3. 点击 "Create Alert"
4. 配置告警条件，例如：
   - 长时间运行Session告警: `mcp_session_long_running_total > 0`
   - Tool Call成功率告警: `(rate(mcp_tool_call_status_change_total{status="success"}[5m]) / rate(mcp_tool_call_status_change_total[5m])) * 100 < 90`
5. 设置评估间隔，例如 "每60秒评估一次"
6. 设置告警阈值，例如 "条件持续5分钟"
7. 配置通知渠道（需提前在Alerting > Notification channels中设置）
8. 点击 "Save" 保存告警配置

### 4. 配置变量（可选）

如果您有多个MCP实例，可以配置变量以便在Dashboard中切换：

1. 在Dashboard设置中，点击 "Variables"
2. 点击 "Add variable"
3. 配置变量：
   - Name: `instance`
   - Type: `Query`
   - Data source: 选择Prometheus
   - Query: `label_values(mcp_session_pending_total, instance)`
   - 勾选 "Multi-value" 和 "Include All option"
4. 点击 "Update" 保存变量
5. 在各Panel的查询中使用变量，例如：`mcp_session_running_total{instance=~"$instance"}`

### 5. 优化Dashboard

1. **添加行分组**：
   - 点击Dashboard右上角的 "Add panel" > "Add row"
   - 为每组相关Panel添加一个Row，例如 "系统概览"、"Session监控" 等
   - 将Panel拖放到相应的Row中

2. **设置刷新间隔**：
   - 在Dashboard右上角的时间选择器旁，设置自动刷新间隔，建议设为30秒

3. **设置默认时间范围**：
   - 在Dashboard设置中，设置默认时间范围为 "Last 1 hour"
   - 添加常用时间范围选项：5m, 15m, 1h, 6h, 24h

### 6. 验证Dashboard

1. 确保所有Panel都能正确显示数据
2. 检查告警配置是否正常工作
3. 测试不同时间范围的数据显示
4. 如果配置了变量，测试切换不同实例的效果

## 常见问题排查

### 无数据显示

1. 检查Prometheus数据源连接是否正常
2. 验证MCP服务的metrics端点是否可访问：`curl http://your-mcp-server:port/metrics`
3. 确认metrics名称是否正确，可在Prometheus UI中测试查询
4. 检查时间范围设置，确保选择了有数据的时间段

### 告警不触发

1. 检查告警条件表达式是否正确
2. 验证告警评估间隔和持续时间设置
3. 确认通知渠道配置正确
4. 查看Grafana告警历史记录

### Dashboard性能问题

1. 减少高频刷新的Panel数量
2. 优化复杂查询，避免使用过多的聚合函数
3. 对于大型Dashboard，考虑拆分为多个专注于不同方面的Dashboard
4. 使用变量过滤不必要的数据

## 后续维护

1. **定期更新**：随着MCP服务的更新，可能需要添加新的metrics或调整现有Panel
2. **告警调优**：根据实际运行情况，调整告警阈值和条件
3. **用户反馈**：收集使用者反馈，优化Dashboard布局和内容
4. **备份配置**：定期导出Dashboard JSON配置，保存为版本控制
