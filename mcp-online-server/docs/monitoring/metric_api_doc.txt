Package gmetrics
import "icode.baidu.com/baidu/gdp/metrics/gmetrics"
Overview
Index
Examples
Subdirectories
Overview ▾
Package gmetrics 提供了对 GDP 框架默认的指标采集能力 按照此标准（ https://ku.baidu-int.com/d/5sBEBX8ndReiY5 )实现，若想了解指标含义请看此文档。 已和云上百度智能监控在标准上对齐，可以很轻松的在智能监控上自动采集并对指标进行展现。

如下是使用步骤：

A.给应用注册、添加指标采集功能：

1 修改 bootstrap/bootstrap.go 文件，在初始化其他组件之前，添加如下初始化代码：

func MustInit(ctx context.Context)
	gmetrics.Default().Init()      // 新增此行：初始化采集器；给 RAL、MySQL、Redis 等注册拦截器

	初始化其他组件的...
}
若需要修改默认的采集维度，应该在调用 Init() 之前完成。

2 注册路由中间件，以采集 HTTP 接口稳定性指标：

router := ghttp.NewRouter() // 原有的 router

// 注册采集 HTTP Server 接口指标的中间件,建议放在 panic recover 之后
// 采集到的指标名为 http_server_requests 和 server_working
// api 字段采集自注册的路由信息
// 在此之后注册的 Handler 的指标都可以采集到
router.Use(gmetrics.Default().CollectHTTPServer())   // 新增此行，应在注册 Handler 之前注册
中间件会采集 method、api(注册的路由)、err(用于标记请求成功失败，一般是0-成功，1-失败，还可以有其他值)、statusCode 这 4 个维度。

采集指标的 api 字段为注册的路由地址，如 "/metrics/*"、"/user/:id"。 若是 404，api 值为 "/-404-"。

3 将 Exporter 的接口注册到路由里去

建议给 Exporter 单独配置一个独立的端口，这样监控和业务是在不同的端口上。 可参考 《如何管理不可对外网暴露的接口》https://ku.baidu-int.com/d/HusJvUmYq11nAL

router := ghttp.NewRouter() // 原有的 router
// 注册其他中间件 ...

// 注册路径为 /metrics (应通过 BFE 等控制，不让外网可访问到)，包括两个子路径，用于给 prometheus 采集：
// /metrics/process  :  进程的指标信息
// /metrics/service  :  RPC Server 和 RPC Client 的指标信息
gmetrics.Default().RegisterExporterTo(router)      // 新增此行，上面的 3 行注释可以一块 copy 过去
4 采集 PBRPC Server 接口稳定性指标

修改路由注册代码添加指标采集的注册拦截器：
func router(ser *pbrpc.Server) {
  ig := pbrpc.NewGroupInterceptor(
    logInterceptor(),                                      // 之前已有：注册用于打印日志的拦截器
    gmetrics.Default().CollectPBRPCServer(),               // 新增此行：注册采集稳定性指标的拦截器
  )
  ec := &echo.Service{
    Intcptr: ig,    // 将拦截器组注册给 Service
  }
  ec.Register(ser)
}
B.在云上百度智能监控配置采集、展现视图、报警: http://chanpin.family.baidu.com/article/167204

C.一些细节/注意事项

1.RAL、MySQL、Redis 访问下游的指标统计，使用同相同的指标名，所以采集维度(labels)必须一样，否则会 panic：

a.指标-client_requests：请求计数和耗时
b.指标-client_working：当前执行中的请求数
2.client_request 的 api 字段如何赋值：

api 字段是下游接口的名称，部分协议框架可以读取到值，但是大部分会是默认值 "unknown"。

若使用 RAL 发送 pbrpc 请求，框架会自动的以 service+"_"+method 作为 api 的值。
若使用 RAL 发送 HTTP、NSHead 请求，api 的值默认会是 "unknown"。
若是 mysql，api 的值默认是 EventType, 如 "Query"、"Exec"。
若是 redis，api 的值是 cmd 名称，如 "GET"、"SET"，不支持自定义。
若应用需要统计更准确的值，可以按照如下方式修改代码予以支持：

a.RAL:给 RALRequest 的 APIName 字段设置值，如下所示：

req := &ghttp.RalRequest{
	// Path "/user/12345"，包含了变量 12345 这个用户 ID，是不可枚举的，不可以用作 APIName
	// 若采集到 1 亿个不同的 api 值，是毫无意义的
	APIName:"/user/save_by_id",  // 添加该行

	Method: "POST",
	Path:   "/user/12345",
	Header: hd,
	Query: url.Values{
		"from": []string{"demo"},
	},
}
b.MySQL：通过 ctx 将表名作为 api 字段

ctx=mysql.ContextWithTableName(ctx,"user_list")
mysqlClient.QueryContext(ctx,"select * from user_list where id=1")
3.Server 的 Handler 如何标记失败：

在 HTTP Server 的 Handler 里(PBRPC Server Handler 也一样)，可以使用 logit.SetErr 方法来标记请求是成功还是失败(pvlost)， 若认为是失败，应调用 SetErr(ctx,1)（调用后err=1），否则认为是成功（err=0）。 该 'err' 字段和日志中间件使用的同一个字段 。 当然，若没有使用 err 字段的习惯、也可以使用 statusCode 字段来表示成功和失败。

若没有给 Handler 设置 err 值，err 值的默认规则 statusCode<500 时 err=0,statusCode>=500 时 err=1 。 下面的例子为通过中间件来设置所有 statusCode >= 400 的请求 err=1:

router.Use(func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
	ret:=next.Next(ctx, w, req)
	// logit.GetErr(ctx) < 0 说明在 Handler 里没有设置 err 值
	if logit.GetErr(ctx) < 0 && w.WroteStatus() >= 400{
		logit.SetErr(ctx,1)
	}
	return ret
})
4.如何添加指标维度 若框架默认的指标维度不满足需求，是可以新增、修改指标维度的。 如下例子为给 HTTP server 的请求指标统计添加一个新的维度(label)：

_ = gmetrics.HTTPServerRequestsLabels.AddLabel(&gmetrics.HTTPServerLabelValue{
  Label: "hello", // 新增一个叫 hello 的维度(label)
  ValueFunc: func(ctx context.Context, w ghttp.Writer, r ghttp.Request) string {
      // 返回指标值
      return "world"
  },
})
HTTPServerRequestsLabels、HTTPServerWorkingLabels 等公开变量都是 LabelValues 类型的， 提供了 Find （按名称查找）、AddLabel (添加新 Label)、SetLabel (修改 Label) 等 API， 使用这些 API 可以很方便的对框架默认的指标采集行为做出调整，具体可以查看 ExampleLabelValues_SetLabel

5.添加自己采集的指标并输出：

有些业务指标也可以通过 prometheus SDK 采集指标数据。并且指标数据可以由 gmetrics 的 Registry 统一输出:

进程相关的指标可以注册到 gmetrics.Default().ProcessRegistry
业务指标可以注册到 gmetrics.Default().ServiceRegistry
具体可参考 Example (UserMetrics)，最终的指标数据会通过 exporter 接口 /metrics 统一输出。

6.其他注意事项：

1.添加完成后，触发相应流量后，才可以从 exporter 接口查看到指标的变化
2.指标维度的值，比如 api 字段，必须是可枚举的。若出现 api='/user/12345' 这种值是不可枚举的，不具备统计意义，
应修改为一个归一化的值，比如 api="/user/save_by_id"。
3.每个指标维度的值，目前限定最多个数为 500，若超过，值会归一化为 "over_limit"。如 api 字段，最多允许 500 个不同的值。
4.每个指标维度的值，目前限定最大长度为 128，若超过，会直接截断。如 api = "/user/12345" 的长度为 len("/user/12345")=11。
5.更多指标定义的标准和说明请看： https://ku.baidu-int.com/d/5sBEBX8ndReiY5
6.exporter 接口默认限制了不允许被并发访问，以避免数据较多时，并发访问 exporter 影响服务可用性。
被限制的请求会排队，若等待 100ms 还不能访问，会返回 429 错误
▹ Example (UserMetrics)

Index ▾
Constants
Variables
func HandleEventRequest(e mcp.ClientEvent) string
type CanInject
type GDPPoolStats
    func (gs *GDPPoolStats) CollectTo(vec WorkingVec)
type HTTPServerCollector
    func (ctr *HTTPServerCollector) MiddleWareFunc() ghttp.MiddleWareFunc
type HTTPServerLabelValue
    func (h *HTTPServerLabelValue) LabelName() string
    func (h *HTTPServerLabelValue) LabelValue(args ...any) string
type LabelValue
type LabelValues
    func (ls *LabelValues) AddLabel(labels ...LabelValue) error
    func (ls LabelValues) Find(name string) LabelValue
    func (ls LabelValues) Labels() []string
    func (ls LabelValues) SameLabels(lsb LabelValues) error
    func (ls *LabelValues) SetLabel(labels ...LabelValue)
    func (ls LabelValues) Values(args ...any) []string
type MCPServerCollector
    func (ctr *MCPServerCollector) Interceptor() *mcp.InterceptorServer
type MCPServerLabelValue
    func (m *MCPServerLabelValue) LabelName() string
    func (m *MCPServerLabelValue) LabelValue(args ...any) string
type McpClientCollector
    func (ctr *McpClientCollector) Inject()
    func (ctr *McpClientCollector) Interceptor() *mcp.Interceptor
type McpClientLabelValue
    func (m *McpClientLabelValue) LabelName() string
    func (m *McpClientLabelValue) LabelValue(args ...any) string
type McpClientPoolStats
    func (mc *McpClientPoolStats) CollectTo(vec WorkingVec)
type Metrics
    func Default() *Metrics
    func (m *Metrics) CollectHTTPServer() ghttp.MiddleWareFunc
    func (m *Metrics) CollectMCPServer() *mcp.InterceptorServer
    func (m *Metrics) CollectPBRPCServer() pbrpc.Interceptor
    func (m *Metrics) HTTPServerRequestsVec() RequestsVec
    func (m *Metrics) Init()
    func (m *Metrics) RALRequestsVec() RequestsVec
    func (m *Metrics) RALWorkingVec() WorkingVec
    func (m *Metrics) RegisterExporterTo(r ghttp.Router)
    func (m *Metrics) RegisterExporterToHTTPServer(s *http.Server)
    func (m *Metrics) RegisterExporterToHTTPServerWithPath(s *http.Server, p string)
    func (m *Metrics) RegisterExporterWithPath(r ghttp.Router, p string)
    func (m *Metrics) ServeHTTP(w http.ResponseWriter, r *http.Request)
    func (m *Metrics) ServerWorkingVec() WorkingVec
type MySQLCollector
    func (ctr *MySQLCollector) Inject()
    func (ctr *MySQLCollector) Interceptor() *mysql.Interceptor
type MySQLLabelValue
    func (m *MySQLLabelValue) LabelName() string
    func (m *MySQLLabelValue) LabelValue(args ...any) string
type MySQLPoolStats
    func (ms *MySQLPoolStats) CollectTo(vec WorkingVec)
type PBRPCServerCollector
    func (ctr *PBRPCServerCollector) Interceptor() pbrpc.Interceptor
type PBRPCServerLabelValue
    func (p *PBRPCServerLabelValue) LabelName() string
    func (p *PBRPCServerLabelValue) LabelValue(args ...any) string
type PanicCollector
    func (p *PanicCollector) Inject()
type RALLabelValue
    func (r *RALLabelValue) LabelName() string
    func (r *RALLabelValue) LabelValue(args ...any) string
type RalCollector
    func (ctr *RalCollector) Inject()
    func (ctr *RalCollector) Interceptor() *ral.Interceptor
type RedisCollector
    func (ctr *RedisCollector) Inject()
    func (ctr *RedisCollector) Interceptor() *redis.Interceptor
type RedisLabelValue
    func (r *RedisLabelValue) LabelName() string
    func (r *RedisLabelValue) LabelValue(args ...any) string
type RedisPoolStats
    func (rs *RedisPoolStats) CollectTo(vec WorkingVec)
type Registry
type RequestsVec
    func ClientRequestsVec(labels []string) RequestsVec
    func HTTPServerRequestsVec(labels []string) RequestsVec
    func LimitRequestsVec(vec RequestsVec, limit int) RequestsVec
    func MCPServerRequestsVec(labels []string) RequestsVec
    func PBRPCServerRequestsVec(labels []string) RequestsVec
type WorkingVec
    func ClientWorkingVec(labels []string) WorkingVec
    func LimitWorkingVec(vec WorkingVec, limit int) WorkingVec
    func ServerWorkingVec(labels []string) WorkingVec
type WorkingVecGather
    func (st *WorkingVecGather) Collect(metrics chan<- prometheus.Metric)
    func (st *WorkingVecGather) Describe(descs chan<- *prometheus.Desc)
    func (st *WorkingVecGather) RegisterGatherFunc(fn func(vec WorkingVec))
Examples(Expand All)
LabelValues.AddLabel
LabelValues.SetLabel
Package (UserMetrics)
Package files
context.go doc.go httpserver.go info.go label.go mcpclient.go mcpserver.go metrics.go mysql.go panic.go pbrpcserver.go poolstats.go ral.go redis.go vec.go

Constants
const DefaultMetricsPath = "/metrics"
Variables
HTTPServerRequestsLabels 用于统计 HTTP Server 处理请求量和耗时的字段

var HTTPServerRequestsLabels = LabelValues{
    &HTTPServerLabelValue{
        Label: "method",
        ValueFunc: func(ctx context.Context, w ghttp.Writer, r ghttp.Request) string {

            return internal.HTTPMethod(r.HTTPRequest().Method)
        },
    },
    &HTTPServerLabelValue{
        Label: "api",
        ValueFunc: func(ctx context.Context, w ghttp.Writer, r ghttp.Request) string {
            info := ghttp.RouterInfoFromContext(ctx)
            if len(info.Path) == 0 {
                return http404Path
            }
            return info.Path
        },
    },
    &HTTPServerLabelValue{
        Label: "statusCode",
        ValueFunc: func(ctx context.Context, w ghttp.Writer, r ghttp.Request) string {

            if code := w.WroteStatus(); code > 0 {
                return strconv.Itoa(code)
            }

            if f := logit.FindField(ctx, ghttp.LogFieldLogStatus); f != nil {
                codeStr := fmt.Sprint(f.Value())
                if code, _ := strconv.Atoi(codeStr); code > 0 {
                    return codeStr
                }
            }

            if ctx.Err() != nil {
                return "499"
            }
            return "200"
        },
    },
    &HTTPServerLabelValue{
        Label: "err",
        ValueFunc: func(ctx context.Context, w ghttp.Writer, r ghttp.Request) string {
            errCode := logit.GetErr(ctx)

            if errCode >= 0 {
                return strconv.Itoa(errCode)
            }
            s := w.WroteStatus()

            if s == 0 || s >= 500 {
                return "1"
            }
            return "0"
        },
    },
}
HTTPServerWorkingLabels 用于统计正在处理的的请求量（并发处理量）

var HTTPServerWorkingLabels = LabelValues{
    &HTTPServerLabelValue{
        Label: "server",
        ValueFunc: func(ctx context.Context, w ghttp.Writer, r ghttp.Request) string {
            return "http_server"
        },
    },
    &HTTPServerLabelValue{
        Label: "api",
        ValueFunc: func(ctx context.Context, w ghttp.Writer, r ghttp.Request) string {
            info := ghttp.RouterInfoFromContext(ctx)
            if len(info.Path) == 0 {
                return http404Path
            }
            return info.Path
        },
    },
}
PBRPCServerRequestsLabels 用于统计 PBRPC Server 处理请求量和耗时的字段

var MCPServerRequestsLabels = LabelValues{
    &MCPServerLabelValue{
        Label: "method",
        ValueFunc: func(ctx context.Context, e mcp.ServerEvent) string {
            return e.GetRequestMethod()
        },
    },

    &MCPServerLabelValue{
        Label: "api",
        ValueFunc: func(ctx context.Context, e mcp.ServerEvent) string {
            return e.GetRequestMethod()
        },
    },

    &MCPServerLabelValue{
        Label: "errCode",
        ValueFunc: func(ctx context.Context, e mcp.ServerEvent) string {
            errCode := e.GetRequestErr().ErrCode()
            errCodeStr := strconv.FormatInt(errCode, 10)
            if errCode > 0 {
                return errCodeStr
            }

            if ctx.Err() != nil {
                return "499"
            }
            if errCode == 0 {
                return "200"
            }
            return "200"
        },
    },
    &MCPServerLabelValue{
        Label: "err",
        ValueFunc: func(ctx context.Context, e mcp.ServerEvent) string {
            errCode := logit.GetErr(ctx)

            if errCode >= 0 {
                return strconv.Itoa(errCode)
            }
            err := e.GetRequestErr().ErrCode()
            if err != 0 {
                return "1"
            }
            return "0"
        },
    },
}
PBRPCServerWorkingLabels 用于统计正在处理的的请求量（并发处理量）

var MCPServerWorkingLabels = LabelValues{
    &MCPServerLabelValue{
        Label: "server",
        ValueFunc: func(ctx context.Context, e mcp.ServerEvent) string {
            return "mcp_server"
        },
    },
    &MCPServerLabelValue{
        Label: "api",
        ValueFunc: func(ctx context.Context, e mcp.ServerEvent) string {
            return e.GetRequestMethod()
        },
    },
}
McpClientRequestLabels 用于统计调用 mcp client 下游的请求量和计时的字段标签

var McpClientRequestLabels = LabelValues{
    &McpClientLabelValue{
        Label: "servicer",
        ProcessValueFunc: func(ctx context.Context, c *mcp.Client, e mcp.ClientEvent) string {
            return c.Name()
        },
    },
    &McpClientLabelValue{
        Label: "api",

        ProcessValueFunc: func(ctx context.Context, c *mcp.Client, e mcp.ClientEvent) string {
            return HandleEventRequest(e)
        },
    },
    &McpClientLabelValue{
        Label: "errCode",
        ProcessValueFunc: func(ctx context.Context, c *mcp.Client, e mcp.ClientEvent) string {
            return c.GetErrCode(e.Err)
        },
    },
    &McpClientLabelValue{
        Label: "try",
        ProcessValueFunc: func(ctx context.Context, c *mcp.Client, e mcp.ClientEvent) string {
            if e.Retry.TryMax > 0 {
                return strconv.Itoa(e.Retry.TryIndex)
            }
            return "99"
        },
    },
    &McpClientLabelValue{
        Label: "err",
        ProcessValueFunc: func(ctx context.Context, c *mcp.Client, e mcp.ClientEvent) string {
            if e.Err == nil {
                return "0"
            }
            return "1"
        },
    },
}
McpClientWorkingLabels 用于统计正在调用 mcp client 的请求量（并发处理量）

var McpClientWorkingLabels = LabelValues{
    &McpClientLabelValue{
        Label: "servicer",
        ProcessValueFunc: func(ctx context.Context, c *mcp.Client, e mcp.ClientEvent) string {
            return c.Name()
        },
    },
}
MySQLRequestLabels 用于统计调用 mysql 下游的请求量和计时的字段标签

var MySQLRequestLabels = LabelValues{
    &MySQLLabelValue{
        Label: "servicer",
        ValueFunc: func(ctx context.Context, c mysql.Client, e mysql.Event) string {
            return c.Name()
        },
    },
    &MySQLLabelValue{
        Label: "api",
        ValueFunc: func(ctx context.Context, c mysql.Client, e mysql.Event) string {
            apiName := mysql.TableNameFromContext(ctx)
            if len(apiName) > 0 {
                return apiName
            }
            return e.Type.String()
        },
    },
    &MySQLLabelValue{
        Label: "errCode",
        ValueFunc: func(ctx context.Context, c mysql.Client, e mysql.Event) string {
            if e.Err == nil {
                return "0"
            }
            return strconv.Itoa(int(mysql.ErrorCode(e.Err)))
        },
    },
    &MySQLLabelValue{
        Label: "try",
        ValueFunc: func(ctx context.Context, c mysql.Client, e mysql.Event) string {

            return "1"
        },
    },
    &MySQLLabelValue{
        Label: "err",
        ValueFunc: func(ctx context.Context, c mysql.Client, e mysql.Event) string {
            if e.Err == nil {
                return "0"
            }
            return "1"
        },
    },
}
MySQLWorkingLabels 用于统计正在调用 mysql 的请求量（并发处理量）

var MySQLWorkingLabels = LabelValues{
    &MySQLLabelValue{
        Label: "servicer",
        ValueFunc: func(ctx context.Context, c mysql.Client, e mysql.Event) string {
            return c.Name()
        },
    },
}
PBRPCServerRequestsLabels 用于统计 PBRPC Server 处理请求量和耗时的字段

var PBRPCServerRequestsLabels = LabelValues{
    &PBRPCServerLabelValue{
        Label: "service",
        ValueFunc: func(ctx context.Context, event string, meta *packing.Meta,
            req pbrpc.Request, resp pbrpc.Response, err error) string {
            return meta.Request.GetServiceName()
        },
    },
    &PBRPCServerLabelValue{
        Label: "method",
        ValueFunc: func(ctx context.Context, event string, meta *packing.Meta,
            req pbrpc.Request, resp pbrpc.Response, err error) string {
            return meta.Request.GetMethodName()
        },
    },
    &PBRPCServerLabelValue{
        Label: "event",
        ValueFunc: func(ctx context.Context, event string, meta *packing.Meta,
            req pbrpc.Request, resp pbrpc.Response, err error) string {
            return event
        },
    },
    &PBRPCServerLabelValue{
        Label: "errCode",
        ValueFunc: func(ctx context.Context, event string, meta *packing.Meta,
            req pbrpc.Request, resp pbrpc.Response, err error) string {
            if resp != nil {
                return strconv.Itoa(int(resp.ErrorCode()))
            }
            if err != nil {
                var ce gerror.CodeError
                if errors.As(err, &ce) {
                    return strconv.Itoa(int(ce.ErrCode()))
                }

                var ce1 pberror.CodeError
                if errors.As(err, &ce1) {
                    return strconv.Itoa(int(ce1.ErrCode()))
                }
                return "255"
            }
            return "0"
        },
    },
    &PBRPCServerLabelValue{
        Label: "err",
        ValueFunc: func(ctx context.Context, event string, meta *packing.Meta,
            req pbrpc.Request, resp pbrpc.Response, err error) string {
            code := logit.GetErr(ctx)
            if code >= 0 {
                return strconv.Itoa(code)
            }

            if err != nil {
                return "1"
            }
            return "0"
        },
    },
}
PBRPCServerWorkingLabels 用于统计正在处理的的请求量（并发处理量）

var PBRPCServerWorkingLabels = LabelValues{
    &PBRPCServerLabelValue{
        Label: "server",
        ValueFunc: func(ctx context.Context, event string, meta *packing.Meta,
            req pbrpc.Request, resp pbrpc.Response, err error) string {
            return "pbrpc_server"
        },
    },
    &PBRPCServerLabelValue{
        Label: "api",
        ValueFunc: func(ctx context.Context, event string, meta *packing.Meta,
            req pbrpc.Request, resp pbrpc.Response, err error) string {
            return meta.Request.GetServiceName() + "/" + meta.Request.GetMethodName()
        },
    },
}
RALRequestLabels 用于统计调用 RAL 下游的请求量和计时的字段标签

var RALRequestLabels = LabelValues{
    &RALLabelValue{
        Label: "servicer",
        ValueFunc: func(ctx context.Context, name any, req ral.Request, resp ral.Response, err error) string {
            return internal.ServicerName(name)
        },
    },
    &RALLabelValue{
        Label: "api",
        ValueFunc: func(ctx context.Context, name any, req ral.Request, resp ral.Response, err error) string {
            api := ral.RequestHelper.APIName(req)
            if len(api) == 0 {
                return "unknown"
            }
            return api
        },
    },
    &RALLabelValue{
        Label: "errCode",
        ValueFunc: func(ctx context.Context, name any, req ral.Request, resp ral.Response, err error) string {
            code := ral.ResponseHelper.ErrCode(resp, err)
            return strconv.FormatInt(int64(code), 10)
        },
    },
    &RALLabelValue{
        Label: "try",
        ValueFunc: func(ctx context.Context, name any, req ral.Request, resp ral.Response, err error) string {
            tt := ctx.Value(ctxKeyRALTryInfo).(*ralTryInfo)
            try := tt.Times()
            return strconv.Itoa(int(try))
        },
    },
    &RALLabelValue{
        Label: "err",
        ValueFunc: func(ctx context.Context, name any, req ral.Request, resp ral.Response, err error) string {
            if err == nil {
                return "0"
            }
            return "1"
        },
    },
}
RALWorkingLabels 用于统计正在调用 RAL 的请求量（并发处理量）

var RALWorkingLabels = LabelValues{
    &RALLabelValue{
        Label: "servicer",
        ValueFunc: func(ctx context.Context, name any, req ral.Request, resp ral.Response, err error) string {
            return internal.ServicerName(name)
        },
    },
}
RedisRequestLabels 用于统计调用 Redis 下游的请求量和计时的字段标签

var RedisRequestLabels = LabelValues{
    &RedisLabelValue{
        Label: "servicer",
        ProcessValueFunc: func(_ context.Context, c redis.Client, _ redis.Cmder) string {
            return c.Name()
        },
        PipelineValueFunc: func(_ context.Context, c redis.Client, _ []redis.Cmder) string {
            return c.Name()
        },
    },
    &RedisLabelValue{
        Label: "api",
        ProcessValueFunc: func(_ context.Context, _ redis.Client, cmd redis.Cmder) string {
            return redis.CmdName(cmd.Name())
        },
        PipelineValueFunc: func(_ context.Context, _ redis.Client, _ []redis.Cmder) string {
            return "pipeline"
        },
    },
    &RedisLabelValue{
        Label: "errCode",
        ProcessValueFunc: func(_ context.Context, _ redis.Client, cmd redis.Cmder) string {
            err := cmd.Err()
            if isRedisSuccess(err) {
                return "0"
            }
            return strconv.Itoa(int(redis.ErrorCode(err)))
        },
        PipelineValueFunc: func(_ context.Context, _ redis.Client, cmds []redis.Cmder) string {
            for i := 0; i < len(cmds); i++ {
                if err := cmds[i].Err(); !isRedisSuccess(err) {
                    return strconv.Itoa(int(redis.ErrorCode(err)))
                }
            }
            return "0"
        },
    },
    &RedisLabelValue{
        Label: "try",
        ProcessValueFunc: func(ctx context.Context, _ redis.Client, _ redis.Cmder) string {
            ti := ral.TryInfoFromContext(ctx)
            if ti.TryMax > 0 {
                return strconv.Itoa(ti.TryIndex)
            }
            return "99"
        },
        PipelineValueFunc: func(ctx context.Context, _ redis.Client, _ []redis.Cmder) string {
            ti := ral.TryInfoFromContext(ctx)
            if ti.TryMax > 0 {
                return strconv.Itoa(ti.TryIndex)
            }
            return "99"
        },
    },
    &RedisLabelValue{
        Label: "err",
        ProcessValueFunc: func(_ context.Context, _ redis.Client, cmd redis.Cmder) string {
            if isRedisSuccess(cmd.Err()) {
                return "0"
            }
            return "1"
        },
        PipelineValueFunc: func(_ context.Context, _ redis.Client, cmds []redis.Cmder) string {
            for i := 0; i < len(cmds); i++ {
                if err := cmds[i].Err(); !isRedisSuccess(err) {
                    return "1"
                }
            }
            return "0"
        },
    },
}
RedisWorkingLabels 用于统计正在调用 Redis 的请求量（并发处理量）

var RedisWorkingLabels = LabelValues{
    &RedisLabelValue{
        Label: "servicer",
        ProcessValueFunc: func(_ context.Context, c redis.Client, _ redis.Cmder) string {
            return c.Name()
        },
        PipelineValueFunc: func(_ context.Context, c redis.Client, _ []redis.Cmder) string {
            return c.Name()
        },
    },
}
func HandleEventRequest
func HandleEventRequest(e mcp.ClientEvent) string
type CanInject
CanInject 支持自动将功能注入到框架

type CanInject interface {
    Inject()
}
type GDPPoolStats
GDPPoolStats 采集 GDP 框架里下游服务的连接池状态信息（MySQL、Redis 除外）

Datahub Client 目前也是专属 Client，也不包括在内（目前会识别为短链接）

type GDPPoolStats struct {
}
func (*GDPPoolStats) CollectTo
func (gs *GDPPoolStats) CollectTo(vec WorkingVec)
CollectTo 采集 GDP 连接池状态信息

MySQL、Redis 的连接池状态是独立的，不包括在内
此处采集的是在配置中，使用 [ConnPool] 所配置的连接池
详见 http://gdp.baidu-int.com/gdp2/docs/examples/client/30_servicer/#23-connpool连接池配置
type HTTPServerCollector
HTTPServerCollector 用于采集 HTTP Server 的指标信息

type HTTPServerCollector struct {
    Working WorkingVec

    Requests RequestsVec

    WorkingLabels LabelValues

    RequestsLabels LabelValues
    // contains filtered or unexported fields
}
func (*HTTPServerCollector) MiddleWareFunc
func (ctr *HTTPServerCollector) MiddleWareFunc() ghttp.MiddleWareFunc
MiddleWareFunc 获取采集指标的中间件,

在该中间件里，会使用 logit.WithContext(ctx)来初始化 ctx，
以让该中间件能读取到业务 handler 往 ctx 里写日志字段
type HTTPServerLabelValue
HTTPServerLabelValue 统计 HTTP Server 指标时的 label

type HTTPServerLabelValue struct {
    ValueFunc func(ctx context.Context, w ghttp.Writer, r ghttp.Request) string
    Label     string
}
func (*HTTPServerLabelValue) LabelName
func (h *HTTPServerLabelValue) LabelName() string
LabelName 标签名称

func (*HTTPServerLabelValue) LabelValue
func (h *HTTPServerLabelValue) LabelValue(args ...any) string
LabelValue 获取标签的值

type LabelValue
LabelValue label 和 value 的接口定义

type LabelValue interface {
    LabelName() string
    LabelValue(args ...any) string
}
type LabelValues
LabelValues LabelValue slice

type LabelValues []LabelValue
func (*LabelValues) AddLabel
func (ls *LabelValues) AddLabel(labels ...LabelValue) error
AddLabel 添加新的标签,若标签重复会添加失败

▹ Example

func (LabelValues) Find
func (ls LabelValues) Find(name string) LabelValue
Find 查找字段，若找不到会返回 nil

func (LabelValues) Labels
func (ls LabelValues) Labels() []string
Labels 返回所有的 label 数组

func (LabelValues) SameLabels
func (ls LabelValues) SameLabels(lsb LabelValues) error
SameLabels 检查标签是否一样，若不一样将返回错误，一样时返回 nil

func (*LabelValues) SetLabel
func (ls *LabelValues) SetLabel(labels ...LabelValue)
SetLabel 设置，若重复会替换 暂时不提供删除的功能

▹ Example

func (LabelValues) Values
func (ls LabelValues) Values(args ...any) []string
Values 获取值

type MCPServerCollector
MCPServerCollector 用于采集 MCP Server 的指标信息

type MCPServerCollector struct {
    Working WorkingVec

    Requests RequestsVec

    It *mcp.InterceptorServer

    WorkingLabels LabelValues

    RequestsLabels LabelValues
}
func (*MCPServerCollector) Interceptor
func (ctr *MCPServerCollector) Interceptor() *mcp.InterceptorServer
Interceptor 获取采集指标的拦截器

type MCPServerLabelValue
MCPServerLabelValue 统计 mcp Server 指标时的 label

type MCPServerLabelValue struct {
    ValueFunc func(ctx context.Context, e mcp.ServerEvent) string
    Label     string
}
func (*MCPServerLabelValue) LabelName
func (m *MCPServerLabelValue) LabelName() string
LabelName 标签名称

func (*MCPServerLabelValue) LabelValue
func (m *MCPServerLabelValue) LabelValue(args ...any) string
LabelValue 获取标签的值

type McpClientCollector
McpClientCollector mcp client 指标采集器

type McpClientCollector struct {
    Working WorkingVec

    Requests RequestsVec

    WorkingLabels LabelValues

    RequestsLabels LabelValues
    // contains filtered or unexported fields
}
func (*McpClientCollector) Inject
func (ctr *McpClientCollector) Inject()
Inject 给框架注入全局拦截器

func (*McpClientCollector) Interceptor
func (ctr *McpClientCollector) Interceptor() *mcp.Interceptor
Interceptor 获取拦截器

type McpClientLabelValue
McpClientLabelValue 统计 mcp client 指标时的 label

type McpClientLabelValue struct {
    ProcessValueFunc func(ctx context.Context, c *mcp.Client, e mcp.ClientEvent) string
    Label            string
}
func (*McpClientLabelValue) LabelName
func (m *McpClientLabelValue) LabelName() string
LabelName 标签名称

func (*McpClientLabelValue) LabelValue
func (m *McpClientLabelValue) LabelValue(args ...any) string
LabelValue 获取标签的值

type McpClientPoolStats
McpClientPoolStats 采集 macp client 的连接池状态信息

type McpClientPoolStats struct {
}
func (*McpClientPoolStats) CollectTo
func (mc *McpClientPoolStats) CollectTo(vec WorkingVec)
CollectTo 采集 MySQL 连接池状态信息

type Metrics
Metrics 框架采集指标的汇总

为了让使用更简单快捷，将 进程、RPC Client、RPC Server 所有的指标采集的功能都汇聚这里
采集标准详见：https://ku.baidu-int.com/d/5sBEBX8ndReiY5
支持对指标维度进行扩展（补充、替换、删除等）
若是添加指标维度，直接调整全局变量 RALRequestLabels 等即可
需要注意，在自定义过程中，所有 rpc client 的指标维度需要保持一致，否则将 panic
type Metrics struct {
    // ProcessRegistry 可选，用于存储进程信息
    ProcessRegistry Registry

    // ServiceRegistry 可选，RPC Client 和 RPC Server 的信息
    // 若业务有自定义指标，可以注册到这个上面
    ServiceRegistry Registry

    // LabelValuesLimit 可选，单个 label 的 values 值的允许的个数
    // 若值 >0 限制生效
    LabelValuesLimit int

    // EnableConnPool 是否采集连接池的状态信息
    EnableConnPool bool

    // ExporterAllowConcView 是否允许 exporter 的 HTTP 接口并发访问，默认 false-不允许
    ExporterAllowConcView bool
    // contains filtered or unexported fields
}
DefaultMetrics 默认的采集器，请通过 Default() 去初始化和调用

var DefaultMetrics *Metrics
func Default
func Default() *Metrics
Default 框架提供的默认的采集器 若需要对指标进行调整，应在调用此方法之前完成，默认会设置单个 label 最多允许出现 500 个值，若超过，label 的值会被替换为"over_limit"

func (*Metrics) CollectHTTPServer
func (m *Metrics) CollectHTTPServer() ghttp.MiddleWareFunc
CollectHTTPServer 提供给 HTTP Server 路由使用的，需要自行将此注册到 ghttp.Router 中去

可以作为 router 的第一个中间件注册进去，recover 中间件注册在之后
func (*Metrics) CollectMCPServer
func (m *Metrics) CollectMCPServer() *mcp.InterceptorServer
CollectMCPServer 提供给 mcp server 使用的拦截器，需自行注册到 mcp server

func (*Metrics) CollectPBRPCServer
func (m *Metrics) CollectPBRPCServer() pbrpc.Interceptor
CollectPBRPCServer 提供给 pbrpc server 使用的拦截器，需自行注册到 pbrpc server

func (*Metrics) HTTPServerRequestsVec
func (m *Metrics) HTTPServerRequestsVec() RequestsVec
HTTPServerRequestsVec 返回可用于统计 HTTP Server 请求耗时和计算的 Vec

用于自定义 HTTP Server 的指标统计
func (*Metrics) Init
func (m *Metrics) Init()
Init 初始化，在初始化其他组件之前调用

func (*Metrics) RALRequestsVec
func (m *Metrics) RALRequestsVec() RequestsVec
RALRequestsVec 返回可用于统计 RPC Client 请求耗时和计数的 Vec

用于自定义 RPC Client 的指标统计
func (*Metrics) RALWorkingVec
func (m *Metrics) RALWorkingVec() WorkingVec
RALWorkingVec 返回可用于统计 RPC Client 当前工作量(处理中的任务数)的 Vec

用于自定义 RPC Client 的指标统计
func (*Metrics) RegisterExporterTo
func (m *Metrics) RegisterExporterTo(r ghttp.Router)
RegisterExporterTo 注册 Exporter 到路由中去，使用默认路径为 /metrics

包含：

/metrics          :  所有的信息
/metrics/process  :  进程的指标信息
/metrics/service  :  RPC Server 和 RPC Client 的信息
注意，建议此接口注册到独立的只能在内网访问的 HTTP Server 上，避免外网可访问到，产生安全问题 可参考 《如何管理不可对外网暴露的接口》https://ku.baidu-int.com/d/HusJvUmYq11nAL

若期望通过给此接口添加限定内网访问的中间件,来限定非外网不让访问（更推荐使用 BF限定，不推荐此方案），可以这样：

r1:=ghttp.NewRouterWrapper(r,ghttp.NewInternalIPMiddleWareFunc()) // 派生一个包含 InternalIP 的新的 Router
RegisterExporterTo(r1)
func (*Metrics) RegisterExporterToHTTPServer
func (m *Metrics) RegisterExporterToHTTPServer(s *http.Server)
RegisterExporterToHTTPServer 注册 Exporter 到httpserver中去，使用默认路径为 /metrics

func (*Metrics) RegisterExporterToHTTPServerWithPath
func (m *Metrics) RegisterExporterToHTTPServerWithPath(s *http.Server, p string)
RegisterExporterToHTTPServerWithPath 注册 Exporter 到路由中去,路径有参数 p 指定

p: 注册的路径，如 /metrics
func (*Metrics) RegisterExporterWithPath
func (m *Metrics) RegisterExporterWithPath(r ghttp.Router, p string)
RegisterExporterWithPath 注册 Exporter 到路由中去,路径有参数 p 指定

p: 注册的路径，如 /metrics
func (*Metrics) ServeHTTP
func (m *Metrics) ServeHTTP(w http.ResponseWriter, r *http.Request)
ServeHTTP 实现 HTTP 接口,输出采集到的信息

/process  :  进程的指标信息
/service  :  RPC Server 和 RPC Client 的信息
/            :  所有的信息
若是不用 RegisterExporterTo 注册，而是自行注册，请按照 RegisterExporterTo
的代码实现，使用 http.StripPrefix 方法对路径进行修正，否则会出现 404 错误
func (*Metrics) ServerWorkingVec
func (m *Metrics) ServerWorkingVec() WorkingVec
ServerWorkingVec 返回可用于统计 RPC Server 当前工作量(处理中的任务数)的 Vec

用于自定义 RPC Server 的指标统计
type MySQLCollector
MySQLCollector MySQL 指标采集器

type MySQLCollector struct {
    Working WorkingVec

    Requests RequestsVec

    WorkingLabels LabelValues

    RequestsLabels LabelValues
    // contains filtered or unexported fields
}
func (*MySQLCollector) Inject
func (ctr *MySQLCollector) Inject()
Inject 给框架注入全局拦截器

func (*MySQLCollector) Interceptor
func (ctr *MySQLCollector) Interceptor() *mysql.Interceptor
Interceptor 获取拦截器

type MySQLLabelValue
MySQLLabelValue 统计 MySQL 指标时的 label

type MySQLLabelValue struct {
    ValueFunc func(ctx context.Context, c mysql.Client, e mysql.Event) string
    Label     string
}
func (*MySQLLabelValue) LabelName
func (m *MySQLLabelValue) LabelName() string
LabelName 标签名称

func (*MySQLLabelValue) LabelValue
func (m *MySQLLabelValue) LabelValue(args ...any) string
LabelValue 获取标签的值

type MySQLPoolStats
MySQLPoolStats 采集 MySQL Client 的连接池状态信息

type MySQLPoolStats struct {
}
func (*MySQLPoolStats) CollectTo
func (ms *MySQLPoolStats) CollectTo(vec WorkingVec)
CollectTo 采集 MySQL 连接池状态信息

type PBRPCServerCollector
PBRPCServerCollector 用于采集 PBRPC Server 的指标信息

由于 pbrpc server 支持了4 种通讯模式，所以统计的方式和 HTTP Server 有所区别
当前实现为不区分通讯模式，在收到 request之后、发送 response 之后、handler 处理完成之后这 3 个阶段进行统计
在指标值中阶段名称字段为 event，如 event=request
type PBRPCServerCollector struct {
    Working WorkingVec

    Requests RequestsVec

    WorkingLabels LabelValues

    RequestsLabels LabelValues
    // contains filtered or unexported fields
}
func (*PBRPCServerCollector) Interceptor
func (ctr *PBRPCServerCollector) Interceptor() pbrpc.Interceptor
Interceptor 获取采集指标的拦截器

type PBRPCServerLabelValue
PBRPCServerLabelValue 统计 pbrpc Server 指标时的 label

type PBRPCServerLabelValue struct {
    ValueFunc func(ctx context.Context, event string, meta *packing.Meta,
        req pbrpc.Request, resp pbrpc.Response, err error) string
    Label string
}
func (*PBRPCServerLabelValue) LabelName
func (p *PBRPCServerLabelValue) LabelName() string
LabelName 标签名称

func (*PBRPCServerLabelValue) LabelValue
func (p *PBRPCServerLabelValue) LabelValue(args ...any) string
LabelValue 获取标签的值

type PanicCollector
PanicCollector 统计 panic 的

type PanicCollector struct {
    PanicCounter prometheus.Counter
}
func (*PanicCollector) Inject
func (p *PanicCollector) Inject()
Inject 注入统计能力

type RALLabelValue
RALLabelValue 统计 RAL 指标时的 label

type RALLabelValue struct {
    ValueFunc func(ctx context.Context, name any, req ral.Request, resp ral.Response, err error) string
    Label     string
}
func (*RALLabelValue) LabelName
func (r *RALLabelValue) LabelName() string
LabelName 标签名称

func (*RALLabelValue) LabelValue
func (r *RALLabelValue) LabelValue(args ...any) string
LabelValue 获取标签的值

type RalCollector
RalCollector RAL 指标采集器

type RalCollector struct {
    Working WorkingVec

    Requests RequestsVec

    WorkingLabels LabelValues

    RequestsLabels LabelValues
    // contains filtered or unexported fields
}
func (*RalCollector) Inject
func (ctr *RalCollector) Inject()
Inject 给框架注入全局拦截器

func (*RalCollector) Interceptor
func (ctr *RalCollector) Interceptor() *ral.Interceptor
Interceptor 获取拦截器

type RedisCollector
RedisCollector Redis 指标采集器

type RedisCollector struct {
    Working WorkingVec

    Requests RequestsVec

    WorkingLabels LabelValues

    RequestsLabels LabelValues
    // contains filtered or unexported fields
}
func (*RedisCollector) Inject
func (ctr *RedisCollector) Inject()
Inject 给框架注入全局拦截器

func (*RedisCollector) Interceptor
func (ctr *RedisCollector) Interceptor() *redis.Interceptor
Interceptor 获取拦截器

type RedisLabelValue
RedisLabelValue 统计 Redis 指标时的 label

type RedisLabelValue struct {
    ProcessValueFunc  func(ctx context.Context, c redis.Client, cmd redis.Cmder) string
    PipelineValueFunc func(ctx context.Context, c redis.Client, cmds []redis.Cmder) string
    Label             string
}
func (*RedisLabelValue) LabelName
func (r *RedisLabelValue) LabelName() string
LabelName 标签名称

func (*RedisLabelValue) LabelValue
func (r *RedisLabelValue) LabelValue(args ...any) string
LabelValue 获取标签的值

type RedisPoolStats
RedisPoolStats 采集 Redis Client 的连接池状态信息

type RedisPoolStats struct {
}
func (*RedisPoolStats) CollectTo
func (rs *RedisPoolStats) CollectTo(vec WorkingVec)
CollectTo 采集连接池状态信息

type Registry
Registry Gatherer and Registerer

type Registry interface {
    prometheus.Registerer
    prometheus.Gatherer
}
type RequestsVec
RequestsVec 用于统计当前请求计数和耗时的计数器

type RequestsVec interface {
    prometheus.Collector
    WithLabelValues(lvs ...string) prometheus.Observer
}
func ClientRequestsVec
func ClientRequestsVec(labels []string) RequestsVec
ClientRequestsVec 生成采集 RPC Client 请求次数和耗时的 HistogramVec

名称为：client_requests
func HTTPServerRequestsVec
func HTTPServerRequestsVec(labels []string) RequestsVec
HTTPServerRequestsVec 生成采集 HTTP Server 请求次数和耗时的 HistogramVec

名称为：http_server_requests
func LimitRequestsVec
func LimitRequestsVec(vec RequestsVec, limit int) RequestsVec
LimitRequestsVec 限制 labelValue 的个数，避免使用错误，值太多导致内存无限使用 若超过限制，会将 labelValue 调整为 other

func MCPServerRequestsVec
func MCPServerRequestsVec(labels []string) RequestsVec
MCPServerRequestsVec 创建用户统计 mcp_server requests 的指标项

名称为：mcp_server_requests
func PBRPCServerRequestsVec
func PBRPCServerRequestsVec(labels []string) RequestsVec
PBRPCServerRequestsVec 创建用户统计 pbrpc_server requests 的指标项

名称为：pbrpc_server_requests
type WorkingVec
WorkingVec 用于统计当前并发工作量（处理中的任务数）的计数器

type WorkingVec interface {
    prometheus.Collector
    WithLabelValues(lvs ...string) prometheus.Gauge
}
func ClientWorkingVec
func ClientWorkingVec(labels []string) WorkingVec
ClientWorkingVec 生成采集 RPC Client 并发工作数的 GaugeVec

func LimitWorkingVec
func LimitWorkingVec(vec WorkingVec, limit int) WorkingVec
LimitWorkingVec 限制 labelValue 的个数，避免使用错误，值太多导致内存无限使用 若超过限制，会将 labelValue 调整为 over_limit

func ServerWorkingVec
func ServerWorkingVec(labels []string) WorkingVec
ServerWorkingVec 生成采集 RPC Server 并发工作数的 GaugeVec

名称为：server_working
type WorkingVecGather
WorkingVecGather 聚合采集器，用于从多个不同的地方/系统，采集同一类 WorkingVec 指标到 同一个指标项里。 如采集 redis、MySQL、RAL 的网络连接池的信息

type WorkingVecGather struct {
    Vec         WorkingVec
    GatherFuncs []func(vec WorkingVec)
}
func (*WorkingVecGather) Collect
func (st *WorkingVecGather) Collect(metrics chan<- prometheus.Metric)
Collect 实现 prometheus.Collector 的方法

func (*WorkingVecGather) Describe
func (st *WorkingVecGather) Describe(descs chan<- *prometheus.Desc)
Describe 实现 prometheus.Collector 的方法

func (*WorkingVecGather) RegisterGatherFunc
func (st *WorkingVecGather) RegisterGatherFunc(fn func(vec WorkingVec))
RegisterGatherFunc 注册采集函数

Subdirectories
Name	Synopsis
..
