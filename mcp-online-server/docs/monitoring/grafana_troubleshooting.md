# Grafana Dashboard 故障排除指南

## 平均响应时间显示 "No Data" 问题

### 问题诊断步骤

1. **检查 Prometheus 中是否存在相关 metrics**
   ```bash
   # 在 Prometheus 查询界面执行以下查询
   {__name__=~"mcp_tool_call_.*"}
   ```

2. **检查具体的执行时间 metrics**
   ```promql
   # 检查是否存在执行时间 histogram
   mcp_tool_call_execution_duration_seconds_sum
   mcp_tool_call_execution_duration_seconds_count
   
   # 检查备用 metrics
   mcp_tool_call_running_to_success_duration_seconds_sum
   mcp_tool_call_running_to_success_duration_seconds_count
   ```

3. **检查 namespace 标签**
   ```promql
   # 检查 metrics 是否有 namespace 标签
   label_values({__name__=~"mcp_.*"}, namespace)
   
   # 如果返回空，说明 metrics 没有 namespace 标签
   ```

### 解决方案

#### 方案 1: 使用修复后的查询（推荐）
当前 Dashboard 已经使用了容错查询：
```promql
rate(mcp_tool_call_execution_duration_seconds_sum[5m]) / rate(mcp_tool_call_execution_duration_seconds_count[5m]) or rate(mcp_tool_call_running_to_success_duration_seconds_sum[5m]) / rate(mcp_tool_call_running_to_success_duration_seconds_count[5m]) or vector(0)
```

#### 方案 2: 简化查询（如果仍有问题）
如果上述查询仍然显示 "No Data"，可以尝试：
```promql
# 最简单的查询，不使用 namespace 过滤
rate(mcp_tool_call_execution_duration_seconds_sum[5m]) / rate(mcp_tool_call_execution_duration_seconds_count[5m])
```

#### 方案 3: 使用备用 metrics
如果主要的 execution_duration metrics 不存在，使用：
```promql
# 使用运行到成功的时间作为执行时间
rate(mcp_tool_call_running_to_success_duration_seconds_sum[5m]) / rate(mcp_tool_call_running_to_success_duration_seconds_count[5m])
```

### 验证步骤

1. **检查 metrics 端点**
   ```bash
   curl http://your-service:port/metrics | grep mcp_tool_call_execution_duration
   ```

2. **验证数据是否存在**
   ```promql
   # 在 Prometheus 中检查是否有数据点
   mcp_tool_call_execution_duration_seconds_sum > 0
   ```

3. **检查时间范围**
   - 确保选择的时间范围内有 tool call 执行
   - 尝试扩大时间范围（如从 1 小时改为 24 小时）

## Namespace 变量问题

### 问题：Namespace 下拉框为空

**原因**: Metrics 没有 namespace 标签

**解决方案**:
1. 修改 namespace 变量查询为：
   ```promql
   label_values({__name__=~"mcp_.*"}, namespace)
   ```

2. 如果仍然为空，可以手动添加选项：
   - 在 Grafana 中编辑 namespace 变量
   - 添加自定义选项：`dataeng-prod`, `dataeng-dev`

### 问题：选择 namespace 后仍显示所有数据

**原因**: 这是正常行为，当前的查询设计为向后兼容

**说明**: 查询使用 `{namespace=~"$namespace"} or metric_name` 模式，会：
- 如果有 namespace 标签，按选择过滤
- 如果没有 namespace 标签，显示所有数据

## 其他常见问题

### 1. 图表显示多条相同的线

**原因**: 多个实例报告相同的 metrics
**解决**: 使用 `sum()` 聚合函数（已在当前 Dashboard 中实现）

### 2. 数据延迟或不更新

**检查项**:
- Prometheus 抓取配置
- 服务的 `/metrics` 端点是否正常
- 网络连接问题

### 3. 告警面板显示错误值

**检查**:
- 确认相关的 long_running 和 long_pending metrics 是否正常工作
- 检查业务逻辑是否正确更新这些 metrics

## 调试工具

### Prometheus 查询测试
```promql
# 测试基本 metrics 可用性
up{job="your-service"}

# 测试 MCP metrics
{__name__=~"mcp_.*"}

# 测试特定 metrics
mcp_tool_call_execution_duration_seconds_bucket
```

### 日志检查
```bash
# 检查服务日志中的 metrics 相关错误
grep -i "metric\|prometheus" /path/to/service.log
```

## 联系支持

如果以上步骤都无法解决问题，请提供：
1. Prometheus 查询结果截图
2. Grafana 错误信息
3. 服务的 `/metrics` 端点输出
4. 相关的服务日志
