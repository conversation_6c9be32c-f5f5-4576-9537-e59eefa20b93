{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Process status published by Go Prometheus client library, e.g. memory used, fds open, GC details", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 6671, "graphTooltip": 0, "id": 32884, "links": [{"asDropdown": true, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["默认大盘"], "targetBlank": false, "title": "快速访问", "tooltip": "", "type": "dashboards", "url": ""}], "liveNow": false, "panels": [{"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 0}, "id": 22, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "count(count(process_cpu_seconds_total{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}) by (pod))", "interval": "", "legendFormat": "{{pod}} ", "refId": "A"}], "title": "Pod/实例数量", "type": "stat"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "core"}, "overrides": []}, "gridPos": {"h": 4, "w": 5, "x": 3, "y": 0}, "id": 23, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "avg(sum(rate(process_cpu_seconds_total{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}[$__interval])) by (pod))", "instant": false, "interval": "", "legendFormat": "{{pod}} ", "refId": "A"}], "title": "平均CPU使用量", "type": "stat"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 5, "x": 8, "y": 0}, "id": 24, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "avg(sum(process_resident_memory_bytes{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}[$__interval]) by (pod))", "instant": false, "interval": "", "legendFormat": "{{pod}} ", "refId": "A"}], "title": "平均内存使用量", "type": "stat"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 5, "x": 13, "y": 0}, "id": 25, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "avg(sum(process_open_fds{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}) by (pod))by(container)", "instant": false, "interval": "", "legendFormat": "{{container}}", "refId": "A"}], "title": "平均FD使用量", "type": "stat"}, {"datasource": {"uid": "${ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "dateTimeAsSystem"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "id": 28, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["max"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "bottomk(1,process_start_time_seconds{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}*1000)", "instant": true, "interval": "", "legendFormat": "最长运行时长", "refId": "A"}, {"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "topk(1,process_start_time_seconds{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}*1000)", "hide": false, "instant": true, "interval": "", "legendFormat": "最短运行时长", "refId": "B"}], "title": "进程启动时间", "type": "stat"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "5FafTHQnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 16, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "5FafTHQnz"}, "refId": "A"}], "title": "进程监控", "type": "row"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 9, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "core"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "id": 11, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "sum(rate(process_cpu_seconds_total{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}[$__interval])) by (pod)", "interval": "", "legendFormat": "{{pod}} ", "refId": "A"}], "title": "process_cpu_seconds_total(进程使用CPU核数)", "transformations": [], "type": "timeseries"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 5}, "id": 14, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "sum(process_resident_memory_bytes{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"})by(pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "title": "process_resident_memory_bytes(进程常驻内存大小)", "type": "timeseries"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 19, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "sum(process_open_fds{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "title": "process_open_fds(打开FD数量)", "type": "timeseries"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 13}, "id": 13, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "process_virtual_memory_bytes{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "title": "process_virtual_memory_bytes(虚拟内存大小)", "type": "timeseries"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 21}, "id": 26, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "process_max_fds{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "title": "process_max_fds(进程最大FD限额)", "type": "timeseries"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "进程启动时间"}, "properties": [{"id": "unit", "value": "dateTimeAsSystem"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 21}, "id": 20, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "process_start_time_seconds{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}*1000", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "title": "process_start_time_seconds(进程启动时间)", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value #B": false, "__name__": true, "app_name": true, "cluster": true, "container": false, "cprom_tenant": true, "data_source": true, "instance": true, "job": true, "plat": true}, "indexByName": {"Time": 0, "Value #B": 12, "__name__": 1, "app_name": 2, "cluster": 5, "container": 6, "cprom_tenant": 7, "data_source": 8, "instance": 9, "job": 10, "namespace": 4, "plat": 11, "pod": 3}, "renameByName": {"Value": "进程启动时间", "Value #B": "", "app_name": ""}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "进程启动时间"}]}}], "type": "table"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "5FafTHQnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 29}, "id": 18, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "5FafTHQnz"}, "refId": "A"}], "title": "Go语言监控", "type": "row"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 30}, "id": 7, "links": [], "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.1", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "go_goroutines{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod}} {{container}}", "metric": "go_goroutines", "refId": "A", "step": 4}], "title": "go_goroutines(Go协程数)", "type": "timeseries"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 30}, "id": 21, "links": [], "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.1", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "go_threads{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod}} {{container}}", "metric": "go_goroutines", "refId": "A", "step": 4}], "title": "go_threads(Go线程数)", "type": "timeseries"}, {"datasource": {"uid": "${ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 37}, "id": 8, "links": [], "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.1", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "increase(go_gc_duration_seconds_sum{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}[$__interval])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod}}", "metric": "go_gc_duration_seconds", "refId": "A", "step": 4}], "title": "go_gc_duration_seconds_sum(Go GC耗时)", "type": "timeseries"}, {"datasource": {"uid": "${ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 37}, "id": 29, "links": [], "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.1", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "increase(go_gc_duration_seconds_count{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}[$__interval])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod}}", "metric": "go_gc_duration_seconds", "refId": "A", "step": 4}], "title": "go_gc_duration_seconds_count(Go GC次数)", "type": "timeseries"}, {"datasource": {"uid": "${ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "alloc rate"}, "properties": [{"id": "unit", "value": "Bps"}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 44}, "id": 2, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.1", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "go_memstats_alloc_bytes{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod}} - bytes allocated", "metric": "go_memstats_alloc_bytes", "refId": "A", "step": 4}, {"datasource": {"uid": "${ds}"}, "expr": "rate(go_memstats_alloc_bytes_total{namespace=~\"^($namespace)$\",pod=~\"^($pod)$\"}[30s])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod}} - alloc rate", "metric": "go_memstats_alloc_bytes_total", "refId": "B", "step": 4}, {"datasource": {"uid": "${ds}"}, "expr": "go_memstats_stack_inuse_bytes{namespace=~\"^($namespace)$\",pod=~\"^($pod)$\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod}} - stack inuse", "metric": "go_memstats_stack_inuse_bytes", "refId": "C", "step": 4}, {"datasource": {"uid": "${ds}"}, "expr": "go_memstats_heap_inuse_bytes{namespace=~\"^($namespace)$\",pod=~\"^($pod)$\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{pod}} - heap inuse", "metric": "go_memstats_heap_inuse_bytes", "refId": "D", "step": 4}], "title": "go memstats", "type": "timeseries"}, {"datasource": {"uid": "${ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 44}, "id": 27, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.0.4", "targets": [{"datasource": {"uid": "${ds}"}, "exemplar": true, "expr": "count(go_info{namespace=~\"$namespace\",app_name=~\"$app_name\", pod=~\"$pod\"})by(namespace,pod,container,version)", "format": "table", "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "metric": "go_gc_duration_seconds", "refId": "A", "step": 4}], "title": "go_info(Go版本)", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "app_name": true}, "indexByName": {"Time": 0, "Value": 5, "container": 3, "namespace": 2, "pod": 1, "version": 4}, "renameByName": {}}}], "type": "table"}], "refresh": "", "schemaVersion": 36, "style": "dark", "tags": ["默认大盘"], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus监控", "value": "Prometheus监控"}, "hide": 0, "includeAll": false, "label": "数据源", "multi": false, "name": "ds", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "/^Prometheus/", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".*", "current": {"selected": false, "text": "mcp-online-server-proxy", "value": "mcp-online-server-proxy"}, "datasource": {"type": "prometheus", "uid": "${ds}"}, "definition": "label_values(process_resident_memory_bytes{job!=\"cprom-agent\"}, app_name)", "hide": 0, "includeAll": false, "multi": false, "name": "app_name", "options": [], "query": {"query": "label_values(process_resident_memory_bytes{job!=\"cprom-agent\"}, app_name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": true, "text": ["dataeng-prod"], "value": ["dataeng-prod"]}, "datasource": {"type": "prometheus", "uid": "${ds}"}, "definition": "label_values(process_resident_memory_bytes{app_name=\"$app_name\"}, namespace)", "hide": 0, "includeAll": true, "multi": true, "name": "namespace", "options": [], "query": {"query": "label_values(process_resident_memory_bytes{app_name=\"$app_name\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "${ds}"}, "definition": "label_values(process_resident_memory_bytes{namespace=~\"$namespace\",app_name=\"$app_name\"}, pod)", "hide": 0, "includeAll": true, "multi": true, "name": "pod", "options": [], "query": {"query": "label_values(process_resident_memory_bytes{namespace=~\"$namespace\",app_name=\"$app_name\"}, pod)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Go进程通用监控", "uid": "mcp-env_go_process", "version": 3, "weekStart": ""}