dataeng_k8s交互服务接口设计

# 背景目标
* 目标：实现对k8s集群的操作，包括创建pod（job）、查询状态、获取日志等

服务使用GoLang实现，部署在cnap上

# 接口设计
## 启动pod执行任务
**接口路径：**/api/v1/internal/dataeng_k8s/create_task

**请求方法：**POST

**请求header：**

|Header|是否必填|说明||
|-|-|-|-|
|X-Trace-Id|否|可选，用于问题排查的链路追踪||

**请求Body：**

|参数|是否必填|参数类型|默认值|说明|示例|
|-|-|-|-|-|-|
|image_path|是|String||启动容器依赖的镜像地址，平台自己的百度云ccr仓库||
|command|是|String||pod容器启动后的执行命令命令中的脚本需要在镜像中脚本路径需要填写绝对路径|/bin/bash /home/<USER>/scripts/run.shpython /home/<USER>/scripts/run.py|
|args|否|[]string|[]|用户命令的参数配置，示例如下：["--a", "/home/<USER>/dataeng/inpu_data/file.json", "--b", "sss"]||
|timeout|是|int||run_mode="sync"时需要配置的参数，单位秒，默认300s||
|output_info|是|String||{"path_name": "xx", "file_name": "xxx"}path_name为每个任务唯一的目录名，file_name为可选的输出文件名path_name、file_name需要在启动pod时作为env参数给到容器，需要容器中可获取path_name对应环境变量：DATAENG_OUTPUT_DIRfile_name对应环境变量：DATAENG_OUTPUT_FILE_NAME||
|source_type|是|string|verifier_system|任务来源类型，用于区分使用哪个k8s集群，可选：verifier_system、dataeng、mcp||
|mount_path_config|是|String||镜像地址和bos的挂载配置，包含三个目录配置：base_path: 挂载的根目录input_path: 任务执行需要的输入数据（对应bos： {dataeng_bucket_name}/input_data）script_path: 任务执行的通用脚本（对应bos： {dataeng_bucket_name}/scripts）output_path: 任务输出的目录（对应bos： {dataeng_bucket_name}/output_data）{"base_path": "/home/<USER>/dataeng", "input_path": "{base_path}/[唯一id]/input_data", "script_path": "{base_path}/[唯一id]/script", "output_path": "{base_path}/[唯一id]/output_data"}base_path是容器内产出数据和bos挂载的根目录，其余几个path都必须在这个根目录下面，需要有一个唯一id的子目录（用于区分不同的任务），建议使用trace_id||
|task_type|是|String|OTHER|枚举值，提交的任务类型：SWE-VERIFIER、MCP、OTHER|SWE|
|input|否|String||任务的输入数据：只支持string_json和bos的url地址可以是string_json格式的单条数据，也可以是bos地址如果为string_json格式数据，需要调用方自行确保数据的正确性默认只支持bos的地址|1、string_json格式示例：跟开源的SWE-bench_Verified数据集格式一致，可参考：https://huggingface.co/datasets/SWE-bench/SWE-bench_Verified/viewer/default/test?views%5B%5D=test&row=02、bos地址|
|resources|否|string(json)|{"cpu": "2", "memory": {"type": "G", "value": 4}}|任务资源配置信息cpu说明：memory说明：支持配置G、M单位，只支持整数配置默认配置：{"cpu": "1", "memory": "1G"}|{"cpu": "1", "memory": "1G"}|
|isRootUser|否|boolean|false|是否已root用户启动容器||
|启动容器需要添加的env：系统相关：DATAENG_K8S_SERVICE_HOST：k8s交互服务地址DATAENG_BOS_AKDATAENG_BOS_SKDATAENG_BOS_BUCKET以上变量是用于SDK中执行容器启动需要的参数用户相关：DATAENG_OUTPUT_DIR：用户输出目录，将mount_path_config中output_path和output_info中path_name拼接出的绝对路径DATAENG_OUTPUT_FILE_NAME：用户输出文件名DATAENG_INPUT_DATA_DIR：用户输入文件的目录，将mount_path_config中input_path和output_info中path_name拼接出的绝对路径DATAENG_SCRIPT_DATA_DIR：用户执行脚本目录，将mount_path_config中script_path和output_info中path_name拼接出的绝对路径以上变量是提供给用户使用的||||||

**响应response：**

trace_id用于问题追踪，请求的header如果带了`X-Trace-Id，这直接用，如果没有，系统生成`

```
# 成功返回, task_id表示jobid
{"code": 0, "data":{"task_id":"job-******}, "message": "success"}
# 失败返回，code非0
{"code": 10001,  "message": "task run failed"} 
```
**错误码类型**

[错误码设计](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tjIp17bwPd/_cb7F-QsrB/Cn5zjhUC07rYEO?t=mention&mt=doc&dt=doc)

|错误码|说明|
|-|-|
|10001|k8s的qps受限，限流报错|
|10202|集群资源受限，无法创建任务（任务数达到集群最大值）|
|-1|任务提交非预期错误|

## 查询pod任务状态
**接口路径：**/api/v1/internal/dataeng_k8s/get_task_state?task_id=xxx

**请求方法：**GET

**请求params：**

|参数|参数类型|说明|
|-|-|-|
|task_id|string|提交任务返回的task_id|

**响应response：**

```
# 成功返回, 容器运行成功，output_url存储输出文件bos地址，容器运行失败，log字段表示失败原因
{"code": 0, "data": {"state": "pending/running/failed/success/", "output_url": "xxx","log":"***"}, "message": "success"}
# 失败返回
{"code": -1, "data": {}, "message": "get task state failed: xxxx"}
```
## 获取pod中任务执行日志
**接口路径：**/api/v1/internal/dataeng_k8s/get_task_log_stream

**请求方法：**POST

**请求params：**

|参数|参数类型|说明|
|-|-|-|
|task_id|string|提交任务返回的task_id|
|since_seconds|int|获取任务日志的起始时间，即从since_seconds之前开始获取日志|

**响应response：**

```
# 成功返回,log_time表示获取到的最后一行日志，日志的输出时间，如果没有日志，log_time返回当前时间
{"code": 0, "data":{"log":"****", "log_time": "2025-06-30 17:00:00"}, "message": "success"}
# 失败返回
{"code": -1,  "message": "get task logs failed: xxxx"}
```
如何计算since_seconds的值：

* 每次查询接口返回日志，会返回log_time字段，表示本次获取的日志的结束时间点
* 再次请求时，可以通过now() - log_time，计算其差值（秒），作为since_seconds的值

## 删除job接口
**接口路径：/api/v1/internal**/dataeng_k8s/delete_job

**请求方法：**GET

请求body:

|参数|类型|是否必填|备注|
|-|-|-|-|
|task_id|string|是|job名称|

**响应response：**

```
# 成功返回
{"code": 0, "data": {}, "message": "success"}
# 失败返回
{"code": -1, "data": {}, "message": "**"}
```
## 2.5 获取集群pod数量（对外接口）
**接口路径：/api/v1/openapi**/dataeng_k8s/get_pod_count

**请求方法：GET**

**请求params：**

|参数|参数类型|说明|
|-|-|-|
|source_type|string|任务类型，verifier_system/dataeng/mcp|

**响应response：**

```
# 成功返回
{"code": 0, "data": {"count":3}, "message": "success"}
# 失败返回
{"code": -1, "data": {}, "message": "**"}
```