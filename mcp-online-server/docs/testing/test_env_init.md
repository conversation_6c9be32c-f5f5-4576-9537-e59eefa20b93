# 环境初始化功能测试文档

## 功能概述

本次实现了基于image_id的容器环境初始化功能，支持在容器运行后根据环境配置自动初始化各种类型的环境依赖。

## 实现的功能

### 1. K8s Proxy Client 新增接口

在 `model/dao/rpc_k8s_proxy/k8s_proxy_client.go` 中新增了两个方法：

- `ExecCommand`: 在容器内执行命令
- `UploadFile`: 上传文件到容器

### 2. Session Init 流程修改

在 `model/service/session/session_init.go` 中修改了 `waitForContainerReady` 方法：

- 当传入 `image_id` 时，在容器状态为 running 且工具列表不为空后，执行环境初始化
- 如果没有传入 `image_id`，维持原有流程不变

### 3. 环境依赖处理

支持以下类型的环境依赖：

#### file 类型
- 将文件内容上传到 BOS，key 格式：`session_env_init/{session_id}/{filename}`
- 获取永久链接
- 通过 k8s_client 的 upload_file 方法上传到容器

#### directory 类型
- 直接调用容器内的 `mkdir -p` 命令创建目录
- 自动创建父目录，如果目录已存在不会报错

#### db 类型
- 使用现有的数据库初始化逻辑创建数据库文件
- 将数据库文件上传到 BOS
- 通过 k8s_client 的 upload_file 方法拷贝到容器

#### url 类型
- 直接调用 k8s_client 的 upload_file 方法，使用 URL 作为文件源

#### cmd 类型
- 直接调用 k8s_client 的 exec_command 方法执行命令
- 支持 bash 命令和普通命令

#### delay_cmd 类型
- 暂时不处理（按用户要求）

## 错误处理

实现了完整的错误处理机制，包括：
- K8s API 调用错误的映射
- 环境依赖处理失败的详细错误信息
- 容器环境初始化失败的回滚处理

## 使用方式

在调用 session_init API 时，传入 `image_id` 参数：

```json
{
  "server_ids": [1, 2],
  "env_id": 123,
  "image_id": "custom-image-uuid",
  "timeout_minutes": 30
}
```

系统会：
1. 验证 image_id 是否存在
2. 创建容器并等待运行
3. 从 env_id 对应的环境配置中获取 environment_dependency
4. 逐个处理环境依赖项
5. 返回初始化完成的容器信息

## 注意事项

1. 环境依赖配置存储在 `obj_mcp_env` 表的 `env_dependency` 字段中
2. BOS 文件的 key 格式为 `session_env_init/{session_id}/{filename}`
3. 所有文件操作都会生成永久链接（过期时间为 -1）
4. 数据库文件会先在本地创建，然后上传到容器
5. 命令执行支持 bash 和普通命令两种格式

## 测试建议

1. 创建包含各种类型环境依赖的环境配置
2. 创建自定义镜像配置
3. 调用 session_init API 测试完整流程
4. 验证容器内文件和目录是否正确创建
5. 验证数据库是否正确初始化
6. 验证命令是否正确执行
