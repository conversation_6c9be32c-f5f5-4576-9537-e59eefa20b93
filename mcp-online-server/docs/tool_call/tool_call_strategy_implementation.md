# Tool Call 策略模式实现总结

## 功能概述

实现了基于 session 的 image_id 来判断 tool_call 执行路径，并使用策略模式处理参数解析和命令拼装：

- **没有 image_id**：走原有的 MCP 工具调用链路
- **有 image_id**：走 k8s_proxy_client 直接执行链路，使用策略模式构建命令

## 核心设计

### 1. 策略模式架构

**文件**: `model/service/tool/command_strategy.go`

- **CommandBuildStrategy**: 策略接口
- **CommandStrategyManager**: 策略管理器
- **SweAgentStrategy**: 具体策略实现
- **GlobalCommandStrategyManager**: 全局策略管理器实例

### 2. SweAgent 策略

根据您的要求实现的命令构建策略：

**输入示例**:
```json
{
  "tool_name": "file_editor",
  "arguments": {
    "command": "view",
    "path": "/testbed", 
    "concise": true
  }
}
```

**输出示例**:
```bash
["file_editor", "view", "--path", "/testbed", "--concise", "True"]
```

### 3. 参数处理规则

1. **command 参数**: 作为子命令直接跟在工具名后面
2. **其他参数**: 转换为 `--key value` 格式
3. **布尔值处理**: `true` → `"True"`, `false` → `"False"`
4. **参数顺序**: 按预定义顺序处理常见参数，保证输出一致性

## 实现修改

### 1. 数据库表结构

**文件**: `script/db/db.sql`

只在 `obj_session` 表中添加了 `image_id` 字段，不新增额外表：
```sql
image_id VARCHAR(256) NOT NULL DEFAULT "" COMMENT '关联的镜像ID，用于环境初始化',
```

### 2. Tool Call 执行逻辑

**文件**: `model/service/tool/tool_call_sync.go`

核心判断逻辑：
```go
if session.ImageID != "" {
    // 有image_id，走k8s_proxy_client直接执行路径
    response, err := s.executeToolViaK8sProxy(ctx, session, &input.ToolCallRequest)
} else {
    // 没有image_id，走原有的MCP工具调用路径
    // 创建任务记录，等待mcp_runtime处理
}
```

### 3. K8s Proxy 执行流程

在 `executeToolViaK8sProxy` 方法中：

1. **检查重复**: 在 tool_call_task 表中检查 call_id 是否重复
2. **构建命令**: 使用 SweAgent 策略构建执行命令
3. **创建记录**: 在 tool_call_task 表中创建记录，状态直接设为 `running`
4. **执行命令**: 调用 k8s_proxy_client.ExecCommand
5. **更新结果**: 更新任务状态和执行结果

## 使用示例

### API 调用示例

```json
{
  "session_id": 12345,
  "call_id": "call-uuid",
  "name": "file_editor", 
  "arguments": {
    "command": "view",
    "path": "/testbed",
    "concise": true
  }
}
```

### 命令构建过程

1. **策略选择**: 自动使用 "sweagent" 策略
2. **参数解析**: JSON 参数解析为 map
3. **命令构建**: 
   - 工具名: `file_editor`
   - 子命令: `view` (来自 command 参数)
   - 参数: `--path /testbed --concise True`
4. **最终命令**: `["file_editor", "view", "--path", "/testbed", "--concise", "True"]`

### 执行结果格式

```json
{
  "call_id": "call-uuid",
  "status": "success", 
  "result": "{\"content\":[{\"type\":\"text\",\"text\":\"命令执行输出\"}]}",
  "error_message": "",
  "old_env_md5": "",
  "new_env_md5": "",
  "old_env_url": "",
  "new_env_url": ""
}
```

## 策略扩展

### 添加新策略

```go
type NewStrategy struct{}

func (s *NewStrategy) GetStrategyName() string {
    return "new_strategy"
}

func (s *NewStrategy) BuildCommand(toolName string, arguments map[string]interface{}) ([]string, error) {
    // 自定义命令构建逻辑
    return command, nil
}

// 注册策略
GlobalCommandStrategyManager.RegisterStrategy(&NewStrategy{})
```

### 使用不同策略

```go
// 可以扩展为根据不同条件选择不同策略
command, err := GlobalCommandStrategyManager.BuildCommand("new_strategy", toolName, argumentsJSON)
```

## 测试验证

**文件**: `model/service/tool/command_strategy_test.go`

包含完整的单元测试：
- SweAgent 策略的各种参数组合测试
- 策略管理器的功能测试
- 错误处理测试
- 全局管理器测试

## 优势

1. **策略模式**: 易于扩展新的命令构建策略
2. **参数顺序**: 保证命令参数的一致性
3. **类型安全**: 完整的类型定义和错误处理
4. **测试覆盖**: 全面的单元测试保证质量
5. **向后兼容**: 原有流程完全不受影响
6. **简化架构**: 不需要额外的数据表，复用现有 tool_call_task 表

## 注意事项

1. 当前只实现了 "sweagent" 策略
2. 布尔值会转换为字符串 "True"/"False"
3. 参数顺序按预定义规则排序
4. 所有执行记录都保存在 tool_call_task 表中
5. 错误处理和状态更新与原有流程保持一致
