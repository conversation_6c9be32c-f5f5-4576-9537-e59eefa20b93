# Tool Call 支持 Image ID 功能实现文档

## 功能概述

实现了基于 session 是否关联 image_id 来判断 tool_call 执行路径的功能：

- 如果 session 没有关联 image_id，走原有的 MCP 工具调用链路
- 如果 session 关联了 image_id，走新的 k8s_proxy_client 的 ExecCommand 调用链路

## 实现的修改

### 1. 数据库表结构修改

**文件**: `script/db/db.sql`

在 `obj_session` 表中添加了 `image_id` 字段：

```sql
image_id VARCHAR(256) NOT NULL DEFAULT "" COMMENT '关联的镜像ID，用于环境初始化',
```

### 2. 数据模型修改

**文件**: `model/dao/session/def.go`

在 `ObjSession` 结构体中添加了 `ImageID` 字段：

```go
ImageID         string               `gorm:"column:image_id;default:''" json:"image_id"`
```

### 3. Session 初始化修改

**文件**: `model/service/session/session_init.go`

- 在创建 session 时保存 `image_id`
- 在容器运行后，如果有 `image_id` 就执行环境初始化

### 4. Tool Call 执行路径判断

**文件**: `cmd/mcp_runtime.go`

在 `ToolExecutor.ExecuteTask` 方法中添加了路径判断逻辑：

```go
// 根据session是否有image_id来判断执行路径
if session.ImageID != "" {
    // 如果有image_id，走k8s_proxy_client的ExecCommand路径
    result, toolErr = e.executeToolViaK8sProxy(ctx, session, task.ToolName, argumentsStr)
} else {
    // 如果没有image_id，走原有的MCP工具调用路径
    result, toolErr = e.runtime.toolManager.InvokableRun(ctx, serverName, toolName, argumentsStr)
}
```

### 5. K8s Proxy 执行方法

**文件**: `cmd/mcp_runtime.go`

实现了 `executeToolViaK8sProxy` 方法：

- 解析工具参数
- 构建命令行参数
- 调用 k8s_proxy_client 的 ExecCommand 接口
- 返回格式化的执行结果

## 使用示例

### 环境初始化示例

在环境配置中添加 file_editor 工具和执行权限：

```json
{
  "environment_dependency": [
    {
      "path": "/usr/local/bin/file_editor",
      "type": "file",
      "content": "#!/usr/bin/env python3\n# file_editor.py content here..."
    },
    {
      "type": "cmd",
      "content": "chmod +x /usr/local/bin/file_editor"
    }
  ]
}
```

### Session 初始化

```json
{
  "server_ids": [1, 2],
  "env_id": 123,
  "image_id": "custom-image-uuid",
  "timeout_minutes": 30
}
```

### Tool Call 执行

当调用工具时：

```json
{
  "session_id": 12345,
  "call_id": "call-uuid",
  "name": "file_editor",
  "arguments": {
    "action": "view",
    "path": "/testbed",
    "concise": true
  }
}
```

系统会：

1. 检查 session 是否有 image_id
2. 如果有，构建命令：`["file_editor", "--action", "view", "--path", "/testbed", "--concise", "true"]`
3. 通过 k8s_proxy_client 在容器内执行命令
4. 返回执行结果

## 执行流程

### 有 image_id 的情况

1. **Session 初始化**: 保存 image_id 到数据库
2. **环境初始化**: 在容器运行后执行环境依赖初始化
3. **Tool Call**: 通过 k8s_proxy_client 直接在容器内执行命令

### 没有 image_id 的情况

1. **Session 初始化**: image_id 为空
2. **Tool Call**: 走原有的 MCP 工具调用链路

## 优势

1. **向后兼容**: 没有 image_id 的 session 继续使用原有流程
2. **灵活性**: 有 image_id 的 session 可以直接在容器内执行工具
3. **性能**: 减少了 MCP 协议的开销，直接执行命令
4. **简单**: 工具调用逻辑简化，参数直接转换为命令行参数

## 注意事项

1. 需要确保容器内已经安装了相应的工具（通过环境初始化）
2. 工具参数会直接转换为命令行参数格式
3. 执行结果格式与 MCP 工具调用保持一致
4. 错误处理机制保持不变

## 测试建议

1. 创建包含工具文件和执行权限的环境配置
2. 使用 image_id 创建 session
3. 验证环境初始化是否正确
4. 测试工具调用是否能正确执行
5. 验证返回结果格式是否正确
