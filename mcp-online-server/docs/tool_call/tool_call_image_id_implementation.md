# Tool Call 支持 Image ID 功能实现总结

## 功能概述

实现了基于 session 是否关联 image_id 来判断 tool_call 执行路径的功能：

- **没有 image_id**：走原有的 MCP 工具调用链路（通过 mcp_runtime 程序处理）
- **有 image_id**：走新的 k8s_proxy_client 直接执行链路（直接在容器内执行命令）

## 核心设计理念

当传递了 `image_id` 时，用户上传的镜像中没有 `mcp_runtime` 主控程序，因此：
- `mcp_runtime` 不会运行，也不需要处理这种情况
- 直接通过 `k8s_proxy_client` 的 `ExecCommand` 在容器内执行工具命令
- 不需要 MCP 协议，不需要任务轮询机制

## 实现的修改

### 1. 数据库表结构修改

**文件**: `script/db/db.sql`

1. 在 `obj_session` 表中添加了 `image_id` 字段：
```sql
image_id VARCHAR(256) NOT NULL DEFAULT "" COMMENT '关联的镜像ID，用于环境初始化',
```

2. 新增 `obj_cmd_exec` 表记录直接命令执行：
```sql
CREATE TABLE obj_cmd_exec (
    exec_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    call_id VARCHAR(64) NOT NULL,
    session_id BIGINT NOT NULL,
    tool_name VARCHAR(128) NOT NULL,
    arguments TEXT,
    command TEXT NOT NULL,
    result TEXT,
    exec_status VARCHAR(32) DEFAULT 'pending',
    error_message TEXT,
    started_at TIMESTAMP(3) NULL,
    completed_at TIMESTAMP(3) NULL,
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3),
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    is_delete tinyint(1) NOT NULL DEFAULT '0',
    -- 索引和约束
);
```

### 2. 数据模型修改

**文件**: `model/dao/session/def.go`
- 在 `ObjSession` 结构体中添加了 `ImageID` 字段

**文件**: `model/dao/cmd_exec/def.go` 和 `business.go`
- 创建了完整的 cmd_exec DAO 层，包括状态枚举和业务操作方法

### 3. Session 初始化修改

**文件**: `model/service/session/session_init.go`
- 在创建 session 时保存 `image_id`
- 在容器运行后，如果有 `image_id` 就执行环境初始化（已实现）

### 4. Tool Call 执行路径判断

**文件**: `model/service/tool/tool_call_sync.go`

核心逻辑：
```go
// 根据session是否有image_id判断执行路径
if session.ImageID != "" {
    // 有image_id，走k8s_proxy_client直接执行路径
    response, err := s.executeToolViaK8sProxy(ctx, session, &input.ToolCallRequest)
    // 直接返回结果
} else {
    // 没有image_id，走原有的MCP工具调用路径
    // 创建任务记录，等待mcp_runtime处理
}
```

### 5. K8s Proxy 直接执行

**文件**: `model/service/tool/tool_call_sync.go`

实现了 `executeToolViaK8sProxy` 方法：
1. 检查 call_id 重复（在 cmd_exec 表中）
2. 解析工具参数
3. 构建执行命令（工具名 + 参数转命令行格式）
4. 创建 cmd_exec 记录
5. 调用 k8s_proxy_client.ExecCommand 执行
6. 更新执行结果和状态
7. 返回格式化结果

## 执行流程对比

### 原有流程（没有 image_id）
```
API请求 → tool_call_sync → 创建task记录 → 等待mcp_runtime轮询 → MCP协议调用 → 返回结果
```

### 新流程（有 image_id）
```
API请求 → tool_call_sync → 创建cmd_exec记录 → k8s_proxy直接执行 → 返回结果
```

## 使用示例

### 环境初始化
```json
{
  "environment_dependency": [
    {
      "path": "/usr/local/bin/file_editor",
      "type": "file",
      "content": "#!/usr/bin/env python3\n# file_editor.py content..."
    },
    {
      "type": "cmd", 
      "content": "chmod +x /usr/local/bin/file_editor"
    }
  ]
}
```

### Session 初始化
```json
{
  "server_ids": [1, 2],
  "env_id": 123,
  "image_id": "custom-image-uuid",
  "timeout_minutes": 30
}
```

### Tool Call 执行
```json
{
  "session_id": 12345,
  "call_id": "call-uuid", 
  "name": "file_editor",
  "arguments": {
    "action": "view",
    "path": "/testbed",
    "concise": true
  }
}
```

执行命令：`["file_editor", "--action", "view", "--path", "/testbed", "--concise", "true"]`

## 优势

1. **性能提升**: 跳过 MCP 协议开销，直接执行命令
2. **简化架构**: 不需要 mcp_runtime 程序参与
3. **向后兼容**: 原有流程完全不受影响
4. **记录完整**: cmd_exec 表记录所有直接执行的命令
5. **错误处理**: 完整的状态跟踪和错误记录

## 注意事项

1. 需要确保容器内已安装相应工具（通过环境初始化）
2. 工具参数直接转换为命令行参数格式
3. 执行结果格式与 MCP 工具调用保持一致
4. 两种执行路径的监控和日志记录分别处理
