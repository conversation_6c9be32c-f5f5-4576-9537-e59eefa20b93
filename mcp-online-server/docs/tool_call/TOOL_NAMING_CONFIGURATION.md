# 工具命名配置功能

## 概述

MCP Runtime 现在支持配置工具命名方式，可以选择是否在工具名称中使用 `server_name` 前缀。

## 配置参数

### `--use-server-prefix`

- **类型**: `bool`
- **默认值**: `true`
- **描述**: 控制是否在工具名称中使用 server_name 前缀

## 使用方式

### 1. 传统模式（默认）

```bash
./mcp_runtime --session-id 123 --use-server-prefix=true
```

在此模式下：
- 工具名称格式：`server_name__tool_name`
- 例如：`filesystem__read_file`、`sqlite__query`
- 执行时通过解析 `__` 分隔符来确定服务器和工具名称

### 2. 简化模式

```bash
./mcp_runtime --session-id 123 --use-server-prefix=false
```

在此模式下：
- 工具名称格式：`tool_name`（直接使用原始工具名称）
- 例如：`read_file`、`query`
- 通过内部映射表来确定工具对应的服务器

## 功能特性

### 缓存机制

- **首次调用**: 从 MCP 客户端获取工具列表并建立缓存
- **后续调用**: 直接返回缓存结果，避免重复查询
- **映射表**: 维护 `tool_name -> server_name` 的映射关系

### 冲突处理

当 `--use-server-prefix=false` 时，如果多个服务器有相同的工具名称：
- 后加载的工具会覆盖先加载的工具
- 系统会记录警告日志
- 建议在此模式下确保工具名称的唯一性

## 实现细节

### 数据结构

```go
type MCPRuntimeManager struct {
    toolManager      *mcphost.MCPToolManager
    config           *mcphost.Config
    useServerPrefix  bool              // 是否使用server_name前缀
    toolsCache       []types.MCPTool   // 缓存的工具列表
    toolToClientMap  map[string]string // tool_name到server_name的映射
    cacheInitialized bool              // 缓存是否已初始化
}
```

### 工具解析逻辑

#### 传统模式 (`useServerPrefix = true`)
```go
if strings.Contains(task.ToolName, "__") {
    splits := strings.Split(task.ToolName, "__")
    serverName = splits[0]
    toolName = splits[1]
}
```

#### 简化模式 (`useServerPrefix = false`)
```go
serverName, exists = e.runtime.toolToClientMap[task.ToolName]
if !exists {
    // 错误处理
}
toolName = task.ToolName
```

## 向后兼容性

- 默认值为 `true`，保持现有行为不变
- 现有的工具调用代码无需修改
- 可以根据需要逐步迁移到简化模式

## 使用建议

### 何时使用传统模式
- 多个服务器可能有相同的工具名称
- 需要明确区分不同服务器的工具
- 现有系统已经依赖于带前缀的工具名称

### 何时使用简化模式
- 工具名称在所有服务器中是唯一的
- 希望简化工具调用接口
- 新项目或可以控制工具命名的场景

## 示例

### 配置示例

```bash
# 使用传统模式（默认）
./mcp_runtime --session-id 123 --mcp-api-base http://localhost:8080

# 使用简化模式
./mcp_runtime --session-id 123 --mcp-api-base http://localhost:8080 --use-server-prefix=false
```

### 工具列表对比

#### 传统模式输出
```json
[
  {
    "name": "filesystem__read_file",
    "description": "Read file content"
  },
  {
    "name": "sqlite__query",
    "description": "Execute SQL query"
  }
]
```

#### 简化模式输出
```json
[
  {
    "name": "read_file",
    "description": "Read file content"
  },
  {
    "name": "query",
    "description": "Execute SQL query"
  }
]
```

## 注意事项

1. **工具名称冲突**: 在简化模式下，确保不同服务器的工具名称不冲突
2. **缓存生命周期**: 缓存在整个运行时生命周期内有效，重启后会重新初始化
3. **性能优化**: 缓存机制避免了重复的工具列表查询，提高了性能
4. **错误处理**: 在简化模式下，如果工具名称不在映射表中，会返回明确的错误信息
