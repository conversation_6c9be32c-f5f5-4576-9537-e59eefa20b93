# Root User 功能测试文档

## 功能描述
在image表中新增root_user字段，image_register接口可以透传此参数，如果不传则为false。session_init时会查询并获取此参数，用于控制容器是否以root用户运行。

## 测试步骤

### 1. 数据库迁移
执行迁移脚本添加root_user字段：
```sql
-- 执行 script/db/migration_add_root_user_field.sql
```

### 2. 测试image_register接口

#### 测试用例1: 不传root_user参数（默认为false）
```bash
curl -X POST http://localhost:8080/api/v1/image/register \
  -H "Content-Type: application/json" \
  -d '{
    "image_path": "test/image:latest",
    "image_description": "测试镜像",
    "container_command": "python",
    "container_args": ["-m", "test"]
  }'
```

预期结果：
- 创建成功
- 数据库中root_user字段为0（false）

#### 测试用例2: 传root_user=true
```bash
curl -X POST http://localhost:8080/api/v1/image/register \
  -H "Content-Type: application/json" \
  -d '{
    "image_path": "test/image-root:latest",
    "image_description": "需要root权限的测试镜像",
    "container_command": "python",
    "container_args": ["-m", "test"],
    "root_user": true
  }'
```

预期结果：
- 创建成功
- 数据库中root_user字段为1（true）

#### 测试用例3: 传root_user=false
```bash
curl -X POST http://localhost:8080/api/v1/image/register \
  -H "Content-Type: application/json" \
  -d '{
    "image_path": "test/image-noroot:latest",
    "image_description": "不需要root权限的测试镜像",
    "container_command": "python",
    "container_args": ["-m", "test"],
    "root_user": false
  }'
```

预期结果：
- 创建成功
- 数据库中root_user字段为0（false）

### 3. 测试session_init接口

#### 测试用例1: 使用root_user=true的镜像
```bash
curl -X POST http://localhost:8080/api/v1/session/init \
  -H "Content-Type: application/json" \
  -d '{
    "image_id": "上面创建的root镜像的image_id",
    "timeout_minutes": 30
  }'
```

预期结果：
- session创建成功
- 调用k8s_proxy的CreateTask时，IsRootUser参数为true

#### 测试用例2: 使用root_user=false的镜像
```bash
curl -X POST http://localhost:8080/api/v1/session/init \
  -H "Content-Type: application/json" \
  -d '{
    "image_id": "上面创建的非root镜像的image_id",
    "timeout_minutes": 30
  }'
```

预期结果：
- session创建成功
- 调用k8s_proxy的CreateTask时，IsRootUser参数为false

#### 测试用例3: 不指定image_id（使用默认镜像）
```bash
curl -X POST http://localhost:8080/api/v1/session/init \
  -H "Content-Type: application/json" \
  -d '{
    "timeout_minutes": 30
  }'
```

预期结果：
- session创建成功
- 调用k8s_proxy的CreateTask时，IsRootUser参数为false（默认值）

## 验证方法

### 1. 数据库验证
```sql
-- 查看镜像表中的root_user字段
SELECT image_id, image_path, root_user FROM obj_image WHERE is_delete = 0;
```

### 2. 日志验证
查看session_init的日志，确认传递给k8s_proxy的IsRootUser参数值正确。

### 3. K8s容器验证
如果有k8s环境，可以验证容器实际是否以root用户运行：
```bash
# 在容器内执行
whoami
id
```

## 代码修改总结

1. **数据库schema**: 在obj_image表中添加root_user字段
2. **ObjImage结构体**: 添加RootUser字段
3. **ImageRegisterInputData**: 添加RootUser字段
4. **image_register服务**: 在创建镜像时设置RootUser字段
5. **ObjImage业务逻辑**: 在Update方法中包含RootUser字段
6. **session_init服务**: 从imageConfig中读取RootUser并传递给k8s_proxy

## 注意事项

1. root_user字段默认为false，符合安全最佳实践
2. 只有在明确需要root权限时才设置为true
3. 如果不传image_id，则使用默认值false
4. 该功能向后兼容，不影响现有功能
