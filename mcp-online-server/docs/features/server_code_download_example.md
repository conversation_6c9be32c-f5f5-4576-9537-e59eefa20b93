# MCP Server代码下载功能

## 功能概述

MCP Runtime现在支持从BOS URL自动下载server代码到指定目录。当MCP服务器配置中包含`server_code_bos_url`字段时，系统会自动下载并解压server代码到`/home/<USER>/servers/{server_name}/`目录。

## 支持的文件格式

- **ZIP文件**: `.zip`
- **TAR.GZ文件**: `.tar.gz`, `.tgz`
- **默认格式**: 如果无法识别文件扩展名，默认按ZIP格式处理

## 使用方式

### 1. 数据库配置

在`obj_register_mcp_server`表中，设置`server_code_bos_url`字段：

```sql
INSERT INTO obj_register_mcp_server (
    server_name,
    command,
    args,
    description,
    server_code_bos_url
) VALUES (
    'filesystem_server',
    'node',
    '["server.js"]',
    '文件系统MCP服务器',
    'https://bos.example.com/mcp-servers/filesystem.zip'
);
```

### 2. 运行时行为

当MCP Runtime启动时，会自动：

1. **检查目录**: 检查`/home/<USER>/servers/{server_name}/`目录是否已存在内容
2. **跳过下载**: 如果目录不为空，跳过下载（避免重复下载）
3. **下载文件**: 从BOS URL下载压缩包到临时位置
4. **解压文件**: 根据文件扩展名选择合适的解压方法
5. **清理临时文件**: 解压完成后删除临时下载文件

### 3. 目录结构

下载完成后的目录结构：

```
/home/<USER>/servers/
├── filesystem_server/
│   ├── server.js
│   ├── package.json
│   └── node_modules/
└── database_server/
    ├── main.py
    ├── requirements.txt
    └── src/
```

## 代码实现

### 核心方法

```go
// downloadServerCode 下载server代码到指定目录
func (m *MCPRuntimeManager) downloadServerCode(ctx context.Context, server *dao_register_mcp_server.ObjRegisterMcpServer) error {
    // 构建目标目录路径: /home/<USER>/servers/server_name/
    serverDir := filepath.Join("/home/<USER>/servers", server.ServerName)
    
    // 创建目标目录
    if err := os.MkdirAll(serverDir, 0755); err != nil {
        return fmt.Errorf("创建server目录失败: %w", err)
    }
    
    // 检查目录是否已经存在内容，如果存在则跳过下载
    entries, err := os.ReadDir(serverDir)
    if err != nil {
        return fmt.Errorf("读取server目录失败: %w", err)
    }
    
    if len(entries) > 0 {
        log.Printf("Server目录已存在内容，跳过下载: %s", serverDir)
        return nil
    }
    
    // 确定文件格式并下载解压
    // ... 详细实现见源码
}
```

### 集成到LoadMCPServers

```go
func (m *MCPRuntimeManager) LoadMCPServers(ctx context.Context, servers []*dao_register_mcp_server.ObjRegisterMcpServer) error {
    for _, server := range servers {
        if server.ServerCodeBosUrl != "" {
            // 下载server代码到指定目录
            if err := m.downloadServerCode(ctx, server); err != nil {
                log.Printf("下载server代码失败: server_name=%s, error=%v", server.ServerName, err)
                return fmt.Errorf("下载server代码失败 %s: %w", server.ServerName, err)
            }
        }
        // ... 继续加载MCP服务器配置
    }
}
```

## 错误处理

系统会处理以下错误情况：

1. **网络错误**: HTTP下载失败
2. **文件系统错误**: 目录创建失败、权限不足
3. **解压错误**: 压缩包格式错误、文件损坏
4. **路径安全**: 防止路径遍历攻击

## 日志输出

系统会输出详细的日志信息：

```
开始下载server代码: server_name=filesystem_server, bos_url=https://bos.example.com/server.zip, target_dir=/home/<USER>/servers/filesystem_server
✅Server代码下载解压完成: server_name=filesystem_server, target_dir=/home/<USER>/servers/filesystem_server
```

## 注意事项

1. **幂等性**: 多次运行不会重复下载，已存在内容的目录会被跳过
2. **权限**: 确保运行用户有权限创建`/home/<USER>/servers/`目录
3. **磁盘空间**: 确保有足够的磁盘空间存储下载的文件
4. **网络访问**: 确保能够访问BOS URL
5. **文件格式**: 建议使用标准的文件扩展名以确保正确的格式检测

## 测试

运行测试验证功能：

```bash
go test -v ./cmd/mcp_runtime_test.go ./cmd/mcp_runtime.go -run TestDownloadServerCode
```
