# Root User 功能实现总结

## 需求描述
在image表中新增一个字段叫root_user bool型，image_register接口可以透传此参数，如果不传则为false。然后session_init的时候需要查询并获取此参数。

## 实现的修改

### 1. 数据库Schema修改
**文件**: `script/db/db.sql`
- 在obj_image表中添加了`root_user tinyint(1) NOT NULL DEFAULT '0'`字段
- 位置：在container_resources字段之后

### 2. 数据模型修改
**文件**: `model/dao/obj_image/def.go`
- 在ObjImage结构体中添加了`RootUser bool`字段
- 使用GORM标签：`gorm:"column:root_user;default:false" json:"root_user"`

### 3. API接口修改
**文件**: `model/service/image/image_register.go`
- 在ImageRegisterInputData结构体中添加了`RootUser bool`字段
- JSON标签：`json:"root_user,omitempty"`
- 在Execute方法中，创建ObjImage时设置RootUser字段

### 4. 业务逻辑修改
**文件**: `model/dao/obj_image/business.go`
- 在Update方法中添加了对root_user字段的更新支持

### 5. Session初始化修改
**文件**: `model/service/session/session_init.go`
- 在buildContainerRequest方法中：
  - 默认isRootUser为false
  - 如果imageConfig不为空，则使用imageConfig.RootUser的值
  - 将isRootUser传递给k8s_proxy的CreateTaskRequest.IsRootUser字段
- 移除了TODO注释

### 6. 数据库迁移脚本
**文件**: `script/db/migration_add_root_user_field.sql`
- 创建了用于添加root_user字段的迁移脚本

## 功能特性

1. **向后兼容**: 新字段默认为false，不影响现有功能
2. **安全默认**: 默认不使用root用户，符合安全最佳实践
3. **透传支持**: image_register接口支持root_user参数透传
4. **可选参数**: 如果不传root_user参数，默认为false
5. **完整流程**: 从镜像注册到session初始化的完整支持

## API使用示例

### 镜像注册（不传root_user，默认false）
```json
{
  "image_path": "test/image:latest",
  "container_command": "python",
  "container_args": ["-m", "test"]
}
```

### 镜像注册（传root_user=true）
```json
{
  "image_path": "test/image:latest",
  "container_command": "python",
  "container_args": ["-m", "test"],
  "root_user": true
}
```

### Session初始化
```json
{
  "image_id": "your-image-id",
  "timeout_minutes": 30
}
```

## 验证方法

1. **编译验证**: ✅ 代码编译通过
2. **数据库验证**: 执行迁移脚本后检查字段是否添加成功
3. **API测试**: 使用上述示例测试image_register和session_init接口
4. **日志验证**: 检查session_init日志中传递给k8s_proxy的IsRootUser参数值

## 注意事项

1. 需要先执行数据库迁移脚本添加root_user字段
2. 该功能依赖k8s_proxy服务支持IsRootUser参数
3. 建议在生产环境中谨慎使用root_user=true，仅在必要时使用
4. 所有现有镜像的root_user字段将默认为false
