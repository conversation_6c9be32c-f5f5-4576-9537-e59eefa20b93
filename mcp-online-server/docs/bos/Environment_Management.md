# MCP运行时环境管理功能

## 概述

MCP运行时现在支持完整的环境快照和恢复功能，包括：

- 🗜️ 环境打包（tar.gz格式）
- 🔐 MD5去重机制
- ☁️ BOS云存储上传
- 📥 环境恢复和解压
- 🔄 工具执行前后的自动快照

## 功能特性

### 1. 环境快照创建

每次执行MCP工具后，系统会自动：
1. 打包当前工作目录为`env.tar.gz` 
2. 计算文件MD5值
3. 重命名为`{md5}.tar.gz`
4. 上传到BOS存储
5. 返回MD5和BOS下载URL

### 2. MD5去重机制

- 相同环境内容只上传一次
- 通过MD5值避免重复存储
- 节省存储空间和上传时间

### 3. 自动排除文件

打包时自动排除：
- `mcphost` 执行文件
- `boost.sh` 脚本
- `*.tar.gz` 文件

## 配置要求

启动MCP运行时需要提供BOS配置：

```bash
./mcp_runtime \
  --session-id=your-session-id \
  --bos-access-key=your-access-key \
  --bos-secret-key=your-secret-key \
  --bos-endpoint=https://su.bcebos.com \
  --bos-bucket=your-bucket \
  --working-dir=/tmp/mcp-runtime
```

### 必需参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--session-id` | 会话ID | `session_12345` |
| `--bos-access-key` | BOS访问密钥 | `ALTAKF2gUJak7ycdp3cYaznbL5` |
| `--bos-secret-key` | BOS秘密密钥 | `677dd0b9288f408c907ec8aa33b4532f` |
| `--bos-endpoint` | BOS服务端点 | `https://su.bcebos.com` |
| `--bos-bucket` | BOS存储桶 | `mcp-environments` |

### 可选参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--working-dir` | 工作目录 | `/tmp/mcp-runtime` |
| `--poll-interval` | 轮询间隔 | `1s` |
| `--mcp-api-base` | API基地址 | `http://localhost:8080` |

## 工作流程

### 环境初始化
1. 容器启动时从BOS下载环境包
2. 解压到工作目录
3. 初始化MCP服务器

### 工具执行
1. **执行前**: 创建环境快照（oldEnvMD5, oldEnvURL）
2. **工具执行**: 运行用户请求的MCP工具
3. **执行后**: 创建新环境快照（newEnvMD5, newEnvURL）
4. **上报结果**: 将快照信息上报到服务端

### 环境恢复
1. 根据BOS URL下载环境包
2. 解压到指定目录
3. 恢复工作环境

## 代码架构

### 核心组件

```
library/utils/
├── environment_manager.go    # 环境管理核心逻辑
├── file_operations.go        # 文件下载和解压工具
└── environment_manager_test.go # 测试文件

cmd/
└── mcp_runtime.go           # 主运行时程序
```

### 关键类型

```go
// 环境管理器
type EnvManager struct {
    workingDir string
    bosClient  *bos.Client
    config     *config.BosConfig
}

// 运行时配置
type RuntimeConfig struct {
    SessionID    string
    MCPAPIBase   string
    WorkingDir   string
    PollInterval time.Duration
    // BOS配置
    BosAccessKey string
    BosSecretKey string
    BosEndpoint  string
    BosBucket    string
}
```

### 核心方法

```go
// 创建环境快照并上传到BOS
func (e *EnvManager) CreateSnapshot(ctx context.Context) (string, string, error)

// 从BOS恢复环境
func (e *EnvManager) RestoreEnvironment(ctx context.Context, bosURL string) error

// 打包环境目录
func (e *EnvManager) packageEnvironment(ctx context.Context, tarFile string) error
```

## 错误处理

### 常见错误

1. **BOS配置错误**
   ```
   FATAL: BOS configuration is required: bos-access-key, bos-secret-key, bos-endpoint, bos-bucket
   ```
   解决：检查BOS配置参数是否完整

2. **BOS客户端初始化失败**
   ```
   Failed to create BOS client: xxx
   ```
   解决：检查BOS凭证和网络连接

3. **环境打包失败**
   ```
   tar命令执行失败: xxx
   ```
   解决：确保系统中有tar命令

4. **环境上传失败**
   ```
   上传到BOS失败: xxx
   ```
   解决：检查BOS权限和网络连接

## 监控和日志

### 关键日志信息

```
# 环境快照创建
Environment snapshot created: md5=abc123, bos_url=https://...

# 工具执行
Executing task task_123: tool_name
Task task_123 completed successfully

# 环境恢复
环境恢复完成

# BOS上传去重
BOS对象已存在，跳过上传: bos_key=mcp_env/20241208/...
```

### 性能监控

- 环境打包时间
- BOS上传时间
- 文件大小统计
- MD5计算时间

## 最佳实践

### 1. 存储管理
- 定期清理旧的环境快照
- 监控BOS存储使用量
- 设置合适的URL过期时间

### 2. 性能优化
- 较小的工作目录有助于快速打包
- 排除不必要的大文件
- 考虑使用BOS多线程上传（大文件）

### 3. 安全考虑
- 妥善保管BOS访问凭证
- 使用专用的BOS存储桶
- 设置适当的存储桶权限

## 故障排除

### 问题诊断步骤

1. **检查配置**: 确认所有BOS参数正确
2. **验证权限**: 测试BOS客户端连接
3. **检查空间**: 确认BOS存储桶有足够空间
4. **查看日志**: 分析错误日志定位问题
5. **网络检查**: 确认到BOS的网络连通性

### 常用调试命令

```bash
# 查看运行时日志
tail -f /var/log/mcp-runtime.log

# 检查工作目录
ls -la /tmp/mcp-runtime/

# 测试BOS连接
curl -I https://su.bcebos.com
```

## 更新历史

- **v1.0.0**: 初始版本，支持基本环境快照功能
- **v1.1.0**: 添加MD5去重机制
- **v1.2.0**: 集成BOS云存储
- **v1.3.0**: 添加自动文件排除功能 