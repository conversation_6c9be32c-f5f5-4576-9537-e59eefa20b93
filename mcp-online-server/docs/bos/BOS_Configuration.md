# BOS配置说明文档

## 概述

MCP运行时需要BOS（百度对象存储）配置来实现环境快照的自动上传和管理。本文档说明需要在配置文件中添加的BOS相关配置项。

## 配置文件说明

### 配置文件位置

BOS配置文件位于：`conf/business/bos.toml`

### 配置结构

```toml
[DataStorageBos]
AccessKey = "BOS访问密钥"
SecretKey = "BOS秘密密钥"
Endpoint = "BOS服务端点"
Bucket = "BOS存储桶名称"
TempBucket = "临时存储桶（可选）"
Expiration = 3600
ExtendPeriodToken = "续期业务token"

[DataFlowBos]
AccessKey = "数据流BOS访问密钥"
SecretKey = "数据流BOS秘密密钥"
Endpoint = "数据流BOS服务端点"
Bucket = "数据流BOS存储桶名称"
TempBucket = "数据流临时存储桶（可选）"
Expiration = 3600
ExtendPeriodToken = "数据流续期业务token"

StsEndpoint = "STS服务端点"
```

## 配置示例

### 生产环境配置 (`conf/business/bos.toml`)
```toml
[DataStorageBos]
AccessKey = "ALTAKF2gUJak7ycdp3cYaznbL5"
SecretKey = "677dd0b9288f408c907ec8aa33b4532f"
Endpoint = "https://su.bcebos.com"
Bucket = "mcp-environments-prod"
TempBucket = "mcp-temp-prod"
Expiration = 3600
ExtendPeriodToken = "prod-extend-token"

[DataFlowBos]
AccessKey = "ALTAKQlobVvz0gGu7CwZ0CkZN0"
SecretKey = "729878919b664d0c9a8803e6923894a2"
Endpoint = "https://su.bcebos.com"
Bucket = "mcp-dataflow-prod"
TempBucket = "mcp-dataflow-temp-prod"
Expiration = 3600
ExtendPeriodToken = "prod-dataflow-extend-token"

StsEndpoint = "https://sts.baidubce.com"
```

### 开发环境配置 (`conf/business/bos.toml`)
```toml
[DataStorageBos]
AccessKey = "ALTAKQlobVvz0gGu7CwZ0CkZN0"
SecretKey = "729878919b664d0c9a8803e6923894a2"
Endpoint = "https://su.bcebos.com"
Bucket = "mcp-environments-dev"
TempBucket = "mcp-temp-dev"
Expiration = 3600
ExtendPeriodToken = "dev-extend-token"

[DataFlowBos]
AccessKey = "ALTAKQlobVvz0gGu7CwZ0CkZN0"
SecretKey = "729878919b664d0c9a8803e6923894a2"
Endpoint = "https://su.bcebos.com"
Bucket = "mcp-dataflow-dev"
TempBucket = "mcp-dataflow-temp-dev"
Expiration = 3600
ExtendPeriodToken = "dev-dataflow-extend-token"

StsEndpoint = "https://sts.baidubce.com"
```

## 配置验证

### 检查配置完整性

启动时，系统会验证BOS配置文件：

```
FATAL: load bos config failed: no such file or directory
```

如果出现此错误，请检查 `conf/business/bos.toml` 文件是否存在。

```
FATAL: BOS配置未初始化
```

如果出现此错误，请检查 `DataStorageBos` 配置是否正确。

### 测试BOS连接

可以通过以下方式测试BOS配置是否正确：

1. **检查配置文件**
   ```bash
   cat conf/business/bos.toml
   ```

2. **验证存储桶权限**
   - 确保具有读写权限
   - 确保存储桶存在
   - 检查区域设置

## BOS存储桶设置

### 权限配置

BOS存储桶需要以下权限：

- **PutObject**: 上传环境快照
- **GetObject**: 下载环境快照  
- **GetObjectMeta**: 检查对象是否存在（去重）
- **DeleteObject**: 清理过期快照（可选）

### 生命周期策略（推荐）

为节省存储成本，建议设置生命周期策略：

```json
{
  "rules": [
    {
      "id": "mcp-env-cleanup",
      "status": "Enabled",
      "filter": {
        "prefix": "mcp_env/"
      },
      "transitions": [
        {
          "days": 30,
          "storageClass": "COLD"
        },
        {
          "days": 90,
          "storageClass": "ARCHIVE"
        }
      ],
      "expiration": {
        "days": 365
      }
    }
  ]
}
```

## 安全注意事项

### 1. 凭证管理
- ❌ 不要在代码中硬编码BOS凭证
- ✅ 使用配置中心统一管理
- ✅ 定期轮换访问密钥
- ✅ 遵循最小权限原则

### 2. 网络安全
- 使用HTTPS端点
- 配置VPC网络访问（如果可能）
- 监控异常访问模式

### 3. 数据安全
- 考虑启用BOS加密
- 定期备份重要环境
- 监控存储使用情况

## 故障排除

### 常见错误

1. **凭证错误**
   ```
   Failed to create BOS client: SignatureDoesNotMatch
   ```
   解决：检查access_key和secret_key是否正确

2. **存储桶不存在**
   ```
   上传到BOS失败: NoSuchBucket
   ```
   解决：检查bucket名称，确保存储桶存在

3. **权限不足**
   ```
   上传到BOS失败: AccessDenied
   ```
   解决：检查BOS用户权限，确保有读写权限

4. **网络连接问题**
   ```
   上传到BOS失败: ConnectTimeout
   ```
   解决：检查网络连接，确保能访问BOS服务

### 调试步骤

1. **验证配置文件**
   ```bash
   # 检查配置文件是否存在
   ls -la conf/business/bos.toml
   
   # 检查配置文件内容
   cat conf/business/bos.toml
   ```

2. **测试连接**
   ```bash
   # 测试BOS端点连通性
   curl -I https://su.bcebos.com
   ```

3. **查看日志**
   ```bash
   # 查看容器日志
   kubectl logs <pod-name> -f
   
   # 查看应用启动日志
   tail -f logs/service.log
   ```

## 配置部署流程

### 1. 准备BOS资源
1. 创建BOS存储桶
2. 创建IAM用户并分配权限
3. 获取访问凭证

### 2. 更新配置文件
1. 创建或更新 `conf/business/bos.toml` 文件
2. 验证配置语法
3. 检查文件权限

### 3. 验证部署
1. 重启相关服务
2. 检查启动日志
3. 执行测试任务验证功能

## 监控和运维

### 关键指标

- BOS API调用成功率
- 环境快照上传时间
- 存储使用量
- 去重命中率

### 告警规则

- BOS API调用失败率 > 5%
- 环境快照上传时间 > 60秒
- 存储使用量 > 80%
- 连续多次上传失败

## 配置文件管理优势

使用配置文件管理BOS配置具有以下优势：

### ✅ 优势
- **版本控制**：配置文件可以纳入版本控制系统
- **环境隔离**：不同环境使用不同的配置文件
- **启动验证**：应用启动时会验证配置文件完整性
- **统一管理**：所有BOS配置集中在一个文件中
- **类型安全**：配置结构化，减少配置错误

### ⚠️ 注意事项
- 配置文件包含敏感信息，需要妥善保管
- 生产环境配置文件权限应设置为 `600`
- 定期检查配置文件是否与实际BOS资源匹配

### 🔧 最佳实践
1. **环境分离**：为每个环境创建独立的配置文件
2. **权限控制**：限制配置文件的访问权限
3. **定期轮换**：定期更新BOS访问密钥
4. **监控告警**：设置BOS相关的监控和告警
5. **备份配置**：定期备份配置文件

这样的配置管理确保了MCP运行时能够可靠地使用BOS进行环境管理，同时保持安全性和可维护性。 