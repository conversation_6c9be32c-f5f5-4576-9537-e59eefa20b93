# BOS（百度对象存储）API接口文档

## 概述

本文档描述了mcp-online-server项目中集成的BOS功能API接口。BOS功能提供了完整的对象存储服务，包括环境管理和文件操作两大类功能。

## 基础信息

- **基础URL**: `/api/v1/mcp`
- **内容类型**: `application/json`
- **认证方式**: 根据项目配置

## API接口列表

### 1. 环境管理相关接口

#### 1.1 目录打包上传到BOS

**接口路径**: `POST /bos/env/package`

**功能描述**: 将指定目录打包为tar.gz格式，计算MD5值，上传到BOS并保存环境记录。支持MD5去重，如果相同MD5的环境已存在，则直接返回现有记录。

**请求参数**:
```json
{
  "dir_path": "/path/to/directory",           // 必填，要打包的目录路径
  "env_name": "环境名称",                      // 可选，环境名称
  "env_description": "环境描述",               // 可选，环境描述
  "env_dependency": {                         // 可选，环境依赖配置
    "python_version": "3.9",
    "packages": ["requests", "numpy"],
    "system_deps": ["curl", "git"]
  }
}
```

**响应示例**:
```json
{
  "md5": "abc123def456...",                   // 压缩包MD5值
  "bos_url": "https://bos.example.com/...",   // BOS下载链接
  "file_size": 52428800,                      // 文件大小（字节）
  "bos_key": "mcp_env/20241208/20241208143000_abc123def456.tar.gz",
  "env_id": 12345,                            // 环境ID
  "is_existing": false                        // 是否为已存在的环境
}
```

#### 1.2 从BOS下载环境

**接口路径**: `POST /bos/env/download`

**功能描述**: 根据环境MD5值从BOS下载环境压缩包并解压到指定目录。

**请求参数**:
```json
{
  "env_md5": "abc123def456...",               // 必填，环境MD5值
  "dest_dir": "/path/to/extract"              // 必填，目标解压目录
}
```

**响应示例**:
```json
{
  "success": true,                            // 是否成功
  "dest_dir": "/path/to/extract",            // 解压目录
  "env_md5": "abc123def456...",              // 环境MD5
  "message": "环境下载成功"                   // 结果消息
}
```

#### 1.3 检查环境MD5是否存在

**接口路径**: `GET /bos/env/check`

**功能描述**: 检查指定MD5值的环境是否已存在于系统中。

**请求参数**:
```
?md5_hash=abc123def456...                    // URL参数，环境MD5值
```

**响应示例**:
```json
{
  "exists": true,                             // 是否存在
  "md5": "abc123def456...",                  // MD5值
  "env": {                                   // 环境对象（如果存在）
    "env_id": 12345,
    "env_md5": "abc123def456...",
    "name": "测试环境",
    "description": "用于测试的Python环境",
    "bos_url": "https://bos.example.com/...",
    "file_size": 52428800,
    "created_at": "2024-12-08T14:30:00Z"
  }
}
```

#### 1.4 获取环境BOS下载链接

**接口路径**: `GET /bos/env/download-url`

**功能描述**: 根据环境MD5值生成临时的BOS下载链接。

**请求参数**:
```
?env_md5=abc123def456...                     // 必填，环境MD5值
&expire_in_seconds=3600                      // 可选，过期时间（秒），默认3600
```

**响应示例**:
```json
{
  "download_url": "https://bos.example.com/...", // 下载链接
  "env_md5": "abc123def456...",                  // 环境MD5
  "expires_in": 3600                             // 过期时间（秒）
}
```

### 2. 文件操作相关接口

#### 2.1 上传文件到BOS

**接口路径**: `POST /bos/file/upload`

**功能描述**: 将文件内容上传到BOS指定位置。

**请求参数**:
```json
{
  "content": "文件内容字符串",                  // 必填，文件内容
  "bos_key": "path/to/file.txt",             // 必填，BOS对象键
  "bucket": "my-bucket"                       // 可选，存储桶名称
}
```

**响应示例**:
```json
{
  "success": true,                            // 是否成功
  "bos_key": "path/to/file.txt",             // BOS对象键
  "bos_url": "https://bos.example.com/...",   // BOS访问链接
  "file_size": 1024,                         // 文件大小
  "message": "文件上传成功"                   // 结果消息
}
```

#### 2.2 从BOS下载文件

**接口路径**: `POST /bos/file/download`

**功能描述**: 从BOS下载指定文件，可以下载到本地文件或返回文件内容。

**请求参数**:
```json
{
  "bos_key": "path/to/file.txt",             // 必填，BOS对象键
  "bucket": "my-bucket",                      // 可选，存储桶名称
  "file_path": "/local/path/file.txt"         // 可选，本地文件路径
}
```

**响应示例**:
```json
{
  "success": true,                            // 是否成功
  "bos_key": "path/to/file.txt",             // BOS对象键
  "content": "文件内容...",                   // 文件内容（如果没有指定本地路径）
  "file_path": "/local/path/file.txt",       // 本地文件路径（如果指定了路径）
  "file_size": 1024,                         // 文件大小
  "message": "文件下载成功"                   // 结果消息
}
```

## 错误处理

所有接口遵循统一的错误响应格式：

```json
{
  "code": 错误码,
  "message": "错误描述",
  "data": null
}
```

### 常见错误码

- `1001`: 参数验证失败
- `2001`: 文件系统错误
- `3001`: BOS操作失败
- `4001`: 数据库操作失败
- `5001`: 内部服务错误

## 使用示例

### 场景1：环境打包和部署

1. **打包本地环境目录**:
```bash
curl -X POST /api/v1/mcp/bos/env/package \
  -H "Content-Type: application/json" \
  -d '{
    "dir_path": "/home/<USER>/my_env",
    "env_name": "生产环境",
    "env_description": "Python 3.9 + TensorFlow环境"
  }'
```

2. **检查环境是否存在**:
```bash
curl "/api/v1/mcp/bos/env/check?md5_hash=abc123def456..."
```

3. **在其他地方下载环境**:
```bash
curl -X POST /api/v1/mcp/bos/env/download \
  -H "Content-Type: application/json" \
  -d '{
    "env_md5": "abc123def456...",
    "dest_dir": "/tmp/restored_env"
  }'
```

### 场景2：文件传输

1. **上传配置文件**:
```bash
curl -X POST /api/v1/mcp/bos/file/upload \
  -H "Content-Type: application/json" \
  -d '{
    "content": "config_content_here",
    "bos_key": "configs/app.conf"
  }'
```

2. **下载配置文件**:
```bash
curl -X POST /api/v1/mcp/bos/file/download \
  -H "Content-Type: application/json" \
  -d '{
    "bos_key": "configs/app.conf",
    "file_path": "/local/app.conf"
  }'
```

## 架构说明

### 核心特性

1. **MD5去重机制**: 避免重复上传相同内容的环境包
2. **完整的tar.gz支持**: 目录打包和解压功能
3. **数据库集成**: 与现有env表无缝集成
4. **统一错误处理**: 完整的错误码和日志记录
5. **流式处理**: 支持大文件的高效处理
6. **并发安全**: 支持多协程并发操作

### 数据流

```
用户请求 → API控制器 → 服务层 → DAO层 → BOS客户端 → 百度BOS
                     ↓
                 数据库记录 → MySQL数据库
```

### 关键组件

- **API控制器**: `httpserver/controller/api/bos.go`
- **服务层**: `model/service/intern/bos_*.go`
- **DAO层**: `model/dao/rpc_bos/`
- **数据模型**: `model/dao/mcp_env/`

## 配置要求

确保以下配置正确设置：

1. **BOS配置**: `conf/business/bos.toml`
2. **数据库配置**: `conf/servicer/drds_dsm_main.toml`
3. **应用配置**: `conf/app.toml`

## 注意事项

1. **权限控制**: 确保BOS客户端有适当的读写权限
2. **存储配额**: 注意BOS存储空间的使用情况
3. **并发控制**: 大文件上传时注意并发数量控制
4. **错误恢复**: 实现适当的重试机制
5. **日志记录**: 关键操作都有详细的日志记录

## 版本信息

- **当前版本**: v1.0.0
- **兼容性**: 支持GDP框架
- **更新日期**: 2024-12-08 