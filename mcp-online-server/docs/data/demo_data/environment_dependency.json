{"environment_dependency": [{"path": "finance.db", "type": "db", "content": "CREATE TABLE accounts (\n    account_id INTEGER PRIMARY KEY,\n    account_name VARCHAR(50),\n    account_type VARCHAR(20),\n    balance DECIMAL(12,2),\n    currency VARCHAR(3) DEFAULT 'CNY'\n);\n\n\nCREATE TABLE categories (\n    category_id INTEGER PRIMARY KEY,\n    category_name VARCHAR(50),\n    category_type VARCHAR(20), -- income/expense\n    parent_id INTEGER\n);\n\nCREATE TABLE transactions (\n    transaction_id INTEGER PRIMARY KEY,\n    account_id INTEGER,\n    category_id INTEGER,\n    amount DECIMAL(10,2),\n    transaction_date DATE,\n    description TEXT,\n    transaction_type VARCHAR(20),\n    FOREIGN KEY (account_id) REFERENCES accounts(account_id),\n    FOREIGN KEY (category_id) REFERENCES categories(category_id)\n);\n\nCREATE TABLE budgets (\n    budget_id INTEGER PRIMARY KEY,\n    category_id INTEGER,\n    budget_amount DECIMAL(10,2),\n    budget_month VARCHAR(7), -- YYYY-MM\n    spent_amount DECIMAL(10,2) DEFAULT 0,\n    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (category_id) REFERENCES categories(category_id)\n);\n\n-- 1. 账户表数据 (accounts)\nINSERT INTO accounts (account_id, account_name, account_type, balance, currency) VALUES\n(1, '工商银行储蓄卡', 'savings', 45280.50, 'CNY'),\n(2, '支付宝余额', 'digital_wallet', 2380.75, 'CNY'),\n(3, '微信钱包', 'digital_wallet', 890.20, 'CNY'),\n(4, '招商银行信用卡', 'credit_card', -3200.00, 'CNY'),\n(5, '余额宝', 'investment', 18500.00, 'CNY'),\n(6, '现金', 'cash', 650.00, 'CNY'),\n(7, '建设银行储蓄卡', 'savings', 12800.30, 'CNY'),\n(8, '花呗', 'credit', -850.00, 'CNY'),\n(9, '京东白条', 'credit', -1200.00, 'CNY'),\n(10, '平安银行信用卡', 'credit_card', -2100.50, 'CNY'),\n(11, '天弘基金', 'investment', 25000.00, 'CNY'),\n(12, '零钱通', 'investment', 3500.80, 'CNY'),\n(13, '公积金账户', 'savings', 58000.00, 'CNY'),\n(14, '定期存款', 'savings', 100000.00, 'CNY'),\n(15, '股票账户', 'investment', 15600.25, 'CNY'),\n(16, '美团月付', 'credit', -300.00, 'CNY'),\n(17, '交通银行储蓄卡', 'savings', 8900.15, 'CNY'),\n(18, '理财通', 'investment', 12000.00, 'CNY'),\n(19, '农业银行储蓄卡', 'savings', 6780.90, 'CNY'),\n(20, 'PayPal', 'digital_wallet', 125.30, 'USD');\n\n-- 2. 分类表数据 (categories)\nINSERT INTO categories (category_id, category_name, category_type, parent_id) VALUES\n-- 收入类别\n(1, '工资收入', 'income', NULL),\n(2, '投资收益', 'income', NULL),\n(3, '兼职收入', 'income', NULL),\n(4, '奖金', 'income', 1),\n(5, '基本工资', 'income', 1),\n(6, '股票分红', 'income', 2),\n(7, '基金收益', 'income', 2),\n-- 支出类别\n(8, '餐饮', 'expense', NULL),\n(9, '交通', 'expense', NULL),\n(10, '住房', 'expense', NULL),\n(11, '购物', 'expense', NULL),\n(12, '娱乐', 'expense', NULL),\n(13, '医疗', 'expense', NULL),\n(14, '教育', 'expense', NULL),\n(15, '早餐', 'expense', 8),\n(16, '午餐', 'expense', 8),\n(17, '晚餐', 'expense', 8),\n(18, '地铁公交', 'expense', 9),\n(19, '打车', 'expense', 9),\n(20, '房租', 'expense', 10);\n\n-- 3. 交易记录表数据 (transactions) - 涵盖2024年11月到2025年5月\nINSERT INTO transactions (transaction_id, account_id, category_id, amount, transaction_date, description, transaction_type) VALUES\n-- 收入记录\n(1, 1, 5, 8500.00, '2024-11-25', '11月基本工资', 'income'),\n(2, 1, 4, 2000.00, '2024-11-30', '11月绩效奖金', 'income'),\n(3, 5, 7, 185.50, '2024-12-01', '余额宝收益', 'income'),\n(4, 1, 5, 8500.00, '2024-12-25', '12月基本工资', 'income'),\n(5, 1, 3, 1200.00, '2024-12-15', '周末兼职收入', 'income'),\n(6, 15, 6, 320.00, '2024-12-20', '股票分红', 'income'),\n(7, 1, 5, 8500.00, '2025-01-25', '1月基本工资', 'income'),\n(8, 1, 4, 3000.00, '2025-01-31', '年终奖金', 'income'),\n(9, 11, 7, 450.80, '2025-02-01', '基金收益', 'income'),\n(10, 1, 5, 8500.00, '2025-02-25', '2月基本工资', 'income'),\n\n-- 餐饮支出\n(11, 2, 15, -12.50, '2025-05-01', '肯德基早餐', 'expense'),\n(12, 3, 16, -28.00, '2025-05-01', '公司楼下午餐', 'expense'),\n(13, 2, 17, -45.80, '2025-05-01', '海底捞晚餐', 'expense'),\n(14, 2, 15, -15.00, '2025-05-02', '豆浆油条', 'expense'),\n(15, 4, 16, -32.00, '2025-05-02', '麦当劳午餐', 'expense'),\n(16, 2, 17, -68.50, '2025-05-02', '西餐厅约会', 'expense'),\n(17, 3, 15, -8.50, '2025-05-03', '煎饼果子', 'expense'),\n(18, 2, 16, -25.00, '2025-05-03', '沙县小吃', 'expense'),\n(19, 4, 17, -89.00, '2025-05-03', '日料晚餐', 'expense'),\n(20, 2, 15, -18.80, '2025-05-04', '星巴克早餐', 'expense'),\n\n-- 交通支出\n(21, 3, 18, -4.00, '2025-05-01', '地铁上班', 'expense'),\n(22, 2, 19, -15.30, '2025-05-01', '滴滴打车', 'expense'),\n(23, 3, 18, -6.00, '2025-05-02', '公交转地铁', 'expense'),\n(24, 8, 19, -22.50, '2025-05-02', '下雨打车', 'expense'),\n(25, 3, 18, -4.00, '2025-05-03', '地铁通勤', 'expense'),\n\n-- 住房支出\n(26, 1, 20, -3200.00, '2025-05-01', '5月房租', 'expense'),\n(27, 2, 10, -150.00, '2025-05-05', '水电费', 'expense'),\n(28, 3, 10, -80.00, '2025-05-08', '网费', 'expense'),\n\n-- 购物支出\n(29, 4, 11, -299.00, '2025-05-02', '淘宝购买衣服', 'expense'),\n(30, 2, 11, -89.90, '2025-05-03', '超市购物', 'expense'),\n(31, 9, 11, -158.00, '2025-05-04', '京东数码配件', 'expense'),\n(32, 2, 11, -45.50, '2025-05-05', '便利店日用品', 'expense'),\n\n-- 娱乐支出\n(33, 2, 12, -58.00, '2025-05-04', '电影票', 'expense'),\n(34, 3, 12, -35.00, '2025-05-05', 'KTV', 'expense'),\n(35, 4, 12, -120.00, '2025-05-06', '健身房月卡', 'expense'),\n\n-- 医疗支出\n(36, 1, 13, -85.50, '2025-05-07', '看病挂号费', 'expense'),\n(37, 2, 13, -45.80, '2025-05-07', '买药', 'expense'),\n\n-- 教育支出\n(38, 1, 14, -199.00, '2025-05-08', '在线课程', 'expense'),\n(39, 2, 14, -68.00, '2025-05-09', '技术书籍', 'expense'),\n\n-- 历史月份的交易记录\n(40, 2, 15, -280.50, '2025-04-01', '4月餐饮支出汇总', 'expense'),\n(41, 3, 16, -420.80, '2025-04-15', '4月午餐支出', 'expense'),\n(42, 1, 20, -3200.00, '2025-04-01', '4月房租', 'expense'),\n(43, 2, 18, -98.00, '2025-04-30', '4月交通费', 'expense'),\n(44, 4, 11, -680.90, '2025-04-20', '4月购物支出', 'expense'),\n(45, 2, 15, -320.75, '2025-03-15', '3月餐饮支出', 'expense'),\n(46, 1, 20, -3200.00, '2025-03-01', '3月房租', 'expense'),\n(47, 3, 9, -150.60, '2025-03-20', '3月交通支出', 'expense'),\n(48, 1, 5, 8500.00, '2025-03-25', '3月基本工资', 'income'),\n(49, 1, 5, 8500.00, '2025-04-25', '4月基本工资', 'income'),\n(50, 1, 5, 8500.00, '2025-05-25', '5月基本工资', 'income');\n\n-- 4. 预算表数据 (budgets) - 设定各月预算\nINSERT INTO budgets (budget_id, category_id, budget_amount, budget_month, spent_amount) VALUES\n-- 2025年3月预算\n(1, 8, 800.00, '2025-03', 320.75),\n(2, 9, 200.00, '2025-03', 150.60),\n(3, 10, 3500.00, '2025-03', 3200.00),\n(4, 11, 500.00, '2025-03', 380.20),\n(5, 12, 300.00, '2025-03', 180.50),\n\n-- 2025年4月预算  \n(6, 8, 800.00, '2025-04', 701.30),\n(7, 9, 200.00, '2025-04', 98.00),\n(8, 10, 3500.00, '2025-04', 3200.00),\n(9, 11, 500.00, '2025-04', 680.90),\n(10, 12, 300.00, '2025-04', 250.00),\n\n-- 2025年5月预算\n(11, 8, 850.00, '2025-05', 341.10),\n(12, 9, 220.00, '2025-05', 51.80),\n(13, 10, 3500.00, '2025-05', 3430.00),\n(14, 11, 600.00, '2025-05', 592.40),\n(15, 12, 350.00, '2025-05', 213.00),\n(16, 13, 200.00, '2025-05', 131.30),\n(17, 14, 300.00, '2025-05', 267.00),\n\n-- 2025年6月预算（未来月份）\n(18, 8, 900.00, '2025-06', 0.00),\n(19, 9, 250.00, '2025-06', 0.00),\n(20, 10, 3500.00, '2025-06', 0.00);"}]}