# MCP 压力测试工具使用指南

## 快速开始

### 1. 使用 Makefile（推荐）

```bash
# 查看所有可用命令
make stress-test-help

# 运行预定义的测试场景
make stress-test-small         # 小负载测试 (5个会话)
make stress-test-default       # 默认测试 (10个会话)
make stress-test-high          # 高负载测试 (100个会话)
make stress-test-db            # 数据库工具测试

# 自定义参数
make stress-test-custom ARGS='-batch-size 50 -server-ids 1,2,3'
make stress-test-custom ARGS='-api-prefix http://localhost:8080/api/v1 -batch-size 25'

# 编译独立的二进制文件
make stress-test-build

# 清理日志文件
make stress-test-clean
```

### 2. 直接使用 go run

```bash
# 使用默认配置运行测试
go run benchmark/cmd/main.go

# 使用预定义的配置文件
go run benchmark/cmd/main.go -config benchmark/config/default.yaml
go run benchmark/cmd/main.go -config benchmark/config/example.yaml
go run benchmark/cmd/main.go -config benchmark/config/small_load.yaml
go run benchmark/cmd/main.go -config benchmark/config/database_test.yaml

# 命令行参数覆盖
go run benchmark/cmd/main.go -api-prefix http://localhost:8080/api/v1 -batch-size 50
go run benchmark/cmd/main.go -server-ids 1,2,3 -env-id 1
go run benchmark/cmd/main.go -tool-name "filesystem__list_directory" -tool-input '{"path":"/tmp"}'
```

## 配置说明

### 基本配置

- `api_prefix`: MCP服务API地址前缀
- `timeout`: HTTP请求超时时间（秒）
- `server_ids`: 要测试的MCP服务器ID列表
- `env_id`: 环境ID

### 会话创建配置

- `batch_size`: 并行创建的会话数量
- `timeout_minutes`: 会话超时时间（分钟）
- `session_creation.max_retries`: 最大重试次数
- `session_creation.retry_interval`: 重试间隔

### 工具调用配置

- `tool_call.tool_name`: 要调用的工具名称
- `tool_call.tool_input`: 工具输入参数（JSON对象）
- `tool_call.timeout_seconds`: 工具调用超时时间（秒）
- `tool_call.max_retries`: 最大重试次数
- `tool_call.retry_interval`: 重试间隔

### 报告配置

- `report.output_dir`: 报告输出目录
- `report.detailed_logs`: 是否生成详细日志
- `report.include_errors`: 是否包含错误详情

### 并发控制

- `concurrency.max_goroutines`: 最大并发goroutine数
- `concurrency.rate_limit`: 请求速率限制

## 测试场景

### 1. 小负载测试

适用于开发环境或初步验证：

```bash
make stress-test-small
# 或者
go run benchmark/cmd/main.go -config benchmark/config/small_load.yaml
```

特点：
- 5个并行会话
- 保守的超时设置
- 较低的并发数

### 2. 中等负载测试

适用于测试环境：

```bash
make stress-test-default
# 或者
go run benchmark/cmd/main.go -config benchmark/config/default.yaml
```

特点：
- 10个并行会话
- 标准的超时设置
- 中等并发数

### 3. 高负载测试

适用于性能测试：

```bash
make stress-test-high
# 或者
go run benchmark/cmd/main.go -config benchmark/config/example.yaml
```

特点：
- 100个并行会话
- 较长的超时设置
- 高并发数

### 4. 数据库工具测试

专门测试数据库相关工具：

```bash
make stress-test-db
# 或者
go run benchmark/cmd/main.go -config benchmark/config/database_test.yaml
```

特点：
- 针对数据库服务器
- 数据库查询工具
- 适合数据库操作的超时设置

## 报告解读

### 会话创建指标

- `total_attempts`: 总尝试次数
- `success_count`: 成功次数
- `failure_count`: 失败次数
- `success_rate`: 成功率（百分比）
- `average_response_time`: 平均响应时间
- `throughput`: 吞吐量（会话/秒）

### 工具调用指标

- `total_attempts`: 总尝试次数
- `success_count`: 成功次数
- `failure_count`: 失败次数
- `success_rate`: 成功率（百分比）
- `average_response_time`: 平均响应时间
- `throughput`: 吞吐量（调用/秒）

### 响应时间分析

- `min_response_time`: 最小响应时间
- `max_response_time`: 最大响应时间
- `p50_response_time`: 50%分位数响应时间
- `p95_response_time`: 95%分位数响应时间
- `p99_response_time`: 99%分位数响应时间

## 故障排除

### 常见问题

1. **连接失败**
   - 检查API地址是否正确
   - 确认MCP服务是否运行
   - 检查网络连接

2. **会话创建失败**
   - 检查服务器ID是否存在
   - 确认环境ID是否有效
   - 检查服务器资源是否充足

3. **工具调用失败**
   - 确认工具名称是否正确
   - 检查工具输入参数格式
   - 验证会话是否处于就绪状态

### 调试技巧

1. 使用 `-verbose` 参数获取详细输出
2. 检查生成的报告文件中的错误详情
3. 从小负载开始逐步增加测试强度
4. 监控服务器资源使用情况

## 性能调优建议

### 客户端调优

1. 调整 `batch_size` 以匹配服务器能力
2. 设置合适的 `timeout` 值
3. 控制 `max_goroutines` 避免过度并发
4. 使用 `rate_limit` 控制请求频率

### 服务器调优

1. 监控CPU和内存使用率
2. 检查数据库连接池设置
3. 优化Kubernetes资源配置
4. 调整会话超时设置

## 扩展使用

### 自定义配置

创建自己的配置文件：

```yaml
# my_test.yaml
api_prefix: "http://my-server:8080/api/v1"
server_ids: [1, 2]
env_id: 1
batch_size: 25
tool_call:
  tool_name: "my_custom_tool"
  tool_input:
    param1: "value1"
    param2: 123
```

### 集成到CI/CD

```bash
#!/bin/bash
# 性能回归测试脚本
go run cmd/stress_test.go -config config/ci_test.yaml
if [ $? -ne 0 ]; then
    echo "性能测试失败"
    exit 1
fi
```
