# MCP 初始化功能使用指南

## 概述

MCP 压力测试工具现在支持自动初始化 MCP 服务器和环境，让您可以从零开始创建完整的测试环境。

## 功能特性

### 1. MCP 服务器初始化
- 自动注册新的 MCP 服务器
- 支持本地（local）、远程（remote）和 SSE 类型服务器
- 自动更新配置中的 server_ids

### 2. 环境初始化
- 创建文件系统结构（目录和文件）
- 初始化数据库（SQLite、MySQL 等）
- 支持任意类型的环境依赖
- 自动更新配置中的 env_id

### 3. 工具调用轮询
- **并发执行**：所有会话同时开始工具调用
- 支持重复调用同一工具（在每个会话内）
- 可配置调用次数和间隔时间
- 适用于性能测试和稳定性测试

## 配置格式

### 服务器配置 (server_conf)

```yaml
server_conf:
  server_name: "sqlite"                    # 服务器名称
  command: "uvx"                          # 启动命令
  args: ["--offline", "mcp-server-sqlite==2025.4.25", "--db-path", "./test.db"]
  description: "sqlite@2025.4.25，官方sqliteMCP服务"
  type: "local"                           # 服务器类型：local/remote/sse
  url: ""                                 # 远程服务器URL（可选）
  headers:                                # 请求头（可选）
    Authorization: "Bearer token"
  mock_mcp_server_code_url: ""           # 模拟服务器代码URL（可选）
```

### 环境配置 (env_conf)

```yaml
env_conf:
  name: "test-environment"
  description: "测试环境描述"
  environment_dependency:
    - path: "./data/"                     # 创建目录
      type: "directory"
      content: ""
    
    - path: "./data/config.json"          # 创建文件
      type: "file"
      content: |
        {
          "setting1": "value1",
          "setting2": "value2"
        }
    
    - path: "./test.db"                   # 创建数据库
      type: "db"
      content: |
        CREATE TABLE users (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL
        );
        INSERT INTO users (name) VALUES ('Alice'), ('Bob');
```

### 工具调用轮询配置

```yaml
tool_call:
  tool_name: "sqlite__query"
  tool_input:
    query: "SELECT COUNT(*) FROM users"
  
  # 轮询配置
  polling_enabled: true                   # 启用轮询
  polling_count: 5                        # 调用5次
  polling_interval: 2s                    # 每次间隔2秒
```

## 使用示例

### 1. 服务器初始化测试

```bash
# 使用预配置的服务器初始化测试
make stress-test-server

# 或者直接运行
go run benchmark/cmd/main.go -config benchmark/config/with_server_init.yaml
```

配置文件示例：`benchmark/config/with_server_init.yaml`

### 2. 环境初始化测试

```bash
# 使用预配置的环境初始化测试
make stress-test-env

# 或者直接运行
go run benchmark/cmd/main.go -config benchmark/config/with_env_init.yaml
```

配置文件示例：`benchmark/config/with_env_init.yaml`

### 3. 完整初始化测试

```bash
# 同时初始化服务器和环境
make stress-test-full

# 或者直接运行
go run benchmark/cmd/main.go -config benchmark/config/full_init.yaml
```

配置文件示例：`benchmark/config/full_init.yaml`

## 工作流程

1. **初始化阶段**（如果配置了）：
   - 注册 MCP 服务器（server_conf）
   - 创建测试环境（env_conf）
   - 自动更新配置中的 server_ids 和 env_id

2. **会话创建阶段**：
   - 使用初始化后的服务器和环境创建会话

3. **工具调用阶段**：
   - 在所有成功会话上调用工具
   - 支持轮询调用（如果启用）

## 配置验证

工具会自动验证配置的有效性：

- 检查必需字段是否存在
- 验证服务器类型是否有效
- 确保环境依赖格式正确
- 验证轮询配置的合理性

## 错误处理

- **服务器初始化失败**：会显示详细错误信息，测试停止
- **环境初始化失败**：会显示失败的依赖项，测试停止
- **配置冲突**：如果同时配置了初始化和固定ID，会优先使用初始化

## 最佳实践

1. **开发阶段**：使用小批量测试验证配置
   ```bash
   make stress-test-custom ARGS='-config benchmark/config/full_init.yaml -batch-size 2'
   ```

2. **测试阶段**：使用完整配置进行压力测试
   ```bash
   make stress-test-full
   ```

3. **生产验证**：使用轮询测试验证稳定性
   ```yaml
   tool_call:
     polling_enabled: true
     polling_count: 10
     polling_interval: 5s
   ```

## 故障排除

### 常见问题

1. **服务器注册失败**
   - 检查服务器名称是否唯一
   - 验证命令和参数是否正确
   - 确认服务器类型配置

2. **环境创建失败**
   - 检查路径格式是否正确
   - 验证文件内容格式
   - 确认数据库SQL语法

3. **轮询调用异常**
   - 检查工具名称是否正确
   - 验证输入参数格式
   - 确认间隔时间设置合理

### 调试技巧

1. 使用 `-verbose` 参数获取详细日志
2. 从小批量开始测试
3. 检查生成的报告文件中的错误详情
4. 验证API端点是否可访问

## 扩展配置

您可以创建自己的配置文件，结合初始化功能：

```yaml
# my_test.yaml
api_prefix: "http://my-server:8080/api/v1"

server_conf:
  server_name: "my_custom_server"
  command: "python"
  args: ["my_server.py"]
  description: "我的自定义MCP服务器"

env_conf:
  name: "my_test_env"
  description: "我的测试环境"
  environment_dependency:
    - path: "./my_data/"
      type: "directory"

batch_size: 15
tool_call:
  tool_name: "my_tool"
  tool_input:
    param1: "value1"
  polling_enabled: true
  polling_count: 3
  polling_interval: 1s
```

然后运行：
```bash
go run benchmark/cmd/main.go -config my_test.yaml -verbose
```
