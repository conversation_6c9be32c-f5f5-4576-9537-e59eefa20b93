# MCP 并发工具调用指南

## 概述

MCP 压力测试工具现在支持**并发工具调用**，允许在所有会话上同时执行工具调用，大幅提高测试效率和真实性。

## 并发模型

### 1. 会话创建阶段
- 并行创建多个会话（由 `batch_size` 控制）
- 使用 goroutine 池实现并发创建
- 速率限制控制会话创建频率

### 2. 工具调用阶段
- **并发执行**：所有会话同时开始工具调用
- 每个会话在独立的 goroutine 中执行
- 使用信号量控制最大并发数（由 `concurrency.max_goroutines` 控制）
- 支持会话内轮询调用（由 `polling_enabled`、`polling_count` 和 `polling_interval` 控制）

## 配置参数

### 并发控制参数

```yaml
concurrency:
  max_goroutines: 100   # 最大并发goroutine数
  rate_limit: 50ms      # 请求速率限制
```

- `max_goroutines`：控制最大并发数，防止系统过载
- `rate_limit`：控制启动新 goroutine 的频率，避免突发流量

### 轮询配置参数

```yaml
tool_call:
  # 轮询配置
  polling_enabled: true    # 启用轮询
  polling_count: 5         # 每个会话调用5次
  polling_interval: 1s     # 每次调用间隔1秒
```

- `polling_enabled`：是否启用轮询调用
- `polling_count`：每个会话内重复调用次数
- `polling_interval`：每个会话内调用间隔

## 使用示例

### 1. 基本并发测试

```bash
# 使用预配置的并发测试
make stress-test-concurrent

# 或者直接运行
go run benchmark/cmd/main.go -config benchmark/config/concurrent_test.yaml
```

### 2. 自定义并发测试

```bash
# 自定义批次大小和并发数
make stress-test-custom ARGS='-config benchmark/config/concurrent_test.yaml -batch-size 20'

# 调整轮询参数
# 需要创建自定义配置文件
```

## 并发模式说明

### 1. 会话并发 + 工具轮询

这是默认模式，适合大多数测试场景：

- 并行创建多个会话
- 所有会话同时开始工具调用
- 每个会话内串行执行多次工具调用（如果启用轮询）

```
Session 1: Tool Call → Wait → Tool Call → Wait → Tool Call
Session 2: Tool Call → Wait → Tool Call → Wait → Tool Call
Session 3: Tool Call → Wait → Tool Call → Wait → Tool Call
(同时执行)
```

### 2. 高并发压力测试

通过增加 `batch_size` 和 `max_goroutines`，可以进行高并发压力测试：

```yaml
batch_size: 100
concurrency:
  max_goroutines: 200
  rate_limit: 10ms
```

### 3. 长时间稳定性测试

通过增加 `polling_count` 和调整 `polling_interval`，可以进行长时间稳定性测试：

```yaml
tool_call:
  polling_enabled: true
  polling_count: 100
  polling_interval: 5s
```

## 性能考虑

### 1. 资源限制

- **CPU 限制**：高并发会增加 CPU 使用率，注意监控
- **内存使用**：大量 goroutine 会增加内存使用，特别是大批量会话
- **网络带宽**：并发请求会增加网络带宽使用

### 2. 调优建议

- 从小批量开始测试，逐步增加并发数
- 监控系统资源使用情况，避免过载
- 调整 `max_goroutines` 和 `rate_limit` 以获得最佳性能
- 考虑测试环境的硬件限制

## 日志解读

并发模式下的日志格式：

```
开始在64个会话上并发调用工具: tool_name=sqlite__read_query
将在64个成功会话上并发调用工具
开始并发调用工具: session_id=1883
开始轮询调用工具: session_id=1883, 轮询次数=30, 间隔=2s
轮询调用 [1/30]: session_id=1883
调用工具 [tool_call_0_b7dd3fe8]: session_id=1883, 开始
开始并发调用工具: session_id=1884
开始轮询调用工具: session_id=1884, 轮询次数=30, 间隔=2s
轮询调用 [1/30]: session_id=1884
调用工具 [tool_call_1_66a8443f]: session_id=1884, 开始
...
```

- 注意多个会话的日志会交错显示，这是正常的并发行为
- 每个会话都有唯一的 session_id 和 tool_call_id

## 故障排除

### 常见问题

1. **并发数过高导致系统过载**
   - 减少 `batch_size` 或 `max_goroutines`
   - 增加 `rate_limit` 以降低启动频率

2. **网络错误增多**
   - 检查网络连接和服务器负载
   - 增加 `timeout` 值
   - 增加 `max_retries` 和调整 `retry_interval`

3. **内存使用过高**
   - 减少 `batch_size` 和 `max_goroutines`
   - 检查是否有内存泄漏

## 最佳实践

1. **渐进式测试**：从小批量开始，逐步增加并发数
2. **监控资源**：关注 CPU、内存和网络使用情况
3. **合理配置**：根据测试目标调整并发参数
4. **分析结果**：关注成功率、响应时间和吞吐量
5. **长时间测试**：使用轮询功能进行长时间稳定性测试

## 示例配置

### 高并发短时测试

```yaml
batch_size: 100
concurrency:
  max_goroutines: 200
  rate_limit: 10ms
tool_call:
  polling_enabled: true
  polling_count: 3
  polling_interval: 1s
```

### 中等并发长时测试

```yaml
batch_size: 20
concurrency:
  max_goroutines: 50
  rate_limit: 100ms
tool_call:
  polling_enabled: true
  polling_count: 50
  polling_interval: 5s
```

### 低并发稳定性测试

```yaml
batch_size: 5
concurrency:
  max_goroutines: 10
  rate_limit: 500ms
tool_call:
  polling_enabled: true
  polling_count: 100
  polling_interval: 10s
```
