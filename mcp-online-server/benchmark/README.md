# MCP Session Management and Tool Calling Stress Testing Tool

This directory contains a comprehensive stress testing tool for MCP session management and tool calling functionality.

## Overview

The stress testing tool performs the following operations:

1. **Parallel Session Creation**: Creates a configurable batch of sessions in parallel
2. **Serial Tool Calling**: Performs tool calls across all created sessions serially
3. **Performance Metrics**: Tracks success rates, response times, and error details
4. **Detailed Reporting**: Generates timestamped log files with comprehensive results

## Directory Structure

```
benchmark/
├── README.md                 # This documentation
├── USAGE.md                  # Detailed usage guide
├── config/                   # Configuration files
│   ├── default.yaml         # Default configuration
│   ├── example.yaml         # High load example
│   ├── small_load.yaml      # Small load testing
│   └── database_test.yaml   # Database tool testing
├── cmd/                      # Command-line application
│   └── main.go              # Main CLI application
├── internal/                 # Internal packages
│   ├── config/              # Configuration management
│   ├── session/             # Session creation stress test
│   ├── toolcall/            # Tool calling stress test
│   ├── metrics/             # Performance metrics collection
│   └── report/              # Report generation
└── logs/                     # Generated log files (created at runtime)
```

## Quick Start

```bash
# 使用项目根目录的 Makefile（推荐）
make stress-test-help          # 查看所有可用命令
make stress-test-small         # 运行小负载测试 (5个会话)
make stress-test-default       # 运行默认测试 (10个会话)
make stress-test-high          # 运行高负载测试 (100个会话)
make stress-test-db            # 运行数据库工具测试

# 新增：初始化功能测试
make stress-test-server        # 运行服务器初始化测试
make stress-test-env           # 运行环境初始化测试
make stress-test-full          # 运行完整初始化测试 (服务器+环境)

# 新增：会话停止功能测试
make stress-test-stop          # 运行包含会话停止的测试

# 自定义参数
make stress-test-custom ARGS='-batch-size 25 -server-ids 1,2'

# 或者直接使用 go run
go run benchmark/cmd/main.go -config benchmark/config/default.yaml
go run benchmark/cmd/main.go -api-prefix http://localhost:8080/api/v1 -batch-size 25
```

## Key Features

### Initialization Support
- **MCP Server Registration**: Automatic registration and configuration of MCP servers
- **Environment Setup**: Create and configure test environments (file systems, databases, etc.)
- **JSON Configuration in YAML**: Direct JSON configuration embedding in YAML files
- **Automatic ID Management**: Auto-populate server_ids and env_id after initialization

### Configuration Management
- YAML-based configuration files
- Command-line parameter overrides
- Multiple predefined test scenarios
- Flexible tool input parameter support

### Session Creation Testing
- Parallel session creation with configurable batch sizes
- Retry logic with configurable attempts and intervals
- Comprehensive error tracking and categorization
- Response time percentile analysis (P50, P95, P99)

### Session Lifecycle Management
- **Automatic Session Stop**: Configurable session termination after tool calls
- Concurrent session cleanup with retry mechanisms
- Custom stop messages for different test scenarios
- Resource cleanup and lifecycle completion testing

### Tool Calling Testing
- **Concurrent tool execution** across all successful sessions
- Support for any MCP tool with custom parameters
- **Polling Support**: Configurable repeated tool calls with intervals within each session
- Timeout and retry configuration
- Status tracking and result analysis

### Performance Metrics
- Success/failure rates for both sessions and tool calls
- Response time statistics (min, max, average, percentiles)
- Throughput measurements (operations per second)
- Error categorization and frequency analysis
- Concurrent load handling assessment

### Reporting
- JSON format detailed reports with timestamps
- Console summary output
- Configurable error detail inclusion
- Historical performance tracking

## Configuration Parameters

### API Configuration
- `api_prefix`: API endpoint prefix (e.g., "http://localhost:8080/api/v1")
- `timeout`: Request timeout duration in seconds

### Test Target Configuration
- `server_ids`: Array of MCP server IDs to test against
- `env_id`: Environment ID for test sessions

### Session Creation
- `batch_size`: Number of sessions to create in parallel
- `timeout_minutes`: Session timeout in minutes
- `session_creation.max_retries`: Maximum retry attempts
- `session_creation.retry_interval`: Retry interval duration

### Tool Calling
- `tool_name`: Name of the tool to call
- `tool_input`: JSON object containing tool input parameters
- `tool_call.timeout_seconds`: Tool call timeout in seconds
- `tool_call.max_retries`: Maximum retry attempts

### Concurrency Control
- `concurrency.max_goroutines`: Maximum concurrent goroutines
- `concurrency.rate_limit`: Request rate limiting

## Test Scenarios

1. **Small Load** (`small_load.yaml`): 5 sessions, conservative settings
2. **Default Load** (`default.yaml`): 10 sessions, standard settings
3. **High Load** (`example.yaml`): 100 sessions, aggressive settings
4. **Database Test** (`database_test.yaml`): Database-specific tool testing
5. **Session Stop Test** (`with_session_stop.yaml`): Complete lifecycle with session cleanup
6. **Concurrent Test** (`concurrent_test.yaml`): High concurrency tool calling

## Output and Reporting

The tool generates comprehensive performance reports including:

### Session Creation Metrics
- Total attempts, success/failure counts and rates
- Response time statistics (min, max, average, P50/P95/P99)
- Throughput (sessions per second)
- Error categorization and frequency

### Tool Calling Metrics
- Total attempts, success/failure counts and rates
- Response time statistics and percentiles
- Throughput (calls per second)
- Tool execution status tracking
- Error analysis

### Overall Summary
- Combined success rates across all operations
- Total test duration and average throughput
- Resource utilization insights

Results are saved to timestamped JSON files in the configured output directory.

## Getting Started

### 安装和编译

```bash
# 编译压力测试工具
make stress-test-build

# 或者直接运行（无需编译）
go run benchmark/cmd/main.go -help
```

### 快速测试

```bash
# 查看所有可用命令
make stress-test-help

# 运行小负载测试（推荐首次使用）
make stress-test-small

# 运行默认测试
make stress-test-default
```

### 详细文档

See [USAGE.md](USAGE.md) for detailed usage instructions, configuration examples, and troubleshooting tips.

## 项目集成

该压力测试工具已完全集成到现有项目中：

- ✅ **复用现有 go.mod**：无需独立的模块管理
- ✅ **集成到主 Makefile**：提供统一的构建和运行命令
- ✅ **遵循项目结构**：放置在独立的 `benchmark/` 目录中
- ✅ **使用项目依赖**：复用现有的第三方库
- ✅ **统一的构建流程**：与主项目使用相同的构建工具链
