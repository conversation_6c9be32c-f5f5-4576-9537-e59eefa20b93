# MCP 会话停止功能指南

## 概述

MCP 压力测试工具现在支持在工具调用完成后自动停止会话，这有助于：

1. **资源清理**：及时释放测试会话占用的系统资源
2. **完整测试流程**：模拟真实场景中的会话生命周期
3. **性能评估**：测试会话停止操作的性能和可靠性
4. **自动化管理**：减少手动清理会话的工作量

## 功能特性

### 1. 可配置启用/禁用
- 通过 `session_stop.enabled` 控制是否启用会话停止功能
- 默认禁用，避免意外停止重要会话

### 2. 并发停止
- 所有成功的会话并发执行停止操作
- 使用 goroutine 池控制并发数
- 支持速率限制和重试机制

### 3. 自定义停止消息
- 可配置停止消息，便于日志追踪
- 支持不同测试场景的个性化消息

### 4. 错误处理和重试
- 支持配置重试次数和间隔
- 停止失败不会影响测试报告生成
- 详细的错误日志记录

## 配置参数

### 基本配置

```yaml
session_stop:
  enabled: true                         # 是否启用会话停止
  message: "压力测试完成，自动停止会话"    # 停止消息
  max_retries: 3                        # 最大重试次数
  retry_interval: 2s                    # 重试间隔
```

### 参数说明

- **`enabled`**: 控制是否启用会话停止功能
  - `true`: 启用，工具调用完成后自动停止会话
  - `false`: 禁用，保持会话运行状态

- **`message`**: 停止会话时发送的消息
  - 用于标识停止原因
  - 会记录在会话日志中

- **`max_retries`**: 停止操作失败时的最大重试次数
  - 建议设置为 2-5 次
  - 过多重试可能延长测试时间

- **`retry_interval`**: 重试间隔时间
  - 建议设置为 1-5 秒
  - 给系统足够时间处理前一次请求

## 使用示例

### 1. 基本使用

```bash
# 使用预配置的会话停止测试
make stress-test-stop

# 或者直接运行
go run benchmark/cmd/main.go -config benchmark/config/with_session_stop.yaml
```

### 2. 自定义配置

创建自定义配置文件：

```yaml
# my_test_with_stop.yaml
api_prefix: "http://localhost:8080/api/v1"
server_ids: [1]
env_id: 1
batch_size: 10

session_stop:
  enabled: true
  message: "自定义测试完成，清理会话"
  max_retries: 5
  retry_interval: 3s

tool_call:
  tool_name: "filesystem__read_file"
  tool_input:
    path: "/tmp/test.txt"
  polling_enabled: true
  polling_count: 3
  polling_interval: 2s
```

然后运行：
```bash
go run benchmark/cmd/main.go -config my_test_with_stop.yaml -verbose
```

### 3. 在现有配置中启用

在任何现有配置文件中添加：

```yaml
session_stop:
  enabled: true
  message: "测试完成，自动停止会话"
  max_retries: 3
  retry_interval: 2s
```

## 执行流程

启用会话停止功能后，测试流程变为：

1. **第一阶段**：并行创建会话
2. **第二阶段**：并发调用工具（支持轮询）
3. **第三阶段**：并发停止会话 ⭐ **新增**
4. **第四阶段**：生成测试报告

## 日志输出

启用会话停止功能后，您会看到类似的日志：

```
第三阶段：停止会话
开始并发停止5个会话
开始停止会话: session_id=1883
开始停止会话: session_id=1884
开始停止会话: session_id=1885
停止会话 [0]: session_id=1883, 开始
停止会话 [1]: session_id=1884, 开始
停止会话 [2]: session_id=1885, 开始
停止会话 [0]: 成功, session_id=1883, status=stopped, 耗时=1.234s
停止会话 [1]: 成功, session_id=1884, status=stopped, 耗时=1.456s
停止会话 [2]: 成功, session_id=1885, status=stopped, 耗时=1.678s
会话停止完成: 总数=5, 成功=5, 失败=0
会话停止结果: 总数=5, 成功=5, 失败=0
```

## 性能考虑

### 1. 并发控制

会话停止操作受以下参数控制：

```yaml
concurrency:
  max_goroutines: 100   # 控制停止操作的最大并发数
  rate_limit: 50ms      # 控制启动停止操作的频率
```

### 2. 超时设置

会话停止操作使用全局超时设置：

```yaml
timeout: 30  # HTTP请求超时时间（秒）
```

### 3. 性能影响

- **额外时间**：会话停止会增加总测试时间
- **网络开销**：每个会话需要额外的 HTTP 请求
- **系统负载**：并发停止操作会增加系统负载

## 错误处理

### 常见错误

1. **会话不存在**
   - 会话可能已经被其他操作停止
   - 检查会话ID是否正确

2. **权限不足**
   - 确认API端点权限配置
   - 检查认证信息

3. **网络超时**
   - 增加 `timeout` 值
   - 检查网络连接稳定性

4. **服务器过载**
   - 减少 `max_goroutines` 值
   - 增加 `rate_limit` 间隔

### 错误恢复

- 停止操作失败不会中断测试流程
- 失败的停止操作会记录详细错误信息
- 可以通过日志查看具体失败原因

## 最佳实践

### 1. 测试环境

在测试环境中启用会话停止功能：

```yaml
session_stop:
  enabled: true
  message: "测试环境压力测试完成"
  max_retries: 3
  retry_interval: 2s
```

### 2. 生产验证

在生产验证中谨慎使用：

```yaml
session_stop:
  enabled: false  # 生产环境建议先禁用
  message: "生产验证完成"
  max_retries: 5
  retry_interval: 5s
```

### 3. 长时间测试

对于长时间运行的测试，建议启用会话停止：

```yaml
session_stop:
  enabled: true
  message: "长时间稳定性测试完成，清理资源"
  max_retries: 5
  retry_interval: 3s
```

### 4. 批量测试

对于大批量会话测试，注意控制并发：

```yaml
batch_size: 100
session_stop:
  enabled: true
  message: "批量测试完成"
  max_retries: 3
  retry_interval: 2s
concurrency:
  max_goroutines: 50  # 控制停止操作并发数
  rate_limit: 100ms
```

## 故障排除

### 1. 停止操作超时

```yaml
timeout: 60  # 增加超时时间
session_stop:
  max_retries: 5
  retry_interval: 5s
```

### 2. 大量停止失败

```yaml
concurrency:
  max_goroutines: 20  # 减少并发数
  rate_limit: 200ms   # 增加速率限制
```

### 3. 调试停止问题

使用 `-verbose` 参数获取详细日志：

```bash
go run benchmark/cmd/main.go -config benchmark/config/with_session_stop.yaml -verbose
```

## 配置示例

### 完整配置示例

```yaml
# 完整的会话停止测试配置
api_prefix: "http://localhost:8080/api/v1"
timeout: 45
server_ids: [1, 2]
env_id: 1
batch_size: 20
timeout_minutes: 10

session_creation:
  max_retries: 3
  retry_interval: 2s

session_stop:
  enabled: true
  message: "完整流程测试完成，自动清理会话"
  max_retries: 3
  retry_interval: 2s

tool_call:
  tool_name: "filesystem__read_file"
  tool_input:
    path: "/tmp/test.txt"
  timeout_seconds: 30
  max_retries: 3
  retry_interval: 1s
  polling_enabled: true
  polling_count: 5
  polling_interval: 2s

report:
  output_dir: "logs/complete_test"
  detailed_logs: true
  include_errors: true

concurrency:
  max_goroutines: 50
  rate_limit: 100ms
```
