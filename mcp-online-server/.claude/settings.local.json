{"permissions": {"allow": ["Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/API_Client_Refactoring_Complete.md /Users/<USER>/code/_my/mcp-online-server/docs/API_Client_Refactoring_Summary.md /Users/<USER>/code/_my/mcp-online-server/docs/api/)", "Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/BOS_API_Documentation.md /Users/<USER>/code/_my/mcp-online-server/docs/BOS_Configuration.md /Users/<USER>/code/_my/mcp-online-server/docs/BOS_Integration_Summary.md /Users/<USER>/code/_my/mcp-online-server/docs/Environment_Management.md /Users/<USER>/code/_my/mcp-online-server/docs/bos/)", "Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/Session_Error_Tracking_Implementation.md /Users/<USER>/code/_my/mcp-online-server/docs/Session_Implementation_Summary.md /Users/<USER>/code/_my/mcp-online-server/docs/Session_Info_API_and_Remote_MCP_Implementation.md /Users/<USER>/code/_my/mcp-online-server/docs/session/)", "Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/TOOL_NAMING_CONFIGURATION.md /Users/<USER>/code/_my/mcp-online-server/docs/Tool_Call_Implementation_Summary.md /Users/<USER>/code/_my/mcp-online-server/docs/tool_call_image_id_implementation.md /Users/<USER>/code/_my/mcp-online-server/docs/tool_call_image_support.md /Users/<USER>/code/_my/mcp-online-server/docs/tool_call_strategy_implementation.md /Users/<USER>/code/_my/mcp-online-server/docs/tool_call/)", "Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/grafana_*.md /Users/<USER>/code/_my/mcp-online-server/docs/grafana_*.json /Users/<USER>/code/_my/mcp-online-server/docs/mcp_grafana_dashboard.json /Users/<USER>/code/_my/mcp-online-server/docs/metric_api_doc.txt /Users/<USER>/code/_my/mcp-online-server/docs/metrics_implementation.md /Users/<USER>/code/_my/mcp-online-server/docs/metrics_implementation_summary.md /Users/<USER>/code/_my/mcp-online-server/docs/monitoring_coverage_checklist.md /Users/<USER>/code/_my/mcp-online-server/docs/prometheus_alerts.yml /Users/<USER>/code/_my/mcp-online-server/docs/verify_metrics.sh /Users/<USER>/code/_my/mcp-online-server/docs/monitoring/)", "Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/MCP_Library_Optimization_Summary.md /Users/<USER>/code/_my/mcp-online-server/docs/Utils_Refactoring_Summary.md /Users/<USER>/code/_my/mcp-online-server/docs/dashboard_fix_summary.md /Users/<USER>/code/_my/mcp-online-server/docs/refactoring/)", "Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/root_user_feature_implementation_summary.md /Users/<USER>/code/_my/mcp-online-server/docs/server_code_download_enhancement.md /Users/<USER>/code/_my/mcp-online-server/docs/server_code_download_example.md /Users/<USER>/code/_my/mcp-online-server/docs/test_root_user_feature.md /Users/<USER>/code/_my/mcp-online-server/docs/features/)", "Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/test_env_init.md /Users/<USER>/code/_my/mcp-online-server/docs/testing/)", "Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/容器管理（k8s-proxy）接口.md /Users/<USER>/code/_my/mcp-online-server/docs/container_management/)", "Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/方案说明.md /Users/<USER>/code/_my/mcp-online-server/docs/方案说明_old.md /Users/<USER>/code/_my/mcp-online-server/docs/image_feature.md /Users/<USER>/code/_my/mcp-online-server/docs/design_documents/)", "Bash(mv /Users/<USER>/code/_my/mcp-online-server/docs/demo_data /Users/<USER>/code/_my/mcp-online-server/docs/data/)", "Bash(ls -la /Users/<USER>/code/_my/mcp-online-server/docs/)"], "deny": []}}