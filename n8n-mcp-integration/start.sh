#!/bin/bash

# n8n与MCP集成启动脚本

set -e

echo "🚀 启动n8n与MCP集成环境..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cat > .env << EOF
# LLM API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# n8n配置
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin123

# 数据库配置
POSTGRES_DB=n8n
POSTGRES_USER=n8n
POSTGRES_PASSWORD=n8n123
EOF
    echo "⚠️  请编辑 .env 文件，填入您的API密钥"
fi

# 检查mcp-bench目录
if [ ! -d "../mcp-bench" ]; then
    echo "❌ 未找到mcp-bench目录，请确保项目结构正确"
    echo "   期望目录结构："
    echo "   ├── mcp-bench/"
    echo "   └── n8n-mcp-integration/"
    exit 1
fi

# 构建自定义节点
echo "🔨 构建自定义n8n节点..."
if [ -d "src/nodes" ]; then
    cd src/nodes
    if [ ! -f package.json ]; then
        npm init -y
        npm install n8n-workflow
    fi
    npm run build 2>/dev/null || echo "构建完成（或无需构建）"
    cd ../..
fi

# 启动服务
echo "🐳 启动Docker服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."

# 检查MCP适配器
if curl -s http://localhost:8001/health > /dev/null; then
    echo "✅ MCP适配器服务正常"
else
    echo "❌ MCP适配器服务异常"
fi

# 检查n8n
if curl -s http://localhost:5678 > /dev/null; then
    echo "✅ n8n服务正常"
else
    echo "❌ n8n服务异常"
fi

echo ""
echo "🎉 启动完成！"
echo ""
echo "📊 服务访问地址："
echo "   n8n工作流界面: http://localhost:5678"
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "🔧 API端点："
echo "   MCP适配器: http://localhost:8001"
echo "   健康检查: http://localhost:8001/health"
echo "   MCP服务器列表: http://localhost:8001/mcp/servers"
echo ""
echo "📚 使用指南："
echo "   1. 访问 http://localhost:5678 登录n8n"
echo "   2. 创建新工作流"
echo "   3. 添加 'MCP Client' 或 'Data Synthesis' 节点"
echo "   4. 配置节点参数并执行工作流"
echo ""
echo "🛑 停止服务: ./stop.sh"
echo "📋 查看日志: docker-compose logs -f"
