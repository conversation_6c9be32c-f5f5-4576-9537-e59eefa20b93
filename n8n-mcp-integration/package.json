{"name": "n8n-mcp-integration", "version": "1.0.0", "description": "n8n integration with MCP servers for data synthesis and benchmark evaluation", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "tsc", "test": "jest"}, "keywords": ["n8n", "mcp", "data-synthesis", "benchmark", "llm"], "author": "Your Name", "license": "MIT", "dependencies": {"n8n": "^1.0.0", "express": "^4.18.0", "axios": "^1.6.0", "ws": "^8.14.0", "uuid": "^9.0.0", "lodash": "^4.17.21", "moment": "^2.29.0", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "nodemon": "^3.0.0", "jest": "^29.0.0"}, "n8n": {"nodes": ["dist/nodes/McpClient/McpClient.node.js", "dist/nodes/DataSynthesis/DataSynthesis.node.js", "dist/nodes/BenchmarkEvaluator/BenchmarkEvaluator.node.js"]}}