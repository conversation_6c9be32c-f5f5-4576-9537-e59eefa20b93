#!/usr/bin/env python3
"""
n8n与MCP集成演示脚本
展示如何通过API调用MCP适配器和创建工作流
"""

import json
import requests
import time
from typing import Dict, Any

class N8nMcpDemo:
    def __init__(self, mcp_adapter_url="http://localhost:8001", n8n_url="http://localhost:5678"):
        self.mcp_adapter_url = mcp_adapter_url
        self.n8n_url = n8n_url
        
    def test_mcp_adapter(self):
        """测试MCP适配器连接"""
        print("🔍 测试MCP适配器连接...")
        
        try:
            # 健康检查
            response = requests.get(f"{self.mcp_adapter_url}/health")
            if response.status_code == 200:
                print("✅ MCP适配器服务正常")
                print(f"   响应: {response.json()}")
            else:
                print("❌ MCP适配器服务异常")
                return False
                
            # 列出可用服务器
            response = requests.get(f"{self.mcp_adapter_url}/mcp/servers")
            if response.status_code == 200:
                servers = response.json()
                print(f"✅ 可用MCP服务器: {servers['servers']}")
            else:
                print("❌ 无法获取MCP服务器列表")
                
            return True
            
        except Exception as e:
            print(f"❌ 连接MCP适配器失败: {e}")
            return False
    
    def test_filesystem_operations(self):
        """测试文件系统操作"""
        print("\n📁 测试文件系统操作...")
        
        # 创建测试目录
        create_dir_payload = {
            "server_name": "filesystem",
            "tool_name": "create_directory",
            "parameters": {"path": "/tmp/n8n_test"}
        }
        
        try:
            response = requests.post(
                f"{self.mcp_adapter_url}/mcp/call",
                json=create_dir_payload
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 创建测试目录成功")
                else:
                    print(f"❌ 创建目录失败: {result.get('error')}")
            
            # 写入测试文件
            test_content = {
                "message": "Hello from n8n-MCP integration!",
                "timestamp": time.time(),
                "demo": True
            }
            
            write_file_payload = {
                "server_name": "filesystem",
                "tool_name": "write_file",
                "parameters": {
                    "path": "/tmp/n8n_test/demo.json",
                    "content": json.dumps(test_content, indent=2)
                }
            }
            
            response = requests.post(
                f"{self.mcp_adapter_url}/mcp/call",
                json=write_file_payload
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 写入测试文件成功")
                else:
                    print(f"❌ 写入文件失败: {result.get('error')}")
            
            # 读取测试文件
            read_file_payload = {
                "server_name": "filesystem",
                "tool_name": "read_file",
                "parameters": {"path": "/tmp/n8n_test/demo.json"}
            }
            
            response = requests.post(
                f"{self.mcp_adapter_url}/mcp/call",
                json=read_file_payload
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 读取测试文件成功")
                    content = json.loads(result['result']['content'])
                    print(f"   文件内容: {content['message']}")
                else:
                    print(f"❌ 读取文件失败: {result.get('error')}")
                    
        except Exception as e:
            print(f"❌ 文件系统操作失败: {e}")
    
    def create_sample_seed_data(self):
        """创建示例种子数据"""
        print("\n🌱 创建示例种子数据...")
        
        seed_data = [
            {
                "instruction": "解释什么是机器学习",
                "response": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。通过分析大量数据，机器学习算法可以识别模式并做出预测或决策。"
            },
            {
                "instruction": "如何优化深度学习模型的性能？",
                "response": "优化深度学习模型性能的方法包括：1) 调整学习率和批次大小；2) 使用正则化技术如dropout；3) 选择合适的激活函数；4) 数据增强；5) 使用预训练模型进行迁移学习。"
            },
            {
                "instruction": "什么是自然语言处理？",
                "response": "自然语言处理(NLP)是计算机科学和人工智能的一个领域，专注于计算机与人类语言之间的交互。它包括文本分析、语言理解、机器翻译、情感分析等任务。"
            }
        ]
        
        # 保存种子数据
        write_payload = {
            "server_name": "filesystem",
            "tool_name": "write_file",
            "parameters": {
                "path": "/tmp/n8n_test/seed_data.json",
                "content": json.dumps(seed_data, ensure_ascii=False, indent=2)
            }
        }
        
        try:
            response = requests.post(
                f"{self.mcp_adapter_url}/mcp/call",
                json=write_payload
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 种子数据创建成功")
                    print(f"   文件路径: /tmp/n8n_test/seed_data.json")
                    print(f"   数据条数: {len(seed_data)}")
                else:
                    print(f"❌ 创建种子数据失败: {result.get('error')}")
                    
        except Exception as e:
            print(f"❌ 创建种子数据失败: {e}")
    
    def simulate_data_synthesis(self):
        """模拟数据合成过程"""
        print("\n🎭 模拟数据合成过程...")
        
        # 这里模拟LLM生成的数据（实际应该调用LLM API）
        synthetic_data = [
            {
                "instruction": "描述神经网络的基本原理",
                "response": "神经网络是受生物神经系统启发的计算模型。它由相互连接的节点（神经元）组成，通过权重和偏置参数来处理信息。网络通过反向传播算法学习，调整参数以最小化预测误差。"
            },
            {
                "instruction": "什么是梯度下降算法？",
                "response": "梯度下降是一种优化算法，用于最小化损失函数。它通过计算损失函数相对于参数的梯度，然后沿着梯度的反方向更新参数。这个过程重复进行，直到找到最优解或收敛。"
            },
            {
                "instruction": "解释卷积神经网络的工作原理",
                "response": "卷积神经网络(CNN)专门用于处理具有网格结构的数据，如图像。它使用卷积层来检测局部特征，池化层来降低维度，全连接层来进行最终分类。这种架构能够有效地识别图像中的模式和特征。"
            }
        ]
        
        # 保存合成数据
        write_payload = {
            "server_name": "filesystem",
            "tool_name": "write_file",
            "parameters": {
                "path": "/tmp/n8n_test/synthetic_data.json",
                "content": json.dumps(synthetic_data, ensure_ascii=False, indent=2)
            }
        }
        
        try:
            response = requests.post(
                f"{self.mcp_adapter_url}/mcp/call",
                json=write_payload
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 合成数据保存成功")
                    print(f"   文件路径: /tmp/n8n_test/synthetic_data.json")
                    print(f"   生成条数: {len(synthetic_data)}")
                    
                    # 简单的质量分析
                    total_length = sum(len(item['instruction']) + len(item['response']) for item in synthetic_data)
                    avg_length = total_length / len(synthetic_data)
                    print(f"   平均长度: {avg_length:.1f} 字符")
                    
                else:
                    print(f"❌ 保存合成数据失败: {result.get('error')}")
                    
        except Exception as e:
            print(f"❌ 保存合成数据失败: {e}")
    
    def generate_demo_report(self):
        """生成演示报告"""
        print("\n📊 生成演示报告...")
        
        report = {
            "demo_summary": {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "status": "completed",
                "components_tested": [
                    "MCP适配器连接",
                    "文件系统操作",
                    "数据合成模拟",
                    "质量分析"
                ]
            },
            "next_steps": [
                "配置LLM API密钥",
                "创建n8n工作流",
                "测试完整的数据合成流程",
                "设置benchmark评估任务"
            ],
            "resources": {
                "n8n_ui": "http://localhost:5678",
                "mcp_adapter": "http://localhost:8001",
                "documentation": "README.md"
            }
        }
        
        write_payload = {
            "server_name": "filesystem",
            "tool_name": "write_file",
            "parameters": {
                "path": "/tmp/n8n_test/demo_report.json",
                "content": json.dumps(report, ensure_ascii=False, indent=2)
            }
        }
        
        try:
            response = requests.post(
                f"{self.mcp_adapter_url}/mcp/call",
                json=write_payload
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 演示报告生成成功")
                    print(f"   报告路径: /tmp/n8n_test/demo_report.json")
                else:
                    print(f"❌ 生成报告失败: {result.get('error')}")
                    
        except Exception as e:
            print(f"❌ 生成报告失败: {e}")
    
    def run_demo(self):
        """运行完整演示"""
        print("🎬 开始n8n与MCP集成演示")
        print("=" * 50)
        
        # 测试连接
        if not self.test_mcp_adapter():
            print("❌ 演示终止：MCP适配器不可用")
            return
        
        # 测试文件操作
        self.test_filesystem_operations()
        
        # 创建示例数据
        self.create_sample_seed_data()
        
        # 模拟数据合成
        self.simulate_data_synthesis()
        
        # 生成报告
        self.generate_demo_report()
        
        print("\n" + "=" * 50)
        print("🎉 演示完成！")
        print("\n📋 下一步操作：")
        print("1. 访问 http://localhost:5678 打开n8n界面")
        print("2. 导入示例工作流: examples/data-synthesis-workflow.json")
        print("3. 配置LLM API密钥")
        print("4. 运行完整的数据合成工作流")
        print("\n📁 生成的文件：")
        print("   /tmp/n8n_test/seed_data.json - 种子数据")
        print("   /tmp/n8n_test/synthetic_data.json - 合成数据")
        print("   /tmp/n8n_test/demo_report.json - 演示报告")

if __name__ == "__main__":
    demo = N8nMcpDemo()
    demo.run_demo()
