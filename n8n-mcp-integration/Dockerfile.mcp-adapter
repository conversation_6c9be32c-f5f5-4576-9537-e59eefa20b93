FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/mcp-adapter/ ./src/mcp-adapter/
COPY mcp-bench/ ./mcp-bench/

# 设置环境变量
ENV PYTHONPATH=/app
ENV FLASK_APP=src/mcp-adapter/app.py

# 暴露端口
EXPOSE 8001

# 启动命令
CMD ["python", "src/mcp-adapter/app.py"]
