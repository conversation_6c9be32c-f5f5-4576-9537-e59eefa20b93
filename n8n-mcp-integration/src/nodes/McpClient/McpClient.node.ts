import {
    IExecuteFunctions,
    INodeExecutionData,
    INodeType,
    INodeTypeDescription,
    NodeOperationError,
} from 'n8n-workflow';
import axios from 'axios';

export class McpClient implements INodeType {
    description: INodeTypeDescription = {
        displayName: 'MCP Client',
        name: 'mcpClient',
        icon: 'fa:plug',
        group: ['transform'],
        version: 1,
        description: 'Connect to MCP servers and execute tools',
        defaults: {
            name: 'MCP Client',
        },
        inputs: ['main'],
        outputs: ['main'],
        properties: [
            {
                displayName: 'MCP Server',
                name: 'serverName',
                type: 'options',
                options: [
                    {
                        name: 'Filesystem',
                        value: 'filesystem',
                    },
                    {
                        name: 'SQLite',
                        value: 'sqlite',
                    },
                    {
                        name: 'Playwright',
                        value: 'playwright',
                    },
                    {
                        name: 'Firecrawl',
                        value: 'firecrawl-mcp',
                    },
                ],
                default: 'filesystem',
                description: 'The MCP server to connect to',
            },
            {
                displayName: 'Operation',
                name: 'operation',
                type: 'options',
                displayOptions: {
                    show: {
                        serverName: ['filesystem'],
                    },
                },
                options: [
                    {
                        name: 'Read File',
                        value: 'read_file',
                        description: 'Read content from a file',
                    },
                    {
                        name: 'Write File',
                        value: 'write_file',
                        description: 'Write content to a file',
                    },
                    {
                        name: 'List Directory',
                        value: 'list_directory',
                        description: 'List files in a directory',
                    },
                    {
                        name: 'Create Directory',
                        value: 'create_directory',
                        description: 'Create a new directory',
                    },
                ],
                default: 'read_file',
                description: 'The operation to perform',
            },
            {
                displayName: 'File Path',
                name: 'filePath',
                type: 'string',
                displayOptions: {
                    show: {
                        serverName: ['filesystem'],
                        operation: ['read_file', 'write_file'],
                    },
                },
                default: '',
                placeholder: '/path/to/file.txt',
                description: 'Path to the file',
            },
            {
                displayName: 'Directory Path',
                name: 'directoryPath',
                type: 'string',
                displayOptions: {
                    show: {
                        serverName: ['filesystem'],
                        operation: ['list_directory', 'create_directory'],
                    },
                },
                default: '',
                placeholder: '/path/to/directory',
                description: 'Path to the directory',
            },
            {
                displayName: 'Content',
                name: 'content',
                type: 'string',
                typeOptions: {
                    rows: 4,
                },
                displayOptions: {
                    show: {
                        serverName: ['filesystem'],
                        operation: ['write_file'],
                    },
                },
                default: '',
                description: 'Content to write to the file',
            },
            {
                displayName: 'Custom Parameters',
                name: 'customParameters',
                type: 'json',
                default: '{}',
                description: 'Custom parameters as JSON object',
            },
        ],
    };

    async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
        const items = this.getInputData();
        const returnData: INodeExecutionData[] = [];

        for (let i = 0; i < items.length; i++) {
            try {
                const serverName = this.getNodeParameter('serverName', i) as string;
                const operation = this.getNodeParameter('operation', i) as string;
                const customParameters = this.getNodeParameter('customParameters', i) as object;

                let parameters: any = { ...customParameters };

                // 根据操作类型构建参数
                switch (operation) {
                    case 'read_file':
                        parameters.path = this.getNodeParameter('filePath', i) as string;
                        break;
                    case 'write_file':
                        parameters.path = this.getNodeParameter('filePath', i) as string;
                        parameters.content = this.getNodeParameter('content', i) as string;
                        break;
                    case 'list_directory':
                    case 'create_directory':
                        parameters.path = this.getNodeParameter('directoryPath', i) as string;
                        break;
                }

                // 调用MCP适配器API
                const result = await this.callMcpAdapter(serverName, operation, parameters);

                returnData.push({
                    json: {
                        serverName,
                        operation,
                        parameters,
                        result: result.data,
                        success: result.data.success,
                        timestamp: new Date().toISOString(),
                    },
                });

            } catch (error) {
                if (this.continueOnFail()) {
                    returnData.push({
                        json: {
                            error: error.message,
                            timestamp: new Date().toISOString(),
                        },
                    });
                    continue;
                }
                throw new NodeOperationError(this.getNode(), error.message);
            }
        }

        return [returnData];
    }

    private async callMcpAdapter(serverName: string, toolName: string, parameters: object) {
        const adapterUrl = process.env.MCP_ADAPTER_URL || 'http://localhost:8001';
        
        try {
            const response = await axios.post(`${adapterUrl}/mcp/call`, {
                server_name: serverName,
                tool_name: toolName,
                parameters: parameters,
            }, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            return response;
        } catch (error) {
            if (error.response) {
                throw new Error(`MCP Adapter Error: ${error.response.status} - ${error.response.data}`);
            } else if (error.request) {
                throw new Error('MCP Adapter not responding. Please check if the adapter service is running.');
            } else {
                throw new Error(`Request Error: ${error.message}`);
            }
        }
    }
}
