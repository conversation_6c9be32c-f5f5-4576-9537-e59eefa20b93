import {
    IExecuteFunctions,
    INodeExecutionData,
    INodeType,
    INodeTypeDescription,
    NodeOperationError,
} from 'n8n-workflow';
import axios from 'axios';

export class DataSynthesis implements INodeType {
    description: INodeTypeDescription = {
        displayName: 'Data Synthesis',
        name: 'dataSynthesis',
        icon: 'fa:magic',
        group: ['transform'],
        version: 1,
        description: 'Generate synthetic training data using LLMs',
        defaults: {
            name: 'Data Synthesis',
        },
        inputs: ['main'],
        outputs: ['main'],
        properties: [
            {
                displayName: 'Synthesis Mode',
                name: 'mode',
                type: 'options',
                options: [
                    {
                        name: 'Instruction-Response Pairs',
                        value: 'instruction_response',
                        description: 'Generate instruction-response training pairs',
                    },
                    {
                        name: 'Multi-turn Conversations',
                        value: 'conversation',
                        description: 'Generate multi-turn conversation data',
                    },
                    {
                        name: 'Data Augmentation',
                        value: 'augmentation',
                        description: 'Augment existing data with variations',
                    },
                    {
                        name: 'Custom Template',
                        value: 'custom',
                        description: 'Use custom prompt template',
                    },
                ],
                default: 'instruction_response',
                description: 'Type of synthetic data to generate',
            },
            {
                displayName: 'LLM Provider',
                name: 'llm<PERSON><PERSON>ider',
                type: 'options',
                options: [
                    {
                        name: 'OpenAI',
                        value: 'openai',
                    },
                    {
                        name: 'Anthropic',
                        value: 'anthropic',
                    },
                    {
                        name: 'Local Model',
                        value: 'local',
                    },
                ],
                default: 'openai',
                description: 'LLM provider to use for generation',
            },
            {
                displayName: 'Model Name',
                name: 'modelName',
                type: 'string',
                default: 'gpt-4',
                description: 'Name of the model to use',
            },
            {
                displayName: 'Seed Data',
                name: 'seedData',
                type: 'json',
                default: '[]',
                description: 'Seed data to base generation on (JSON array)',
            },
            {
                displayName: 'Generation Count',
                name: 'generationCount',
                type: 'number',
                default: 5,
                description: 'Number of synthetic examples to generate',
            },
            {
                displayName: 'Temperature',
                name: 'temperature',
                type: 'number',
                typeOptions: {
                    minValue: 0,
                    maxValue: 2,
                    numberStepSize: 0.1,
                },
                default: 0.7,
                description: 'Creativity level (0 = deterministic, 2 = very creative)',
            },
            {
                displayName: 'Custom Prompt Template',
                name: 'customPrompt',
                type: 'string',
                typeOptions: {
                    rows: 6,
                },
                displayOptions: {
                    show: {
                        mode: ['custom'],
                    },
                },
                default: 'Generate {count} training examples based on this seed data: {seedData}',
                description: 'Custom prompt template. Use {count}, {seedData}, etc. as placeholders',
            },
            {
                displayName: 'Quality Filters',
                name: 'qualityFilters',
                type: 'collection',
                placeholder: 'Add Quality Filter',
                default: {},
                options: [
                    {
                        displayName: 'Minimum Length',
                        name: 'minLength',
                        type: 'number',
                        default: 10,
                        description: 'Minimum character length for generated text',
                    },
                    {
                        displayName: 'Maximum Length',
                        name: 'maxLength',
                        type: 'number',
                        default: 1000,
                        description: 'Maximum character length for generated text',
                    },
                    {
                        displayName: 'Require Both Instruction and Response',
                        name: 'requireBoth',
                        type: 'boolean',
                        default: true,
                        description: 'Ensure both instruction and response are present',
                    },
                    {
                        displayName: 'Filter Duplicates',
                        name: 'filterDuplicates',
                        type: 'boolean',
                        default: true,
                        description: 'Remove duplicate or very similar examples',
                    },
                ],
            },
            {
                displayName: 'Output Format',
                name: 'outputFormat',
                type: 'options',
                options: [
                    {
                        name: 'JSON Array',
                        value: 'json',
                    },
                    {
                        name: 'JSONL',
                        value: 'jsonl',
                    },
                    {
                        name: 'CSV',
                        value: 'csv',
                    },
                ],
                default: 'json',
                description: 'Format for the generated data',
            },
        ],
    };

    async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
        const items = this.getInputData();
        const returnData: INodeExecutionData[] = [];

        for (let i = 0; i < items.length; i++) {
            try {
                const mode = this.getNodeParameter('mode', i) as string;
                const llmProvider = this.getNodeParameter('llmProvider', i) as string;
                const modelName = this.getNodeParameter('modelName', i) as string;
                const seedData = this.getNodeParameter('seedData', i) as any[];
                const generationCount = this.getNodeParameter('generationCount', i) as number;
                const temperature = this.getNodeParameter('temperature', i) as number;
                const qualityFilters = this.getNodeParameter('qualityFilters', i) as any;
                const outputFormat = this.getNodeParameter('outputFormat', i) as string;

                // 构建提示词
                const prompt = this.buildPrompt(mode, seedData, generationCount, i);

                // 调用LLM生成数据
                const generatedData = await this.generateData(
                    llmProvider,
                    modelName,
                    prompt,
                    temperature
                );

                // 应用质量过滤
                const filteredData = this.applyQualityFilters(generatedData, qualityFilters);

                // 格式化输出
                const formattedOutput = this.formatOutput(filteredData, outputFormat);

                returnData.push({
                    json: {
                        mode,
                        seedDataCount: seedData.length,
                        generatedCount: filteredData.length,
                        filteredCount: generatedData.length - filteredData.length,
                        data: formattedOutput,
                        metadata: {
                            llmProvider,
                            modelName,
                            temperature,
                            timestamp: new Date().toISOString(),
                        },
                    },
                });

            } catch (error) {
                if (this.continueOnFail()) {
                    returnData.push({
                        json: {
                            error: error.message,
                            timestamp: new Date().toISOString(),
                        },
                    });
                    continue;
                }
                throw new NodeOperationError(this.getNode(), error.message);
            }
        }

        return [returnData];
    }

    private buildPrompt(mode: string, seedData: any[], count: number, itemIndex: number): string {
        const seedDataStr = JSON.stringify(seedData, null, 2);

        switch (mode) {
            case 'instruction_response':
                return `Generate ${count} high-quality instruction-response pairs for training a language model. 
Base them on these seed examples: ${seedDataStr}

Each pair should have:
- "instruction": A clear, specific task or question
- "response": A helpful, accurate, and detailed response

Output as a JSON array of objects with "instruction" and "response" fields.
Ensure variety in topics, complexity, and response styles.`;

            case 'conversation':
                return `Generate ${count} multi-turn conversations based on these seed examples: ${seedDataStr}

Each conversation should have:
- Multiple turns between user and assistant
- Natural flow and context continuity
- Varied topics and conversation styles

Output as a JSON array where each object has a "conversation" field containing an array of {"role": "user/assistant", "content": "..."} objects.`;

            case 'augmentation':
                return `Create ${count} variations of the following data while preserving the core meaning and structure: ${seedDataStr}

For each example, generate variations by:
- Rephrasing the language
- Changing the perspective or tone
- Adding relevant details or context
- Using different examples or scenarios

Output as a JSON array maintaining the original structure.`;

            case 'custom':
                const customPrompt = this.getNodeParameter('customPrompt', itemIndex) as string;
                return customPrompt
                    .replace('{count}', count.toString())
                    .replace('{seedData}', seedDataStr);

            default:
                throw new Error(`Unknown synthesis mode: ${mode}`);
        }
    }

    private async generateData(provider: string, model: string, prompt: string, temperature: number): Promise<any[]> {
        try {
            // 这里可以集成不同的LLM提供商
            // 为了简化，我们使用一个通用的API调用
            const response = await axios.post('http://localhost:8002/generate', {
                provider,
                model,
                prompt,
                temperature,
                max_tokens: 4000,
            });

            const generatedText = response.data.content;
            
            // 尝试解析JSON响应
            try {
                return JSON.parse(generatedText);
            } catch (parseError) {
                // 如果不是有效JSON，尝试提取JSON部分
                const jsonMatch = generatedText.match(/\[[\s\S]*\]/);
                if (jsonMatch) {
                    return JSON.parse(jsonMatch[0]);
                }
                throw new Error('Generated content is not valid JSON');
            }

        } catch (error) {
            throw new Error(`Data generation failed: ${error.message}`);
        }
    }

    private applyQualityFilters(data: any[], filters: any): any[] {
        if (!filters || !Array.isArray(data)) {
            return data;
        }

        return data.filter((item, index) => {
            // 长度检查
            if (filters.minLength || filters.maxLength) {
                const text = JSON.stringify(item);
                if (filters.minLength && text.length < filters.minLength) return false;
                if (filters.maxLength && text.length > filters.maxLength) return false;
            }

            // 检查指令-响应对的完整性
            if (filters.requireBoth && item.instruction !== undefined && item.response !== undefined) {
                if (!item.instruction || !item.response) return false;
                if (item.instruction.trim().length < 5 || item.response.trim().length < 10) return false;
            }

            // 重复检查（简单的字符串相似度）
            if (filters.filterDuplicates) {
                const currentText = JSON.stringify(item).toLowerCase();
                for (let i = 0; i < index; i++) {
                    const compareText = JSON.stringify(data[i]).toLowerCase();
                    const similarity = this.calculateSimilarity(currentText, compareText);
                    if (similarity > 0.8) return false; // 80%相似度阈值
                }
            }

            return true;
        });
    }

    private calculateSimilarity(str1: string, str2: string): number {
        // 简单的Jaccard相似度计算
        const set1 = new Set(str1.split(' '));
        const set2 = new Set(str2.split(' '));
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);
        return intersection.size / union.size;
    }

    private formatOutput(data: any[], format: string): any {
        switch (format) {
            case 'json':
                return data;
            case 'jsonl':
                return data.map(item => JSON.stringify(item)).join('\n');
            case 'csv':
                if (data.length === 0) return '';
                const headers = Object.keys(data[0]);
                const csvRows = [headers.join(',')];
                data.forEach(item => {
                    const row = headers.map(header => {
                        const value = item[header] || '';
                        return `"${value.toString().replace(/"/g, '""')}"`;
                    });
                    csvRows.push(row.join(','));
                });
                return csvRows.join('\n');
            default:
                return data;
        }
    }
}
