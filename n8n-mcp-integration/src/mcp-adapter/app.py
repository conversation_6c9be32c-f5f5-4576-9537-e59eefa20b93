#!/usr/bin/env python3
"""
MCP适配器服务
为n8n提供与MCP服务器交互的HTTP API接口
"""

import asyncio
import json
import logging
import os
import sys
from typing import Dict, Any, Optional
from flask import Flask, request, jsonify
from flask_cors import CORS

# 添加mcp-bench路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../mcp-bench'))

from src.engines.evaluation_engine import EvaluationEngine
from src.models.evaluation_models import EvaluationConfig, EvaluationTask, MCPServerConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class McpAdapterService:
    """MCP适配器服务类"""
    
    def __init__(self):
        self.evaluation_engine = None
        self.mcp_config = None
        self.load_mcp_config()
    
    def load_mcp_config(self):
        """加载MCP服务器配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../../../mcp-bench/mcpservers_all.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                self.mcp_config = json.load(f)
            logger.info("MCP配置加载成功")
        except Exception as e:
            logger.error(f"加载MCP配置失败: {e}")
            self.mcp_config = {"mcpServers": {}}
    
    async def initialize_evaluation_engine(self, server_names: list = None):
        """初始化评估引擎"""
        try:
            if server_names is None:
                server_names = list(self.mcp_config.get('mcpServers', {}).keys())
            
            # 创建MCP服务器配置
            mcp_servers = []
            for server_name in server_names:
                if server_name in self.mcp_config['mcpServers']:
                    server_config = self.mcp_config['mcpServers'][server_name]
                    mcp_server = MCPServerConfig(
                        name=server_name,
                        command=[server_config['command']] + server_config.get('args', []),
                        env=server_config.get('env', {}),
                        transport='stdio'
                    )
                    mcp_servers.append(mcp_server)
            
            # 创建评估配置
            eval_config = EvaluationConfig(
                evaluation_id="n8n_adapter_session",
                mcp_servers=mcp_servers,
                tasks=[],  # 临时空任务列表
                global_timeout=300
            )
            
            # 初始化评估引擎
            self.evaluation_engine = EvaluationEngine(eval_config)
            await self.evaluation_engine.setup_servers()
            
            logger.info(f"评估引擎初始化成功，连接了 {len(mcp_servers)} 个MCP服务器")
            return True
            
        except Exception as e:
            logger.error(f"初始化评估引擎失败: {e}")
            return False
    
    async def call_mcp_tool(self, server_name: str, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            # 确保评估引擎已初始化
            if self.evaluation_engine is None:
                await self.initialize_evaluation_engine([server_name])
            
            # 检查服务器是否可用
            if server_name not in self.evaluation_engine.mcp_clients:
                return {
                    "success": False,
                    "error": f"MCP服务器 '{server_name}' 不可用",
                    "available_servers": list(self.evaluation_engine.mcp_clients.keys())
                }
            
            # 调用工具
            client = self.evaluation_engine.mcp_clients[server_name]
            result = await client.call_tool(tool_name, parameters)
            
            return {
                "success": True,
                "result": result,
                "server": server_name,
                "tool": tool_name,
                "parameters": parameters
            }
            
        except Exception as e:
            logger.error(f"调用MCP工具失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "server": server_name,
                "tool": tool_name,
                "parameters": parameters
            }
    
    async def list_available_tools(self, server_name: str) -> Dict[str, Any]:
        """列出可用工具"""
        try:
            if self.evaluation_engine is None:
                await self.initialize_evaluation_engine([server_name])
            
            if server_name not in self.evaluation_engine.mcp_clients:
                return {
                    "success": False,
                    "error": f"MCP服务器 '{server_name}' 不可用"
                }
            
            client = self.evaluation_engine.mcp_clients[server_name]
            tools = await client.list_tools()
            
            return {
                "success": True,
                "server": server_name,
                "tools": tools
            }
            
        except Exception as e:
            logger.error(f"列出工具失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "server": server_name
            }

# 创建全局适配器实例
adapter = McpAdapterService()

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        "status": "healthy",
        "service": "MCP Adapter",
        "version": "1.0.0"
    })

@app.route('/mcp/servers', methods=['GET'])
def list_servers():
    """列出可用的MCP服务器"""
    return jsonify({
        "success": True,
        "servers": list(adapter.mcp_config.get('mcpServers', {}).keys())
    })

@app.route('/mcp/tools/<server_name>', methods=['GET'])
def list_tools(server_name):
    """列出指定服务器的可用工具"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(adapter.list_available_tools(server_name))
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/mcp/call', methods=['POST'])
def call_mcp_tool():
    """调用MCP工具的主要端点"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "error": "请求体不能为空"
            }), 400
        
        server_name = data.get('server_name')
        tool_name = data.get('tool_name')
        parameters = data.get('parameters', {})
        
        if not server_name or not tool_name:
            return jsonify({
                "success": False,
                "error": "server_name 和 tool_name 是必需的参数"
            }), 400
        
        # 异步调用MCP工具
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(
            adapter.call_mcp_tool(server_name, tool_name, parameters)
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"处理MCP调用请求失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/mcp/batch', methods=['POST'])
def batch_call_mcp_tools():
    """批量调用MCP工具"""
    try:
        data = request.get_json()
        calls = data.get('calls', [])
        
        if not calls:
            return jsonify({
                "success": False,
                "error": "calls 数组不能为空"
            }), 400
        
        results = []
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        for call in calls:
            result = loop.run_until_complete(
                adapter.call_mcp_tool(
                    call.get('server_name'),
                    call.get('tool_name'),
                    call.get('parameters', {})
                )
            )
            results.append(result)
        
        return jsonify({
            "success": True,
            "results": results
        })
        
    except Exception as e:
        logger.error(f"批量调用失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8001))
    debug = os.environ.get('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"启动MCP适配器服务，端口: {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
