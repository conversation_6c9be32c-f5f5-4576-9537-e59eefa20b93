# n8n与MCP集成：数据合成和Benchmark评估平台

这个项目将n8n工作流自动化平台与您现有的MCP基础设施集成，为大模型训练数据合成和benchmark评估提供可视化、自动化的解决方案。

## 🚀 快速开始

### 前置要求
- Docker 和 Docker Compose
- 您的mcp-bench项目（应位于 `../mcp-bench/`）
- LLM API密钥（OpenAI、Anthropic等）

### 一键启动
```bash
# 克隆或创建项目目录
mkdir n8n-mcp-integration && cd n8n-mcp-integration

# 复制所有文件到此目录后执行
chmod +x start.sh stop.sh
./start.sh
```

### 访问界面
- **n8n工作流界面**: http://localhost:5678
  - 用户名: `admin`
  - 密码: `admin123`
- **MCP适配器API**: http://localhost:8001

## 📋 功能特性

### 🔧 核心组件
1. **MCP Client节点** - 连接和调用MCP服务器
2. **Data Synthesis节点** - LLM驱动的数据合成
3. **Benchmark Evaluator节点** - 自动化评估任务
4. **MCP适配器服务** - HTTP API桥接MCP协议

### 🎯 主要功能
- **可视化工作流设计** - 拖拽式流程编排
- **多MCP服务器支持** - filesystem、sqlite、playwright等
- **智能数据合成** - 基于种子数据生成训练样本
- **质量控制机制** - 自动过滤和评估生成数据
- **批量评估任务** - 并行执行多模型benchmark
- **结果聚合分析** - 自动生成评估报告

## 🏗️ 架构设计

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   n8n UI    │    │ MCP Adapter │    │ MCP Servers │
│  (5678)     │◄──►│   (8001)    │◄──►│ (各种工具)   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 工作流引擎   │    │ Python后端   │    │ 文件系统     │
│ 自定义节点   │    │ Flask API   │    │ 数据库      │
│ 数据处理     │    │ 异步调用     │    │ 浏览器自动化 │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 📚 使用指南

### 1. 数据合成工作流

#### 创建基础数据合成流程：
1. 登录n8n界面
2. 创建新工作流
3. 添加以下节点：
   - **MCP Client** (读取种子数据)
   - **Data Synthesis** (生成合成数据)
   - **Function** (质量检查)
   - **IF** (质量门控)
   - **MCP Client** (保存结果)

#### 配置示例：
```json
{
  "mode": "instruction_response",
  "llmProvider": "openai",
  "modelName": "gpt-4",
  "generationCount": 10,
  "temperature": 0.7,
  "qualityFilters": {
    "minLength": 20,
    "requireBoth": true,
    "filterDuplicates": true
  }
}
```

### 2. Benchmark评估工作流

#### 创建评估流程：
1. 加载评估配置
2. 创建任务矩阵（任务×模型）
3. 并行执行评估
4. 聚合结果
5. 生成报告

### 3. 自定义节点开发

#### 扩展MCP Client节点：
```typescript
// 添加新的MCP服务器支持
{
  displayName: 'Custom MCP Server',
  name: 'customMcp',
  type: 'options',
  options: [
    {
      name: 'My Custom Server',
      value: 'my_custom_server',
    }
  ]
}
```

## 🔧 配置说明

### 环境变量 (.env)
```bash
# LLM API密钥
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# n8n配置
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin123

# 服务端口
MCP_ADAPTER_PORT=8001
LLM_PROXY_PORT=8002
```

### MCP服务器配置
配置文件位于 `../mcp-bench/mcpservers_all.json`，支持：
- filesystem - 文件系统操作
- sqlite - 数据库操作
- playwright - 浏览器自动化
- firecrawl - 网页抓取

## 📊 示例工作流

### 数据合成流程
1. **输入**: 种子数据文件 (`seed_data.json`)
2. **处理**: LLM生成变体数据
3. **过滤**: 质量检查和去重
4. **输出**: 高质量训练数据集

### 评估流程
1. **配置**: 加载评估任务和模型列表
2. **执行**: 并行运行多个评估任务
3. **聚合**: 收集和分析结果
4. **报告**: 生成详细评估报告

## 🛠️ 开发指南

### 添加新的自定义节点
1. 在 `src/nodes/` 下创建新目录
2. 实现 `INodeType` 接口
3. 在 `package.json` 中注册节点
4. 重新构建和部署

### 扩展MCP适配器
1. 修改 `src/mcp-adapter/app.py`
2. 添加新的API端点
3. 实现MCP协议交互逻辑

### 集成新的LLM提供商
1. 扩展 `DataSynthesis` 节点
2. 添加新的提供商选项
3. 实现API调用逻辑

## 🔍 监控和调试

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f mcp-adapter
docker-compose logs -f n8n
```

### 健康检查
```bash
# MCP适配器健康检查
curl http://localhost:8001/health

# 列出可用MCP服务器
curl http://localhost:8001/mcp/servers

# 测试MCP工具调用
curl -X POST http://localhost:8001/mcp/call \
  -H "Content-Type: application/json" \
  -d '{"server_name": "filesystem", "tool_name": "list_directory", "parameters": {"path": "/"}}'
```

## 🚨 故障排除

### 常见问题

1. **MCP服务器连接失败**
   - 检查 `mcpservers_all.json` 配置
   - 确认依赖的npm包已安装
   - 查看适配器日志

2. **n8n节点不显示**
   - 确认自定义节点已正确构建
   - 检查 `package.json` 中的节点注册
   - 重启n8n容器

3. **LLM API调用失败**
   - 验证API密钥配置
   - 检查网络连接
   - 确认模型名称正确

### 重置环境
```bash
# 停止所有服务并清理数据
./stop.sh
docker-compose down -v
docker system prune -f

# 重新启动
./start.sh
```

## 📈 性能优化

### 建议配置
- **并发任务数**: 根据系统资源调整
- **批处理大小**: 平衡内存使用和效率
- **缓存策略**: 启用Redis缓存重复请求
- **资源限制**: 设置Docker容器资源限制

### 扩展部署
- 使用Kubernetes进行水平扩展
- 配置负载均衡器
- 实施监控和告警系统

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🔗 相关链接

- [n8n官方文档](https://docs.n8n.io/)
- [MCP协议规范](https://modelcontextprotocol.io/)
- [您的mcp-bench项目](../mcp-bench/)

---

**开始您的数据合成和评估自动化之旅！** 🎉
