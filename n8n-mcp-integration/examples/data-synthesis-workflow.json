{"name": "数据合成工作流示例", "nodes": [{"parameters": {"serverName": "filesystem", "operation": "read_file", "filePath": "/workspace/seed_data.json"}, "id": "load-seed-data", "name": "加载种子数据", "type": "mcpClient", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"mode": "instruction_response", "llmProvider": "openai", "modelName": "gpt-4", "seedData": "={{ JSON.parse($json.result.content) }}", "generationCount": 10, "temperature": 0.7, "qualityFilters": {"minLength": 20, "maxLength": 2000, "requireBoth": true, "filterDuplicates": true}, "outputFormat": "json"}, "id": "synthesize-data", "name": "合成训练数据", "type": "dataSynthesis", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"functionCode": "// 数据质量评估和统计\nconst data = $json.data;\nconst stats = {\n  totalGenerated: data.length,\n  avgInstructionLength: 0,\n  avgResponseLength: 0,\n  topics: new Set(),\n  qualityScore: 0\n};\n\nif (Array.isArray(data) && data.length > 0) {\n  let totalInstLen = 0;\n  let totalRespLen = 0;\n  \n  data.forEach(item => {\n    if (item.instruction) {\n      totalInstLen += item.instruction.length;\n      // 简单的主题提取\n      const words = item.instruction.toLowerCase().split(' ');\n      words.forEach(word => {\n        if (word.length > 4) stats.topics.add(word);\n      });\n    }\n    if (item.response) {\n      totalRespLen += item.response.length;\n    }\n  });\n  \n  stats.avgInstructionLength = Math.round(totalInstLen / data.length);\n  stats.avgResponseLength = Math.round(totalRespLen / data.length);\n  stats.topics = Array.from(stats.topics).slice(0, 10); // 前10个主题\n  \n  // 简单的质量评分\n  stats.qualityScore = Math.min(1.0, \n    (stats.avgInstructionLength / 50) * 0.3 + \n    (stats.avgResponseLength / 100) * 0.4 + \n    (stats.topics.length / 10) * 0.3\n  );\n}\n\nreturn [{\n  json: {\n    ...stats,\n    data: data,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "quality-analysis", "name": "质量分析", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.qualityScore }}", "operation": "larger", "value2": 0.6}]}}, "id": "quality-gate", "name": "质量门控", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"serverName": "filesystem", "operation": "write_file", "filePath": "={{ '/workspace/output/synthetic_data_' + new Date().toISOString().split('T')[0] + '.json' }}", "content": "={{ JSON.stringify($json.data, null, 2) }}"}, "id": "save-approved-data", "name": "保存合格数据", "type": "mcpClient", "typeVersion": 1, "position": [1120, 240]}, {"parameters": {"functionCode": "// 生成数据质量报告\nconst report = {\n  timestamp: new Date().toISOString(),\n  status: 'rejected',\n  reason: 'Quality score too low',\n  qualityScore: $json.qualityScore,\n  recommendations: [\n    '调整提示词模板以提高生成质量',\n    '增加种子数据的多样性',\n    '调整温度参数以平衡创造性和一致性'\n  ],\n  data: $json.data\n};\n\nreturn [{ json: report }];"}, "id": "rejection-report", "name": "生成拒绝报告", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1120, 360]}, {"parameters": {"serverName": "filesystem", "operation": "write_file", "filePath": "={{ '/workspace/output/quality_report_' + new Date().toISOString().split('T')[0] + '.json' }}", "content": "={{ JSON.stringify($json, null, 2) }}"}, "id": "save-report", "name": "保存质量报告", "type": "mcpClient", "typeVersion": 1, "position": [1340, 300]}], "connections": {"加载种子数据": {"main": [[{"node": "合成训练数据", "type": "main", "index": 0}]]}, "合成训练数据": {"main": [[{"node": "质量分析", "type": "main", "index": 0}]]}, "质量分析": {"main": [[{"node": "质量门控", "type": "main", "index": 0}]]}, "质量门控": {"main": [[{"node": "保存合格数据", "type": "main", "index": 0}, {"node": "保存质量报告", "type": "main", "index": 0}], [{"node": "生成拒绝报告", "type": "main", "index": 0}]]}, "保存合格数据": {"main": [[{"node": "保存质量报告", "type": "main", "index": 0}]]}, "生成拒绝报告": {"main": [[{"node": "保存质量报告", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "data-synthesis", "name": "数据合成"}], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}