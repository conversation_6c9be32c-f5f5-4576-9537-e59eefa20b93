version: '3.8'

services:
  # n8n工作流引擎
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n-mcp
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=Asia/Shanghai
      - MCP_ADAPTER_URL=http://mcp-adapter:8001
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./src/nodes:/home/<USER>/.n8n/custom/nodes
      - ../mcp-bench:/workspace/mcp-bench:ro
    depends_on:
      - mcp-adapter
    networks:
      - n8n-network

  # MCP适配器服务
  mcp-adapter:
    build:
      context: .
      dockerfile: Dockerfile.mcp-adapter
    container_name: mcp-adapter
    ports:
      - "8001:8001"
    environment:
      - PORT=8001
      - DEBUG=false
      - PYTHONPATH=/app
    volumes:
      - ../mcp-bench:/app/mcp-bench:ro
      - ./src/mcp-adapter:/app/src/mcp-adapter
    networks:
      - n8n-network

  # LLM代理服务（可选）
  llm-proxy:
    build:
      context: .
      dockerfile: Dockerfile.llm-proxy
    container_name: llm-proxy
    ports:
      - "8002:8002"
    environment:
      - PORT=8002
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    networks:
      - n8n-network

  # PostgreSQL数据库（用于存储n8n数据）
  postgres:
    image: postgres:13
    container_name: n8n-postgres
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - n8n-network

  # Redis（用于缓存和队列）
  redis:
    image: redis:7-alpine
    container_name: n8n-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - n8n-network

volumes:
  n8n_data:
  postgres_data:
  redis_data:

networks:
  n8n-network:
    driver: bridge
