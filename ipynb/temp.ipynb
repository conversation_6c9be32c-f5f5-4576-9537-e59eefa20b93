{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "False\n", "False\n"]}], "source": ["from datetime import datetime\n", "\n", "def validate_date_range(start_date: str, end_date: str) -> bool:\n", "    \"\"\"\n", "    验证两个日期字符串的间隔是否不超过3天\n", "    \n", "    Args:\n", "        start_date: 开始日期字符串，格式为'YYYYMMDD'\n", "        end_date: 结束日期字符串，格式为'YYYYMMDD'\n", "    \n", "    Returns:\n", "        bool: 如果日期间隔不超过3天返回True，否则返回False\n", "        \n", "    Raises:\n", "        ValueError: 如果日期格式不正确\n", "    \"\"\"\n", "    try:\n", "        # 将字符串转换为datetime对象\n", "        start = datetime.strptime(start_date, '%Y%m%d')\n", "        end = datetime.strptime(end_date, '%Y%m%d')\n", "        \n", "        # 计算日期差\n", "        delta = end - start\n", "        \n", "        # 检查日期差是否不超过3天，且开始日期不大于结束日期\n", "        return 0 <= delta.days <= 3\n", "        \n", "    except ValueError:\n", "        raise ValueError(\"日期格式不正确，请使用'YYYYMMDD'格式\")\n", "\n", "# 使用示例\n", "try:\n", "    # 正确示例\n", "    print(validate_date_range('20241201', '20241203'))  # True\n", "    print(validate_date_range('20241201', '20241205'))  # False\n", "    \n", "    # 错误格式示例\n", "    print(validate_date_range('2024120', '20241205'))  # 将抛出异常\n", "    \n", "except ValueError as e:\n", "    print(f\"错误：{e}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}