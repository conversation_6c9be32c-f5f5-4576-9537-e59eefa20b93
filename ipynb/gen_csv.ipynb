{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已生成 CSV 文件：output.csv，共 20000 行数据\n"]}], "source": ["import csv\n", "\n", "# 定义输出文件名\n", "output_file = 'output.csv'\n", "\n", "# 定义表头\n", "headers = ['test1', 'test2', 'test3']\n", "\n", "# 生成数据并写入CSV文件\n", "with open(output_file, 'w', newline='') as f:\n", "    writer = csv.writer(f)\n", "    \n", "    # 写入表头\n", "    writer.writerow(headers)\n", "    \n", "    # 生成20000行数据\n", "    for i in range(20000):\n", "        # 每行的数字都是连续的\n", "        row = [i*3 + 1, i*3 + 2, i*3 + 3]\n", "        writer.writerow(row)\n", "\n", "print(f\"已生成 CSV 文件：{output_file}，共 20000 行数据\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已生成 CSV 文件：output.csv，共 20000 行数据\n", "已生成 JSONL 文件：output.jsonl，共 20000 行数据\n"]}], "source": ["import csv\n", "import json\n", "import random\n", "\n", "# 定义输出文件名\n", "csv_file = 'output.csv'\n", "jsonl_file = 'output.jsonl'\n", "\n", "# 生成一些示例问答对\n", "sample_queries = [\n", "    \"你好，请问今天天气怎么样？\",\n", "    \"如何学习Python编程？\",\n", "    \"什么是人工智能？\",\n", "    \"推荐一些好看的电影\",\n", "    \"如何保持健康的作息？\"\n", "]\n", "\n", "sample_answers = [\n", "    \"今天天气晴朗，气温适宜，很适合外出活动。\",\n", "    \"学习Python可以从基础语法开始，多看教程和实践练习。\",\n", "    \"人工智能是模拟人类智能的计算机系统，能够学习和解决问题。\",\n", "    \"最近很多好评的电影有《奥本海默》、《疯狂动物城》等。\",\n", "    \"建议保持规律作息，早睡早起，适量运动。\"\n", "]\n", "\n", "# 生成JSONL文件\n", "with open(jsonl_file, 'w', encoding='utf-8') as f:\n", "    for i in range(20000):\n", "        # 随机选择问答对\n", "        idx = random.randint(0, len(sample_queries)-1)\n", "        data = {\n", "            \"query\": sample_queries[idx],\n", "            \"answer\": sample_answers[idx],\n", "            \"round\": random.ran<PERSON>t(1, 5),\n", "            \"chat_type\": random.randint(0, 2)\n", "        }\n", "        f.write(json.dumps(data, ensure_ascii=False) + '\\n')\n", "\n", "print(f\"已生成 CSV 文件：{csv_file}，共 20000 行数据\")\n", "print(f\"已生成 JSONL 文件：{jsonl_file}，共 20000 行数据\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["解码结果: 解码错误: 'utf-8' codec can't decode byte 0xd8 in position 1: invalid continuation byte\n"]}], "source": ["import base64\n", "\n", "# 解码base64字符串\n", "def decode_base64(encoded_string):\n", "    try:\n", "        # 解码base64\n", "        decoded_bytes = base64.b64decode(encoded_string)\n", "        # 将字节转换为字符串\n", "        decoded_string = decoded_bytes.decode('utf-8')\n", "        return decoded_string\n", "    except Exception as e:\n", "        return f\"解码错误: {str(e)}\"\n", "\n", "# 使用示例\n", "encoded_data = \"Gdjmmy6JDWWlD8Z2QjhjvA==\"\n", "decoded_result = decode_base64(encoded_data)\n", "print(f\"解码结果: {decoded_result}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32mFINISHED\u001b[0m\n", "Ellapsed time: 3.5148088932037354 s\n"]}], "source": ["import time\n", "from termcolor import colored\n", "start = time.time()\n", "number = 0\n", "for i in range(100000000):\n", "    number += i\n", "    \n", "print(colored(\"FINISHED\", \"green\"))\n", "print(f\"Ellapsed time: {time.time() - start} s\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}