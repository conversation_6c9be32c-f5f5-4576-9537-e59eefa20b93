{"query": "你好，请问今天天气怎么样？", "answer": "今天前期清凉，气温适宜，很适合外出活动。", "round": 5, "chat_type": 2}
{"query": "什么是人工智能 么？", "answer": "人工智能是模拟人类智能的计算机系统，能够学习和解决问题。", "round": 2, "chat_type": 2}
{"query": "什么是人工智能？", "answer": "人工智能是模拟人类智能的计算机系统，能够学习和解决问题。", "round": 2, "chat_type": 2}
{"query": "如何保持健康的作息？", "answer": "建议保持规律作息，早睡早起，适量运动。", "round": 3, "chat_type": 2}
{"query": "推荐一些好看的电影", "answer": "最近很多好评的电影有《奥本海默》、《疯狂动物城》等。", "round": 5, "chat_type": 0}
{"query": "什么是人工智能？", "answer": "人工智能是模拟人类智能的计算机系统，能够学习和解决问题。", "round": 2, "chat_type": 0}
{"query": "如何学习Python编程？", "answer": "学习Python可以从基础语法开始，多看教程和实践练习。", "round": 4, "chat_type": 2}
{"query": "推荐一些好看的电影", "answer": "最近很多好评的电影有《奥本海默》、《疯狂动物城》等。", "round": 1, "chat_type": 0}
{"query": "如何学习Python编程？", "answer": "学习Python可以从基础语法开始，多看教程和实践练习。", "round": 3, "chat_type": 2}
{"query": "如何保持健康的作息？", "answer": "建议保持规律作息，早睡早起，适量运动。", "round": 1, "chat_type": 2}