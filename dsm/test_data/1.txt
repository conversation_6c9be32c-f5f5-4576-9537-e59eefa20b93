#!/bin/bash
check_afs_agent_alive(){
    ps -ef|grep afs_agent|grep -v grep
    if [ $? -ne 0 ];then
        echo "afs_aent not found! reStart afs_agent"
        cd /home/<USER>/data_aggregate/afs/afs_agent/
        bash ./start_agent.sh
        cd -
    fi
}
check_afs_agent_alive

spark_bin='/home/<USER>/data_aggregate/spark/spark/bin'
export LOCAL_DIRS=/home/<USER>/data_aggregate/spark/spark/temp
export YARN_CONF_DIR=/home/<USER>/data_aggregate/spark/spark/conf
d=`date +"%Y-%m-%d"`
echo $*
cd $spark_bin
d=`date +"%Y-%m-%d"`
./spark-submit --master yarn --deploy-mode cluster --class com.baidu.dataeng.ProducedData --num-executors 10 --conf spark.driver.memory=16g --conf spark.executor.memory=10g  /home/<USER>/data_aggregate/spark/spark/data_aggregate/data_aggregate-assembly-1.0.jar $* >/dev/null
exit

#!/bin/bash
run_env=$1
sampleRate=$2
date=$3
qianfan_log_path=$4
data_aggregate_path=/home/<USER>/data_aggregate
export LOCAL_DIRS=$data_aggregate_path/spark/spark/temp
export YARN_CONF_DIR=$data_aggregate_path/spark/spark/conf
$data_aggregate_path/spark/spark/bin/spark-submit --master yarn \
              --deploy-mode cluster \
              --conf spark.executor.cores=1 \
              --conf spark.executor.instances=80 \
              --conf spark.driver.memory=2G \
              --conf spark.executor.memory=1G \
              --conf spark.yarn.maxAppAttempts=1 \
              --name "QianFanLogEtl" \
              --class com.baidu.kgeng.qianfanlog.QianFanLogEtl \
              $data_aggregate_path/app/qianfan-log-spark-1.0-SNAPSHOT-jar-with-dependencies.jar \
              $run_env $sampleRate $date $qianfan_log_path \



/home/<USER>/data_aggregate/app/qianfan_log_etl.sh online 5 20241129 qianfan_log_temp