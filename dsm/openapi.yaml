openapi: 3.1.0
info:
  title: 数据存储管理服务 API
  description: 提供文件、目录、数据集和向量检索等数据存储管理功能
  version: 1.0.0
servers:
  - url: /api/v1
tags:
  - name: 目录操作
    description: 目录相关API
  - name: 文件操作
    description: 文件相关API
  - name: 标签操作
    description: 标签相关API
  - name: 数据集操作
    description: 数据集相关API
  - name: BOS操作
    description: BOS云存储相关API
  - name: 向量检索
    description: 向量检索相关API
  - name: 内部接口
    description: 系统内部使用的接口

paths:
  /dir/create:
    post:
      tags:
        - 目录操作
      summary: 创建目录
      description: 在指定项目和父目录下创建新目录
      operationId: dirCreate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dir_name
                - project_id
              properties:
                dir_name:
                  type: string
                  description: 目录名称
                parent_dir_id:
                  type: integer
                  format: int64
                  description: 父目录ID，若不指定则创建在根目录下
                project_id:
                  type: integer
                  format: int64
                  description: 项目ID
      responses:
        '200':
          description: 成功创建目录
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      dir_id:
                        type: integer
                        format: int64
                        description: 目录ID
                      op_permission:
                        type: integer
                        format: int8
                        description: 操作权限，1表示读写
                      user_permission:
                        type: integer
                        format: int8
                        description: 用户权限

  /dir/del:
    post:
      tags:
        - 目录操作
      summary: 删除目录
      description: 删除指定目录
      operationId: dirDelete
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dir_id
              properties:
                dir_id:
                  type: integer
                  format: int64
                  description: 目录ID
      responses:
        '200':
          description: 成功删除目录
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /dir/rename:
    post:
      tags:
        - 目录操作
      summary: 重命名目录
      description: 重命名指定目录
      operationId: dirRename
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dir_id
                - dir_name
              properties:
                dir_id:
                  type: integer
                  format: int64
                  description: 目录ID
                dir_name:
                  type: string
                  description: 新的目录名称
      responses:
        '200':
          description: 成功重命名目录
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /dir/tree:
    get:
      tags:
        - 目录操作
      summary: 获取目录树
      description: 获取项目的目录树结构
      operationId: dirTree
      parameters:
        - name: project_id
          in: query
          required: true
          schema:
            type: integer
            format: int64
            description: 项目ID
        - name: dir_id
          in: query
          required: false
          schema:
            type: integer
            format: int64
            description: 目录ID，若不指定则获取整个项目的目录树
      responses:
        '200':
          description: 成功获取目录树
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/DirNode'

  /dir/list_files:
    get:
      tags:
        - 目录操作
      summary: 列出目录下的文件
      description: 获取指定目录下的文件列表
      operationId: dirListFiles
      parameters:
        - name: dir_id
          in: query
          required: true
          schema:
            type: integer
            format: int64
            description: 目录ID
      responses:
        '200':
          description: 成功获取文件列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/FileInfo'

  /file/create:
    post:
      tags:
        - 文件操作
      summary: 创建文件
      description: 在指定目录下创建文件（同步操作）
      operationId: fileCreate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FileCreateRequest'
      responses:
        '200':
          description: 成功创建文件
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    $ref: '#/components/schemas/FileCreateResponse'

  /file/create_batch:
    post:
      tags:
        - 文件操作
      summary: 批量创建文件
      description: 在指定目录下批量创建文件（异步操作）
      operationId: fileCreateBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dir_id
                - files
              properties:
                dir_id:
                  type: integer
                  format: int64
                  description: 目录ID
                files:
                  type: array
                  items:
                    $ref: '#/components/schemas/FileInput'
      responses:
        '200':
          description: 成功提交文件创建任务
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      file_ids:
                        type: array
                        items:
                          type: integer
                          format: int64
                        description: 生成的文件ID列表

  /dataset/create:
    post:
      tags:
        - 数据集操作
      summary: 创建数据集
      description: 创建一个新的数据集
      operationId: datasetCreate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - project_id
                - dataset_name
                - creation_type
              properties:
                project_id:
                  type: integer
                  format: int64
                  description: 项目ID
                dataset_name:
                  type: string
                  maxLength: 50
                  description: 数据集名称
                description:
                  type: string
                  maxLength: 300
                  description: 数据集描述
                creation_type:
                  type: string
                  enum: [upload, select]
                  description: 创建类型，upload表示上传文件，select表示选择已有文件
                import_type:
                  type: string
                  enum: [move, copy]
                  description: 导入类型，move表示移动文件，copy表示复制文件
                files:
                  type: array
                  items:
                    $ref: '#/components/schemas/FileInput'
                  description: 要上传的文件列表
                selected_dirs:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: 选择的目录ID列表
                selected_files:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: 选择的文件ID列表
      responses:
        '200':
          description: 成功创建数据集
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      project_id:
                        type: integer
                        format: int64
                        description: 项目ID
                      dataset_id:
                        type: integer
                        format: int64
                        description: 数据集ID
                      dataset_name:
                        type: string
                        description: 数据集名称
                      description:
                        type: string
                        description: 数据集描述
                      creation_type:
                        type: string
                        description: 创建类型

  /dataset/obj_add:
    post:
      tags:
        - 数据集操作
      summary: 向数据集添加对象
      description: 向指定数据集添加文件或目录
      operationId: datasetObjAdd
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dir_id
                - creation_type
              properties:
                dir_id:
                  type: integer
                  format: int64
                  description: 目录ID
                creation_type:
                  type: string
                  enum: [upload, select]
                  description: 创建类型，upload表示上传文件，select表示选择已有文件
                files:
                  type: array
                  items:
                    $ref: '#/components/schemas/FileInput'
                  description: 要上传的文件列表
                selected_dirs:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: 选择的目录ID列表
                selected_files:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: 选择的文件ID列表
      responses:
        '200':
          description: 成功添加对象
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /dataset/check_name:
    get:
      tags:
        - 数据集操作
      summary: 检查数据集名称
      description: 检查数据集名称是否已存在
      operationId: datasetCheckName
      parameters:
        - name: dataset_name
          in: query
          required: true
          schema:
            type: string
            description: 数据集名称
        - name: project_id
          in: query
          required: true
          schema:
            type: integer
            format: int64
            description: 项目ID
      responses:
        '200':
          description: 成功检查数据集名称
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      exists:
                        type: boolean
                        description: 名称是否已存在

  /dataset/meta:
    get:
      tags:
        - 数据集操作
      summary: 获取数据集元数据
      description: 获取数据集的详细信息
      operationId: datasetMeta
      parameters:
        - name: dataset_id
          in: query
          required: true
          schema:
            type: integer
            format: int64
            description: 数据集ID
        - name: project_id
          in: query
          required: false
          schema:
            type: integer
            format: int64
            description: 项目ID
        - name: dataset_version_id
          in: query
          required: false
          schema:
            type: integer
            format: int64
            description: 数据集版本ID
      responses:
        '200':
          description: 成功获取数据集元数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    $ref: '#/components/schemas/DatasetInfo'

  /dataset/meta_modify:
    post:
      tags:
        - 数据集操作
      summary: 修改数据集元数据
      description: 修改数据集的详细信息
      operationId: datasetMetaModify
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dataset_id
              properties:
                dataset_id:
                  type: integer
                  format: int64
                  description: 数据集ID
                dataset_name:
                  type: string
                  maxLength: 50
                  description: 数据集名称
                description:
                  type: string
                  maxLength: 300
                  description: 数据集描述
      responses:
        '200':
          description: 成功修改数据集元数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /dataset/list:
    get:
      tags:
        - 数据集操作
      summary: 获取数据集列表
      description: 获取数据集列表，支持分页和筛选
      operationId: datasetList
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            format: int64
            minimum: 1
            default: 1
            description: 页码
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            format: int64
            minimum: 1
            default: 10
            description: 每页数量
        - name: project_id
          in: query
          required: false
          schema:
            type: integer
            format: int64
            description: 项目ID
        - name: from_market
          in: query
          required: false
          schema:
            type: boolean
            default: false
            description: 是否从数据集市场获取
        - name: key
          in: query
          required: false
          schema:
            type: string
            description: 搜索关键词
        - name: data_types
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
            description: 数据类型，多选
        - name: data_classifications
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
            description: 数据分类，多选
        - name: chat_types
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
            description: 专项分类，多选
        - name: languages
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
            description: 语言，多选
      responses:
        '200':
          description: 成功获取数据集列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/DatasetInfo'
                        description: 数据集列表
                      page:
                        type: integer
                        format: int64
                        description: 当前页码
                      page_size:
                        type: integer
                        format: int64
                        description: 每页数量
                      total:
                        type: integer
                        format: int64
                        description: 总数量

  /dataset/obj_list:
    get:
      tags:
        - 数据集操作
      summary: 获取数据集对象列表
      description: 获取数据集中的文件和目录列表
      operationId: datasetObjList
      parameters:
        - name: dataset_id
          in: query
          required: true
          schema:
            type: integer
            format: int64
            description: 数据集ID
        - name: dir_id
          in: query
          required: false
          schema:
            type: integer
            format: int64
            description: 目录ID
      responses:
        '200':
          description: 成功获取数据集对象列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/DirAndFileInfo'
                        description: 对象列表

  /dataset/delete:
    post:
      tags:
        - 数据集操作
      summary: 删除数据集
      description: 删除指定数据集
      operationId: datasetDelete
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dataset_id
              properties:
                dataset_id:
                  type: integer
                  format: int64
                  description: 数据集ID
      responses:
        '200':
          description: 成功删除数据集
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /dataset/rename:
    post:
      tags:
        - 数据集操作
      summary: 重命名数据集
      description: 重命名指定数据集
      operationId: datasetRename
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dataset_id
                - dataset_name
              properties:
                dataset_id:
                  type: integer
                  format: int64
                  description: 数据集ID
                dataset_name:
                  type: string
                  maxLength: 50
                  description: 新的数据集名称
      responses:
        '200':
          description: 成功重命名数据集
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /dataset/op_logs:
    get:
      tags:
        - 数据集操作
      summary: 获取数据集操作日志
      description: 获取数据集的操作日志
      operationId: datasetOpLogs
      parameters:
        - name: dataset_id
          in: query
          required: true
          schema:
            type: integer
            format: int64
            description: 数据集ID
        - name: page
          in: query
          required: false
          schema:
            type: integer
            format: int64
            minimum: 1
            default: 1
            description: 页码
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            format: int64
            minimum: 1
            default: 10
            description: 每页数量
      responses:
        '200':
          description: 成功获取数据集操作日志
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          type: object
                          properties:
                            op_id:
                              type: integer
                              format: int64
                              description: 操作ID
                            op_type:
                              type: string
                              description: 操作类型
                            op_desc:
                              type: string
                              description: 操作描述
                            op_user:
                              type: string
                              description: 操作用户
                            op_time:
                              type: integer
                              format: int64
                              description: 操作时间
                        description: 操作日志列表
                      page:
                        type: integer
                        format: int64
                        description: 当前页码
                      page_size:
                        type: integer
                        format: int64
                        description: 每页数量
                      total:
                        type: integer
                        format: int64
                        description: 总数量

  /dataset/version/create:
    post:
      tags:
        - 数据集操作
      summary: 创建数据集版本
      description: 创建数据集的新版本
      operationId: datasetVersionCreate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dataset_version_id
              properties:
                dataset_version_id:
                  type: integer
                  format: int64
                  description: 数据集版本ID
      responses:
        '200':
          description: 成功创建数据集版本
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      dataset_id:
                        type: integer
                        format: int64
                        description: 数据集ID
                      dataset_version_id:
                        type: integer
                        format: int64
                        description: 数据集版本ID
                      version_number:
                        type: string
                        description: 版本号
                      dir_id:
                        type: integer
                        format: int64
                        description: 目录ID

  /dataset/version/publish:
    post:
      tags:
        - 数据集操作
      summary: 发布数据集版本
      description: 发布数据集的指定版本
      operationId: datasetVersionPublish
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dataset_id
                - dataset_version_id
                - description
              properties:
                dataset_id:
                  type: integer
                  format: int64
                  description: 数据集ID
                dataset_version_id:
                  type: integer
                  format: int64
                  description: 数据集版本ID
                description:
                  type: string
                  maxLength: 300
                  description: 版本描述
      responses:
        '200':
          description: 成功发布数据集版本
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /file/rename:
    post:
      tags:
        - 文件操作
      summary: 重命名文件
      description: 重命名指定文件
      operationId: fileRename
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - file_id
                - file_name
              properties:
                file_id:
                  type: integer
                  format: int64
                  description: 文件ID
                file_name:
                  type: string
                  description: 新的文件名
      responses:
        '200':
          description: 成功重命名文件
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /file/check_name:
    get:
      tags:
        - 文件操作
      summary: 检查文件名
      description: 检查文件名是否可用
      operationId: fileCheckName
      parameters:
        - name: file_name
          in: query
          required: true
          schema:
            type: string
            description: 文件名
        - name: dir_id
          in: query
          required: true
          schema:
            type: integer
            format: int64
            description: 目录ID
      responses:
        '200':
          description: 成功检查文件名
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      exists:
                        type: boolean
                        description: 名称是否已存在

  /file/del:
    post:
      tags:
        - 文件操作
      summary: 删除文件
      description: 删除指定文件
      operationId: fileDel
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - file_id
              properties:
                file_id:
                  type: integer
                  format: int64
                  description: 文件ID
      responses:
        '200':
          description: 成功删除文件
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /file/move:
    post:
      tags:
        - 文件操作
      summary: 移动文件
      description: 将文件移动到指定目录
      operationId: fileMove
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - file_id
                - target_dir_id
              properties:
                file_id:
                  type: integer
                  format: int64
                  description: 文件ID
                target_dir_id:
                  type: integer
                  format: int64
                  description: 目标目录ID
      responses:
        '200':
          description: 成功移动文件
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /file/meta:
    get:
      tags:
        - 文件操作
      summary: 获取文件元数据
      description: 获取文件的详细信息
      operationId: fileMeta
      parameters:
        - name: file_id
          in: query
          required: true
          schema:
            type: integer
            format: int64
            description: 文件ID
      responses:
        '200':
          description: 成功获取文件元数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    $ref: '#/components/schemas/FileInfo'

  /file/meta_modify:
    post:
      tags:
        - 文件操作
      summary: 修改文件元数据
      description: 修改文件的详细信息
      operationId: fileMetaModify
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - file_id
              properties:
                file_id:
                  type: integer
                  format: int64
                  description: 文件ID
                file_name:
                  type: string
                  description: 文件名
                desc:
                  type: string
                  maxLength: 300
                  description: 文件描述
                type:
                  type: string
                  enum: [text, image, audio, log, video, other]
                  description: 文件类型
                format:
                  type: string
                  enum: [sft, rm, dpo, ppo, pretrain, other]
                  description: 文件格式
                purpose:
                  type: string
                  enum: [analysis, storage, other]
                  description: 文件用途
                tags:
                  type: array
                  items:
                    type: string
                  description: 标签列表
      responses:
        '200':
          description: 成功修改文件元数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /file/view:
    post:
      tags:
        - 文件操作
      summary: 预览文件
      description: 预览文件内容
      operationId: fileView
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - file_id
              properties:
                file_id:
                  type: integer
                  format: int64
                  description: 文件ID
                page:
                  type: integer
                  description: 页码，从1开始
                page_size:
                  type: integer
                  description: 每页行数
      responses:
        '200':
          description: 成功预览文件
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      content:
                        type: string
                        description: 文件内容
                      total:
                        type: integer
                        description: 总行数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页行数
                      schema:
                        type: array
                        items:
                          $ref: '#/components/schemas/Column'
                        description: 结构化数据的schema

  /tag/add:
    post:
      tags:
        - 标签操作
      summary: 添加标签
      description: 给文件添加标签
      operationId: tagAdd
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - file_id
                - tag_name
              properties:
                file_id:
                  type: integer
                  format: int64
                  description: 文件ID
                tag_name:
                  type: string
                  description: 标签名称
      responses:
        '200':
          description: 成功添加标签
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /tag/del:
    post:
      tags:
        - 标签操作
      summary: 删除标签
      description: 删除文件的标签
      operationId: tagDel
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - file_id
                - tag_name
              properties:
                file_id:
                  type: integer
                  format: int64
                  description: 文件ID
                tag_name:
                  type: string
                  description: 标签名称
      responses:
        '200':
          description: 成功删除标签
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object

  /dsm/bos/generate_sts:
    get:
      tags:
        - BOS操作
      summary: 生成临时安全凭证
      description: 生成访问BOS的临时安全凭证
      operationId: generateSts
      responses:
        '200':
          description: 成功生成临时安全凭证
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      access_key_id:
                        type: string
                        description: 访问密钥ID
                      secret_access_key:
                        type: string
                        description: 秘密访问密钥
                      session_token:
                        type: string
                        description: 会话令牌
                      expiration:
                        type: string
                        format: date-time
                        description: 过期时间

  /bos/extend_period:
    get:
      tags:
        - BOS操作
      summary: 延长临时安全凭证的有效期
      description: 延长BOS临时安全凭证的有效期
      operationId: bosExtendPeriod
      responses:
        '200':
          description: 成功延长临时安全凭证的有效期
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      access_key_id:
                        type: string
                        description: 访问密钥ID
                      secret_access_key:
                        type: string
                        description: 秘密访问密钥
                      session_token:
                        type: string
                        description: 会话令牌
                      expiration:
                        type: string
                        format: date-time
                        description: 过期时间

  /auth_check:
    get:
      tags:
        - 向量检索
      summary: 检查向量检索权限
      description: 检查用户是否有向量检索功能的权限
      operationId: authCheck
      responses:
        '200':
          description: 成功检查权限
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      has_auth:
                        type: boolean
                        description: 是否有权限

  /batch_search:
    post:
      tags:
        - 向量检索
      summary: 批量向量检索
      description: 执行批量向量检索
      operationId: batchSearch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - limit
              properties:
                query_type:
                  type: integer
                  format: int8
                  enum: [0, 1]
                  description: 查询类型，0表示直接提供查询列表，1表示通过文件提供查询列表
                query_list:
                  type: array
                  items:
                    type: string
                  minItems: 1
                  maxItems: 10
                  description: 查询列表
                file_id:
                  type: string
                  description: 文件ID
                search_onbord_data:
                  type: integer
                  format: int8
                  enum: [0, 1]
                  description: 是否搜索已上线数据
                modal_type:
                  type: string
                  enum: [text, multi_modal]
                  description: 模态类型
                data_type_list:
                  type: array
                  items:
                    type: string
                  description: 数据类型列表
                dataset_list:
                  type: array
                  items:
                    type: string
                  description: 数据集列表
                limit:
                  type: integer
                  format: int8
                  minimum: 1
                  maximum: 10
                  description: 每个查询返回的结果数量限制
                is_multi_turn:
                  type: integer
                  format: int8
                  enum: [0, 1, 2]
                  description: 是否多轮查询
                page:
                  type: integer
                  minimum: 1
                  description: 页码
                page_size:
                  type: integer
                  minimum: 1
                  description: 每页大小
      responses:
        '200':
          description: 成功执行批量向量检索
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      total:
                        type: integer
                        format: int64
                        description: 总结果数
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/VSResult'
                        description: 结果列表

  /list_onboard_dataset_info:
    get:
      tags:
        - 向量检索
      summary: 列出已上线的数据集信息
      description: 获取已上线的数据集信息列表
      operationId: listOnboardDatasetInfo
      responses:
        '200':
          description: 成功获取已上线的数据集信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/VSDatasetInfo'
                        description: 数据集信息列表

  /list_onboard_project_info:
    get:
      tags:
        - 向量检索
      summary: 列出已上线的项目信息
      description: 获取已上线的项目信息列表
      operationId: listOnboardProjectInfo
      responses:
        '200':
          description: 成功获取已上线的项目信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          type: object
                          properties:
                            project:
                              type: string
                              description: 项目名称
                            tags:
                              type: array
                              items:
                                type: string
                              description: 标签列表
                        description: 项目信息列表

  /query_file_upload:
    post:
      tags:
        - 向量检索
      summary: 上传查询文件
      description: 上传用于向量检索的查询文件
      operationId: uploadFile
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file_data
              properties:
                file_data:
                  type: string
                  format: binary
                  description: 文件数据
      responses:
        '200':
          description: 成功上传查询文件
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码，0表示成功
                  message:
                    type: string
                    description: 状态信息
                  data:
                    type: object
                    properties:
                      file_id:
                        type: string
                        description: 文件ID

components:
  schemas:
    DirNode:
      type: object
      properties:
        dir_id:
          type: integer
          format: int64
          description: 目录ID
        dir_name:
          type: string
          description: 目录名称
        op_permission:
          type: integer
          format: int8
          description: 操作权限
        user_permission:
          type: integer
          format: int8
          description: 用户权限
        has_files:
          type: boolean
          description: 是否包含文件
        create_time:
          type: integer
          format: int64
          description: 创建时间
        update_time:
          type: integer
          format: int64
          description: 更新时间
        children:
          type: array
          items:
            $ref: '#/components/schemas/DirNode'
          description: 子目录
        files:
          type: array
          items:
            $ref: '#/components/schemas/FileInfo'
          description: 目录下的文件

    FileInfo:
      type: object
      properties:
        project_id:
          type: integer
          format: int64
          description: 项目ID
        dataset_id:
          type: integer
          format: int64
          description: 数据集ID
        dataset_version_id:
          type: integer
          format: int64
          description: 数据集版本ID
        file_id:
          type: integer
          format: int64
          description: 文件ID
        file_name:
          type: string
          description: 文件名
        source:
          type: string
          description: 文件来源
        type:
          type: string
          description: 文件类型
        format:
          type: string
          description: 文件格式
        purpose:
          type: string
          description: 文件用途
        desc:
          type: string
          description: 文件描述
        file_format:
          type: string
          description: 文件格式详情
        file_line:
          type: integer
          format: int64
          description: 文件行数
        file_size:
          type: integer
          format: int64
          description: 文件大小
        afs_path:
          type: string
          description: AFS路径
        op_permission:
          type: integer
          format: int8
          description: 操作权限
        user_permission:
          type: integer
          format: int8
          description: 用户权限
        tags:
          type: array
          items:
            type: string
          description: 标签列表
        is_view:
          type: integer
          format: int8
          description: 是否可查看
        create_time:
          type: integer
          format: int64
          description: 创建时间
        update_time:
          type: integer
          format: int64
          description: 更新时间
        dir_id:
          type: integer
          format: int64
          description: 所属目录ID
        path:
          type: string
          description: 文件路径
        url:
          type: string
          description: 文件URL
        bos_key:
          type: string
          description: BOS键值
        bos_bucket:
          type: string
          description: BOS存储桶
        upload_type:
          type: string
          description: 上传类型
        security_level:
          type: integer
          format: int8
          description: 安全等级
        import_status:
          type: integer
          format: int8
          description: 导入状态
        import_fail_msg:
          type: string
          description: 导入失败信息

    FileInput:
      type: object
      required:
        - file_name
      properties:
        upload_type:
          type: string
          description: 上传方式 url或local
          enum: [url, local]
        file_name:
          type: string
          description: 文件名
        dir_id:
          type: integer
          format: int64
          description: 目录ID
        type:
          type: string
          description: 文件类型
          enum: [text, image, audio, log, video, other]
        format:
          type: string
          description: 文件格式
          enum: [sft, rm, dpo, ppo, pretrain, other]
        purpose:
          type: string
          description: 文件用途
          enum: [analysis, storage, other]
        desc:
          type: string
          description: 文件描述
          maxLength: 300
        tags:
          type: array
          items:
            type: string
          description: 标签列表
        source:
          type: string
          description: 来源
        bos_key:
          type: string
          description: 当upload_type为local时使用
        url:
          type: string
          description: 当upload_type为url时使用
        security_level:
          type: integer
          format: int8
          description: 安全等级
        afs_path:
          type: string
          description: AFS路径

    FileCreateRequest:
      type: object
      properties:
        upload_type:
          type: string
          description: 上传方式 url或local
          enum: [url, local]
        file_name:
          type: string
          description: 文件名
        dir_id:
          type: integer
          format: int64
          description: 目录ID
        type:
          type: string
          description: 文件类型
          enum: [text, image, audio, log, video, other]
        format:
          type: string
          description: 文件格式
          enum: [sft, rm, dpo, ppo, pretrain, other]
        purpose:
          type: string
          description: 文件用途
          enum: [analysis, storage, other]
        desc:
          type: string
          description: 文件描述
          maxLength: 300
        tags:
          type: array
          items:
            type: string
          description: 标签列表
        bos_key:
          type: string
          description: 当upload_type为local时使用
        url:
          type: string
          description: 当upload_type为url时使用
        security_level:
          type: integer
          format: int8
          description: 安全等级
        afs_path:
          type: string
          description: AFS路径

    FileCreateResponse:
      type: object
      properties:
        file_id:
          type: integer
          format: int64
          description: 文件ID
        file_name:
          type: string
          description: 文件名
        desc:
          type: string
          description: 文件描述
        purpose:
          type: string
          description: 文件用途
        source:
          type: string
          description: 文件来源
        type:
          type: string
          description: 文件类型
        dir_id:
          type: integer
          format: int64
          description: 目录ID
        format:
          type: string
          description: 文件格式
        bos_key:
          type: string
          description: BOS键值

    DatasetInfo:
      type: object
      properties:
        project_id:
          type: integer
          format: int64
          description: 项目ID
        dataset_id:
          type: integer
          format: int64
          description: 数据集ID
        dataset_name:
          type: string
          description: 数据集名称
        description:
          type: string
          description: 数据集描述
        create_by:
          type: string
          description: 创建者
        update_by:
          type: string
          description: 更新者
        create_time:
          type: integer
          format: int64
          description: 创建时间
        update_time:
          type: integer
          format: int64
          description: 更新时间
        user_permission:
          type: integer
          format: int8
          description: 用户权限
        dataset_version_id:
          type: integer
          format: int64
          description: 数据集版本ID
        dataset_version_number:
          type: string
          description: 数据集版本号
        publish_status:
          type: integer
          format: int8
          description: 发布状态
        file_type_counts:
          type: object
          additionalProperties:
            type: integer
            format: int64
          description: 各类型文件数量
        versions:
          type: array
          items:
            $ref: '#/components/schemas/DatasetVersionInfo'
          description: 版本列表
        is_public:
          type: boolean
          description: 是否公开
        update_mode:
          type: string
          description: 更新模式
        security_level:
          type: integer
          format: int8
          description: 安全等级
        chat_types:
          type: array
          items:
            type: string
          description: 专项分类
        data_tags:
          type: array
          items:
            type: string
          description: 数据标签
        languages:
          type: array
          items:
            type: string
          description: 语言
        data_classifications:
          type: array
          items:
            type: string
          description: 数据分类
        data_type:
          type: string
          description: 数据类型
        public_desc:
          type: string
          description: 公开描述
        ku_url:
          type: string
          description: ku链接
        last_public_time:
          type: integer
          format: int64
          description: 最后公开时间
        usage_count:
          type: integer
          format: int64
          description: 使用次数
        apply_count:
          type: integer
          format: int64
          description: 申请次数
        has_permission:
          type: integer
          format: int8
          description: 是否有权限
        apply_status:
          type: integer
          format: int8
          description: 申请状态
        dataset_admins:
          type: array
          items:
            type: string
          description: 数据集管理员列表

    DatasetVersionInfo:
      type: object
      properties:
        dataset_version_id:
          type: integer
          format: int64
          description: 数据集版本ID
        version_number:
          type: string
          description: 版本号
        source_version_id:
          type: integer
          format: int64
          description: 源版本ID
        user_permission:
          type: integer
          format: int8
          description: 用户权限
        dir_id:
          type: integer
          format: int64
          description: 目录ID
        status:
          type: integer
          format: int8
          description: 状态
        description:
          type: string
          description: 描述
        is_file_importing:
          type: boolean
          description: 是否有文件正在导入
        create_by:
          type: string
          description: 创建者
        update_by:
          type: string
          description: 更新者
        create_time:
          type: integer
          format: int64
          description: 创建时间
        update_time:
          type: integer
          format: int64
          description: 更新时间

    DirAndFileInfo:
      type: object
      properties:
        is_dir:
          type: integer
          format: int8
          description: 是否为目录，1表示目录，0表示文件
        id:
          type: integer
          format: int64
          description: ID（目录ID或文件ID）
        name:
          type: string
          description: 名称
        op_permission:
          type: integer
          format: int8
          description: 操作权限
        file_size:
          type: integer
          format: int64
          description: 文件大小
        file_lines:
          type: integer
          format: int64
          description: 文件行数
        file_format:
          type: string
          description: 文件格式
        format:
          type: string
          description: 格式
        type:
          type: string
          description: 类型
        user_name:
          type: string
          description: 用户名
        create_time:
          type: integer
          format: int64
          description: 创建时间
        update_time:
          type: integer
          format: int64
          description: 更新时间
        user_permission:
          type: integer
          format: int8
          description: 用户权限
        is_viewable:
          type: boolean
          description: 是否可查看
        is_structured:
          type: boolean
          description: 是否为结构化数据
        security_level:
          type: integer
          format: int8
          description: 安全等级
        tags:
          type: array
          items:
            type: string
          description: 标签列表
        import_status:
          type: integer
          format: int8
          description: 导入状态
        import_fail_msg:
          type: string
          description: 导入失败信息

    Column:
      type: object
      properties:
        name:
          type: string
          description: 列名
        dtype:
          type: string
          description: 数据类型

    VSResult:
      type: object
      properties:
        query:
          type: string
          description: 查询
        results:
          type: array
          items:
            type: object
            properties:
              score:
                type: number
                format: float
                description: 相似度分数
              content:
                type: string
                description: 内容
              dataset_id:
                type: string
                description: 数据集ID
              metadata:
                type: object
                additionalProperties: true
                description: 元数据
          description: 检索结果

    VSDatasetInfo:
      type: object
      properties:
        dataset_id:
          type: string
          description: 数据集ID
        project_name:
          type: string
          description: 项目名称
        modal_type:
          type: integer
          format: int8
          description: 模态类型
        tag_name:
          type: string
          description: 标签名称
        data_type:
          type: string
          description: 数据类型
        bos_url:
          type: string
          description: BOS URL
        source:
          type: integer
          format: int8
          description: 来源
        create_time:
          type: integer
          format: int64
          description: 创建时间
        update_time:
          type: integer
          format: int64
          description: 更新时间
