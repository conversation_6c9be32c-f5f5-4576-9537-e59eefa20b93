-- 1. 找出所有符合条件的数据集ID
SET @dataset_ids = (
    SELECT GROUP_CONCAT(dataset_id) 
    FROM dataset 
    WHERE dataset_name LIKE 'test_permission_dataset%'
);

-- 2. 删除数据集权限关联表中的记录
DELETE FROM dataset_permission_relation 
WHERE FIND_IN_SET(dataset_id, @dataset_ids);

-- 3. 删除数据集权限行为记录表中的记录
DELETE FROM dataset_permission_record 
WHERE FIND_IN_SET(dataset_id, @dataset_ids);

-- 4. 删除操作日志表中的记录
DELETE FROM op_log 
WHERE FIND_IN_SET(dataset_id, @dataset_ids);

-- 5. 找出相关数据集版本ID
SET @dataset_version_ids = (
    SELECT GROUP_CONCAT(dataset_version_id) 
    FROM dataset_version 
    WHERE FIND_IN_SET(dataset_id, @dataset_ids)
);

-- 6. 删除操作日志表中与这些版本相关的记录
DELETE FROM op_log 
WHERE FIND_IN_SET(dataset_version_id, @dataset_version_ids);

-- 7. 找出所有相关的目录ID
SET @dir_ids = (
    SELECT GROUP_CONCAT(dir_id) 
    FROM dataset_version 
    WHERE FIND_IN_SET(dataset_id, @dataset_ids)
);

-- 8. 找出所有与数据集相关的文件夹ID(包括子文件夹)
CREATE TEMPORARY TABLE temp_dir_ids AS (
    WITH RECURSIVE dir_tree AS (
        -- 初始目录(根目录)
        SELECT dir_id FROM obj_dir WHERE FIND_IN_SET(dir_id, @dir_ids)
        UNION ALL
        -- 子目录
        SELECT child.dir_id
        FROM obj_dir child
        JOIN dir_tree parent ON child.parent_dir_id = parent.dir_id
    )
    SELECT dir_id FROM dir_tree
);

SET @all_dir_ids = (SELECT GROUP_CONCAT(dir_id) FROM temp_dir_ids);

-- 9. 找出所有相关文件ID
SET @file_ids = (
    SELECT GROUP_CONCAT(file_id)
    FROM obj_file
    WHERE FIND_IN_SET(dir_id, @all_dir_ids) OR FIND_IN_SET(dataset_id, @dataset_ids)
);

-- 10. 删除文件标签
DELETE FROM obj_tag
WHERE FIND_IN_SET(file_id, @file_ids);

-- 11. 删除文件与算子关联
DELETE FROM con_file_openg
WHERE FIND_IN_SET(file_id, @file_ids);

-- 12. 删除文件
DELETE FROM obj_file
WHERE FIND_IN_SET(dataset_id, @dataset_ids) OR FIND_IN_SET(dir_id, @all_dir_ids);

-- 13. 删除目录
DELETE FROM obj_dir
WHERE FIND_IN_SET(dataset_id, @dataset_ids) OR FIND_IN_SET(dir_id, @all_dir_ids);

-- 14. 删除数据集版本
DELETE FROM dataset_version
WHERE FIND_IN_SET(dataset_id, @dataset_ids);

-- 15. 最后删除数据集
DELETE FROM dataset
WHERE dataset_name LIKE 'test_permission_dataset%';

-- 16. 删除临时表
DROP TEMPORARY TABLE IF EXISTS temp_dir_ids;