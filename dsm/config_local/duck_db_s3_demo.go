package duck_db

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	_ "github.com/marcboeker/go-duckdb"
	"icode.baidu.com/baidu/dataeng/data-storage-manager/model/dao/rpc_bos/bosselect"
)

// QueryRequest 定义 API 查询请求结构
type QueryRequest struct {
	Sql string `json:"sql"`
}

// QueryResponse 定义 API 查询响应结构
type QueryResponse struct {
	Columns []string        `json:"columns"`
	Rows    [][]interface{} `json:"rows"`
	Error   string          `json:"error,omitempty"`
}

// 支持的文件类型常量
const (
	FileTypeParquet = "parquet"
	FileTypeCSV     = "csv"
	FileTypeJSON    = "json"
	FileTypeJSONL   = "jsonl"
	FileTypeExcel   = "xlsx"
)

// DBManager 管理 DuckDB 数据库连接和操作
type DBManager struct {
	db *sql.DB
}

// NewDBManager 创建并初始化 DuckDB 管理器，从 S3 加载数据
// duck_db.NewDBManager(fmt.Sprintf("s3://%s/%s", bucket, ser.InputData.BosKey), ak, sk, "su1", "s3.su.bcebos.com")
func NewDBManager(s3Url, s3AccessKey, s3SecretKey, s3Region string, s3Endpoint string) (*DBManager, error) {
	log.Printf("初始化 DuckDB 管理器，文件URL: %s, 端点: %s", s3Url, s3Endpoint)

	// 初始化 DuckDB，连接到内存数据库
	db, err := sql.Open("duckdb", "")
	if err != nil {
		return nil, fmt.Errorf("failed to open duckdb: %v", err)
	}

	// 安装并加载必要扩展
	extensionQuery := "INSTALL httpfs; LOAD httpfs; INSTALL parquet; LOAD parquet; INSTALL json; LOAD json;"
	log.Printf("加载DuckDB扩展: %s", extensionQuery)
	_, err = db.Exec(extensionQuery)
	if err != nil {
		return nil, fmt.Errorf("failed to install/load extensions: %v", err)
	}

	// 配置 S3 访问参数
	configQuery := `
    SET s3_access_key_id = '%s';
    SET s3_secret_access_key = '%s';
    SET s3_region = '%s';
    SET s3_endpoint = '%s';
    SET s3_url_style = 'path';
`
	log.Printf("配置S3参数: endpoint=%s, region=%s", s3Endpoint, s3Region)
	_, err = db.Exec(fmt.Sprintf(configQuery, s3AccessKey, s3SecretKey, s3Region, s3Endpoint))

	if err != nil {
		return nil, fmt.Errorf("failed to configure S3 access: %v", err)
	}

	// 尝试检查文件是否存在
	fileExistsQuery := fmt.Sprintf("SELECT count(*) FROM http_request('%s', Method='HEAD');", s3Url)
	log.Printf("检查文件是否存在: %s", fileExistsQuery)
	var exists int
	err = db.QueryRow(fileExistsQuery).Scan(&exists)
	if err != nil {
		log.Printf("检查文件存在性失败 (可能不支持HEAD请求): %v", err)
	} else {
		log.Printf("文件存在检查结果: %d", exists)
	}

	// 尝试获取文件内容样本
	peekFileContent(db, s3Url)

	// 根据文件类型创建相应的视图
	fileType := detectFileType(s3Url)
	log.Printf("检测到文件类型: %s，原始URL: %s", fileType, s3Url)

	err = createViewForFileType(db, "data", s3Url, fileType)
	if err != nil {
		// 如果是JSONL文件并且创建视图失败，尝试其他读取方式
		if fileType == FileTypeJSONL {
			log.Printf("JSONL视图创建失败，尝试其他读取方式")
			err = tryAlternativeJSONLMethods(db, "data", s3Url)
			if err != nil {
				return nil, fmt.Errorf("all attempts to create JSONL view failed: %v", err)
			}
		} else {
			return nil, fmt.Errorf("failed to create view: %v", err)
		}
	}

	// 测试查询视图是否有数据
	testQuery := "SELECT count(*) FROM data"
	log.Printf("执行测试查询: %s", testQuery)
	var count int
	err = db.QueryRow(testQuery).Scan(&count)
	if err != nil {
		log.Printf("测试查询出错: %v", err)
	} else {
		log.Printf("视图中的数据行数: %d", count)
	}

	return &DBManager{db: db}, nil
}

// peekFileContent 尝试获取文件内容的前几行用于诊断
func peekFileContent(db *sql.DB, fileUrl string) {
	// 创建一个临时表来存储文件内容
	createTableQuery := "CREATE OR REPLACE TABLE temp_content AS SELECT * FROM read_text_auto('" + fileUrl + "', 'AUTO', 5);"
	log.Printf("创建临时表以查看文件内容: %s", createTableQuery)
	_, err := db.Exec(createTableQuery)
	if err != nil {
		log.Printf("创建临时内容表失败: %v", err)
		return
	}

	// 获取内容
	rows, err := db.Query("SELECT * FROM temp_content LIMIT 5;")
	if err != nil {
		log.Printf("查询文件内容失败: %v", err)
		return
	}
	defer rows.Close()

	// 输出文件内容样本
	log.Printf("文件内容样本 (最多5行):")
	columns, _ := rows.Columns()
	count := 0
	for rows.Next() {
		count++
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			log.Printf("  行 %d 扫描失败: %v", count, err)
			continue
		}

		for i, v := range values {
			if v != nil {
				log.Printf("  行 %d 列 %s: %v", count, columns[i], v)
			}
		}
	}

	if count == 0 {
		log.Printf("  未能读取到任何内容")
	}

	// 删除临时表
	db.Exec("DROP TABLE IF EXISTS temp_content;")
}

// tryAlternativeJSONLMethods 尝试不同的方法读取JSONL文件
func tryAlternativeJSONLMethods(db *sql.DB, viewName, fileUrl string) error {
	methods := []struct {
		name  string
		query string
	}{
		{"read_json_auto", fmt.Sprintf("CREATE VIEW %s AS SELECT * FROM read_json_auto('%s');", viewName, fileUrl)},
		{"read_json_records", fmt.Sprintf("CREATE VIEW %s AS SELECT * FROM read_json('%s', format='newline_delimited');", viewName, fileUrl)},
		{"read_json_auto_detect", fmt.Sprintf("CREATE VIEW %s AS SELECT * FROM read_json('%s', format='auto');", viewName, fileUrl)},
		{"read_csv_auto", fmt.Sprintf("CREATE VIEW %s AS SELECT * FROM read_csv_auto('%s', delim='', header=false, columns={'value': 'JSON'});", viewName, fileUrl)},
	}

	// 先尝试删除可能已存在的视图
	dropQuery := fmt.Sprintf("DROP VIEW IF EXISTS %s;", viewName)
	_, err := db.Exec(dropQuery)
	if err != nil {
		log.Printf("删除现有视图失败: %v", err)
		// 继续尝试，不返回错误
	}

	// 尝试每种方法
	for _, method := range methods {
		log.Printf("尝试使用 %s 方法读取JSONL: %s", method.name, method.query)
		_, err := db.Exec(method.query)
		if err != nil {
			log.Printf("方法 %s 失败: %v", method.name, err)
			// 如果失败，继续尝试下一个方法
			continue
		}

		// 测试视图是否有数据
		var count int
		countQuery := fmt.Sprintf("SELECT count(*) FROM %s", viewName)
		err = db.QueryRow(countQuery).Scan(&count)
		if err != nil {
			log.Printf("方法 %s 创建视图成功但查询失败: %v", method.name, err)
			// 删除视图并尝试下一个方法
			db.Exec(dropQuery)
			continue
		}

		log.Printf("方法 %s 成功，数据行数: %d", method.name, count)
		if count > 0 {
			return nil // 找到可用方法
		}

		// 视图为空，删除并尝试下一个方法
		db.Exec(dropQuery)
	}

	return fmt.Errorf("所有JSONL读取方法都失败")
}

// detectFileType 根据文件扩展名检测文件类型
func detectFileType(fileUrl string) string {
	fileType := bosselect.GetFileFileFormat(fileUrl)
	log.Printf("bosselect.GetFileFileFormat检测结果: %s", fileType)

	switch fileType {
	case FileTypeParquet:
		return FileTypeParquet
	case "csv":
		return FileTypeCSV
	case "json":
		return FileTypeJSON
	case "jsonl":
		return FileTypeJSONL
	case "xlsx", "xls":
		return FileTypeExcel
	default:
		log.Printf("未识别的文件类型: %s，默认使用Parquet", fileType)
		return FileTypeParquet // 默认使用 Parquet
	}
}

// createViewForFileType 根据不同文件类型创建适当的视图
func createViewForFileType(db *sql.DB, viewName, fileUrl, fileType string) error {
	var query string

	switch fileType {
	case FileTypeParquet:
		query = fmt.Sprintf("CREATE VIEW %s AS SELECT * FROM read_parquet('%s');", viewName, fileUrl)
	case FileTypeCSV:
		query = fmt.Sprintf("CREATE VIEW %s AS SELECT * FROM read_csv_auto('%s');", viewName, fileUrl)
	case FileTypeJSON:
		query = fmt.Sprintf("CREATE VIEW %s AS SELECT * FROM read_json_auto('%s');", viewName, fileUrl)
	case FileTypeJSONL:
		// 对JSONL文件尝试使用更可靠的格式设置
		query = fmt.Sprintf("CREATE VIEW %s AS SELECT * FROM read_json('%s', format='auto', records=true);", viewName, fileUrl)
	case FileTypeExcel:
		query = fmt.Sprintf("CREATE VIEW %s AS SELECT * FROM read_excel('%s');", viewName, fileUrl)
	default:
		return fmt.Errorf("unsupported file type: %s", fileType)
	}

	log.Printf("创建视图SQL: %s", query)
	_, err := db.Exec(query)
	if err != nil {
		log.Printf("创建视图失败: %v", err)
	} else {
		log.Printf("创建视图成功")

		// 检查视图是否包含数据
		var count int
		countQuery := fmt.Sprintf("SELECT count(*) FROM %s", viewName)
		err = db.QueryRow(countQuery).Scan(&count)
		if err != nil {
			log.Printf("视图创建成功但查询失败: %v", err)
		} else {
			log.Printf("视图包含 %d 行数据", count)
			if count == 0 {
				log.Printf("警告: 视图创建成功但不包含数据")
			}
		}
	}
	return err
}

// Close 关闭数据库连接
func (m *DBManager) Close() error {
	return m.db.Close()
}

// QueryData 执行 SQL 查询并返回结果
func (m *DBManager) QueryData(sqlQuery string) (*QueryResponse, error) {
	log.Printf("执行SQL查询: %s", sqlQuery)

	rows, err := m.db.Query(sqlQuery)
	if err != nil {
		log.Printf("查询执行失败: %v", err)
		return &QueryResponse{Error: err.Error()}, nil
	}
	defer rows.Close()

	// 获取列名
	columns, err := rows.Columns()
	if err != nil {
		log.Printf("获取列名失败: %v", err)
		return &QueryResponse{Error: err.Error()}, nil
	}
	log.Printf("查询结果列: %v", columns)

	// 准备存储结果
	var resultRows [][]interface{}
	rowCount := 0
	for rows.Next() {
		rowCount++
		// 动态创建扫描目标
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// 扫描一行数据
		if err := rows.Scan(valuePtrs...); err != nil {
			log.Printf("扫描行数据失败: %v", err)
			return &QueryResponse{Error: err.Error()}, nil
		}

		// 转换为通用类型
		row := make([]interface{}, len(columns))
		for i, v := range values {
			row[i] = v
		}
		resultRows = append(resultRows, row)
	}

	log.Printf("查询结果行数: %d", rowCount)
	if rowCount == 0 {
		log.Printf("警告: 查询结果为空")
	}

	return &QueryResponse{
		Columns: columns,
		Rows:    resultRows,
	}, nil
}

// queryHandler 处理 API 查询请求
func (m *DBManager) queryHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req QueryRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// 执行查询
	resp, err := m.QueryData(req.Sql)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回 JSON 响应
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(resp); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}
}
