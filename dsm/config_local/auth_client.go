package auth

import (
	"context"
	"net/http"
	"time"

	"icode.baidu.com/baidu/dataeng/data-gdp-library/base/httpserver/controller"
	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/uuap"

	"icode.baidu.com/baidu/dataeng/data-storage-manager/library/resource"
)

type contextKey string

const (
	UsernameKey contextKey = "X-USERNAME"
)

func CommonExecute(ctx context.Context, req ghttp.Request, service base.ServiceInterface[any, any]) (ret ghttp.Response) {
	// user := GetUserFromUUAP(ctx, req)
	user := &uuap.SSOUser{
		UserName: "system",
	}
	if user == nil {
		return ghttp.NewJSONResponse(403, &lib_error.CustomErr{Code: lib_error.UserNotLogin, Msg: "user not login"})
	}
	if user.UserName == "" {
		return ghttp.NewJSONResponse(403, &lib_error.CustomErr{Code: lib_error.UserNotLogin, Msg: "uuap username is empty"})
	}
	ctx = context.WithValue(ctx, UsernameKey, user.UserName)
	return controller.CommonExecute(ctx, req, service)
}

// GetUserName 从context中获取已缓存的 username 一般不需要判空
func GetUserName(ctx context.Context) string {
	value := ctx.Value(UsernameKey)
	if value == nil {
		return ""
	}
	username, ok := value.(string)
	if !ok {
		return ""
	}
	return username
}

func SetMockUserName(ctx context.Context, userName string) context.Context {
	return context.WithValue(ctx, UsernameKey, userName)
}

// GetUserFromUUAP 一般情况下由CommonExecute调用，服务内部获取 username 直接使用 GetUserName
func GetUserFromUUAP(ctx context.Context, req ghttp.Request) *uuap.SSOUser {

	//openapi用户名从header获取
	userName := ctx.Value(UsernameKey)
	if userName != nil {
		return &uuap.SSOUser{
			UserName: userName.(string),
		}
	}
	//UUAP_S_TOKEN 是线上 stoken,  UUAP_S_TOKEN_OFFLINE 是线下 stoken
	//前端控制跳转登录,会携带ptoken,ticket参数(用户信息需要根据ptoken与stoken共同生成),stoken需要根据ticket调用uuap的STokenDecrypt方法去生成
	//目前在项目中还没有办法识别test与prod环境,暂时在代码中同时获取线上与线下stoken,如果没有获取到,ticket也没有直接返回,如果ticket有值,生成stoken
	//用户第一次登录用ticket生成stoken,后续登录直接使用stoken
	cookieCtx := ctx.Value("cookie")
	cookie := &http.Cookie{}
	if cookieCtx != nil {
		cookie = cookieCtx.(*http.Cookie)
	}
	sso := uuap.DefaultSSO
	offLineCookie, _ := req.HTTPRequest().Cookie("UUAP_S_TOKEN_OFFLINE")
	onLineCookie, _ := req.HTTPRequest().Cookie("UUAP_S_TOKEN")
	sToken := ""
	if offLineCookie == nil && onLineCookie == nil {
		ticket := req.HTTPRequest().Header["Uuap-Ticket"]
		if len(ticket) == 0 {
			resource.LoggerService.Warning(ctx, "user ticket empty", logit.String("err", "ticket not found"))
			return nil
		}
		var decryptErr error
		sToken, decryptErr = sso.STokenDecrypt(ctx, ticket[0])
		if decryptErr != nil {
			resource.LoggerService.Error(ctx, "process stoken err"+decryptErr.Error(), logit.String("err", decryptErr.Error()))
			return nil
		}
		if sToken != "" {
			WrapperCookie(sToken, cookie)
			req.HTTPRequest().AddCookie(cookie)
			user, err := sso.Validate(ctx, req.HTTPRequest())
			return ValidUser(ctx, user, err)
		}
	} else {
		if onLineCookie != nil {
			ticket := req.HTTPRequest().Header["Uuap-Ticket"]
			if len(ticket) != 0 {
				//如果stoken与ticket同时存在,为了防止stoken过期,先用ticket更新stoken
				var decryptErr error
				sToken, decryptErr = sso.STokenDecrypt(ctx, ticket[0])
				if decryptErr != nil {
					resource.LoggerService.Error(ctx, "process stoken err"+decryptErr.Error(), logit.String("err", decryptErr.Error()))
					return nil
				}
				if sToken != "" {
					WrapperCookie(sToken, cookie)
					newRequest := new(http.Request)
					newRequest.Header = make(http.Header)
					newRequest.AddCookie(cookie)
					//获取pToken
					pToken, _ := req.HTTPRequest().Cookie("UUAP_P_TOKEN")
					if pToken == nil {
						resource.LoggerService.Warning(ctx, "pToken is nil")
						return nil
					}
					newRequest.AddCookie(pToken)
					user, err := sso.Validate(ctx, newRequest)
					return ValidUser(ctx, user, err)
				}
			} else {
				user, err := sso.Validate(ctx, req.HTTPRequest())
				return ValidUser(ctx, user, err)
			}
		}
	}
	return nil
}

func WrapperCookie(sToken string, cookie *http.Cookie) {
	cookie.Name = "UUAP_S_TOKEN"
	cookie.Value = sToken
	cookie.MaxAge = 86400 * 30
	cookie.Path = "/"
	cookie.HttpOnly = true
	cookie.Expires = time.Now().Add(30 * 24 * time.Hour)
}
func ValidUser(ctx context.Context, user *uuap.SSOUser, err error) *uuap.SSOUser {
	if err != nil {
		resource.LoggerService.Error(ctx, "user validate error", logit.String("err", err.Error()))
		return nil
	}
	logit.AddAllLevel(ctx, logit.AutoField("uuap_user_name", user.UserName))
	return user
}
