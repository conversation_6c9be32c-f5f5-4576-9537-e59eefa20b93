package main

import (
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

// Dynamic SQL
type Querier interface {
	// SELECT * FROM @@table WHERE name = @name{{if role !=""}} AND role = @role{{end}}
	FilterWithNameAndRole(name, role string) ([]gen.T, error)
}

func main() {
	g := gen.NewGenerator(gen.Config{
		OutPath: "script/db/query",
		Mode:    gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
	})

	gormdb, _ := gorm.Open(mysql.Open("root:dataeng_test@(127.0.0.1:8036)/data_storage_mge?charset=utf8mb4&parseTime=True&loc=Local"))
	g.UseDB(gormdb) // reuse your gorm db

	// 获取数据库中所有表
	tables, _ := gormdb.Migrator().GetTables()

	// 为每个表生成对应的模型和查询代码
	models := make([]interface{}, len(tables))
	for i, table := range tables {
		// 生成模型并保存到 models 切片中
		models[i] = g.GenerateModel(table)
	}

	// 应用自定义查询接口到生成的模型
	g.ApplyInterface(func(Querier) {}, models...)

	// 生成代码
	g.Execute()
}
