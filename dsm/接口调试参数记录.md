
### /api/v1/dsm/dir/create 创建目录

```json
{
    "dir_name": "dir-01",
    "parent_dir_id": 0,
    "project_id": 15
}

{
    "code": 0,
    "message": "success",
    "data": {
        "dir_id": 862
    },
    "ext": null,
    "request_id": "3159918804",
    "request_time": "2024-12-24 13:34:11"
}


```
### /api/v1/dsm/dir/create 继续创建子目录
```json

{
    "dir_name": "dir-01-01",
    "parent_dir_id": 862,
    "project_id": 15
}

{
    "code": 0,
    "message": "success",
    "data": {
        "dir_id": 863
    },
    "ext": null,
    "request_id": "3159918805",
    "request_time": "2024-12-24 13:34:46"
}


{
    "dir_name": "dir-01-02",
    "parent_dir_id": 862,
    "project_id": 15
}

{
    "code": 0,
    "message": "success",
    "data": {
        "dir_id": 864
    },
    "ext": null,
    "request_id": "3159918806",
    "request_time": "2024-12-24 13:35:22"
}
```
### /api/v1/dsm/dir/tree 全量获取目录树
param: project_id=15
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "data": [
            {
                "dir_id": 862,
                "dir_name": "dir-01",
                "op_permission": 1,
                "user_permission": 1,
                "children": [
                    {
                        "dir_id": 863,
                        "dir_name": "dir-01-01",
                        "op_permission": 1,
                        "user_permission": 1,
                        "children": [],
                        "files": null
                    },
                    {
                        "dir_id": 864,
                        "dir_name": "dir-01-02",
                        "op_permission": 1,
                        "user_permission": 1,
                        "children": [],
                        "files": null
                    }
                ],
                "files": null
            }
        ]
    },
    "ext": null,
    "request_id": "3159918807",
    "request_time": "2024-12-24 13:35:47"
}
```

### /api/v1/dsm/file/create 创建文件
分别在 862 863 864 目录下创建文件
```json
{
    "project_id": 15,
    "upload_type": "url",
    "file_name": "2.jsonl",
    "dir_id": 862,
    "source": "user",
    "type": "text",
    "format": "sft",
    "purpose": "analysis",
    "url":"http://localhost:8000/2.jsonl",
    "bos_key": "test/view_test.csv",
    "tags": ["12","1","3","20"]
}

```
### /api/v1/dsm/dir/list_files 获取目录下文件列表
dir_id:862 
project_id:15 
page:1 
page_size:100
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "total": 5,
        "list": [
            {
                "file_id": 1196,
                "file_name": "2-4.jsonl",
                "source": "user",
                "type": "text",
                "format": "sft",
                "purpose": "analysis",
                "desc": "",
                "file_format": "jsonl",
                "file_line": 0,
                "file_size": 368913,
                "op_permission": 1,
                "user_permission": 1,
                "tags": [
                    "12",
                    "1",
                    "3",
                    "20"
                ],
                "is_view": 1,
                "create_time": 1734990230
            },
            {
                "file_id": 1195,
                "file_name": "2-3.jsonl",
                "source": "user",
                "type": "text",
                "format": "sft",
                "purpose": "analysis",
                "desc": "",
                "file_format": "jsonl",
                "file_line": 0,
                "file_size": 368913,
                "op_permission": 1,
                "user_permission": 1,
                "tags": [
                    "12",
                    "1",
                    "3",
                    "20"
                ],
                "is_view": 1,
                "create_time": 1734990224
            },
            {
                "file_id": 1194,
                "file_name": "2-2.jsonl",
                "source": "user",
                "type": "text",
                "format": "sft",
                "purpose": "analysis",
                "desc": "",
                "file_format": "jsonl",
                "file_line": 0,
                "file_size": 368913,
                "op_permission": 1,
                "user_permission": 1,
                "tags": [
                    "12",
                    "1",
                    "3",
                    "20"
                ],
                "is_view": 1,
                "create_time": 1734990198
            },
            {
                "file_id": 1193,
                "file_name": "2-1.jsonl",
                "source": "user",
                "type": "text",
                "format": "sft",
                "purpose": "analysis",
                "desc": "",
                "file_format": "jsonl",
                "file_line": 0,
                "file_size": 368913,
                "op_permission": 1,
                "user_permission": 1,
                "tags": [
                    "12",
                    "1",
                    "3",
                    "20"
                ],
                "is_view": 1,
                "create_time": 1734990191
            },
            {
                "file_id": 1192,
                "file_name": "2.jsonl",
                "source": "user",
                "type": "text",
                "format": "sft",
                "purpose": "analysis",
                "desc": "",
                "file_format": "jsonl",
                "file_line": 0,
                "file_size": 368913,
                "op_permission": 1,
                "user_permission": 1,
                "tags": [
                    "12",
                    "1",
                    "3",
                    "20"
                ],
                "is_view": 1,
                "create_time": 1734990168
            }
        ],
        "page": 1,
        "page_size": 10
    },
    "ext": null,
    "request_id": "3159918827",
    "request_time": "2024-12-24 13:50:29"
}
```

### 将其中一个子目录移动到数据集


```plantuml
@startuml
skinparam component {
  BackgroundColor<<API>> LightBlue
  BackgroundColor<<Logic>> LightGreen
  BackgroundColor<<Storage>> LightYellow
}

rectangle "系统架构" {
  [API 层 (RESTful/gRPC)] <<API>>
  
  package "业务逻辑层" <<Logic>> {
    [数据集管理]
    [版本控制]
    [标签系统]
  }
  
  [存储抽象层] <<Storage>>
  [存储引擎层] <<Storage>>
}

[API 层 (RESTful/gRPC)] -down-> [数据集管理]
[API 层 (RESTful/gRPC)] -down-> [版本控制]
[API 层 (RESTful/gRPC)] -down-> [标签系统]

[数据集管理] -down-> [存储抽象层]
[版本控制] -down-> [存储抽象层]
[标签系统] -down-> [存储抽象层]

[存储抽象层] -down-> [存储引擎层]
@enduml

```