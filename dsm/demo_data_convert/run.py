#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目运行脚本
支持多种运行方式，包括Streamlit应用
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
import logging
import time

# 确保项目根目录在系统路径中
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# 配置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("运行")

def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import streamlit
        import pandas
        import matplotlib
        import seaborn
        logger.info("依赖检查通过")
        return True
    except ImportError as e:
        logger.error(f"依赖检查失败: {e}")
        logger.info("请先运行: pip install -r requirements.txt")
        return False

def run_streamlit():
    """运行Streamlit应用"""
    logger.info("启动Streamlit应用...")
    
    # 检查app.py是否存在
    app_path = os.path.join(PROJECT_ROOT, "app.py")
    if not os.path.exists(app_path):
        logger.error(f"应用文件不存在: {app_path}")
        return False
    
    try:
        # 构建Streamlit命令
        cmd = ["streamlit", "run", app_path, "--server.fileWatcherType", "auto"]
        
        # 启动Streamlit
        process = subprocess.Popen(cmd)
        
        # 等待一下，看是否有立即的错误
        time.sleep(2)
        if process.poll() is not None:
            logger.error("Streamlit应用启动失败")
            return False
        
        logger.info("Streamlit应用已启动")
        
        # 等待进程结束
        process.wait()
        return True
    except Exception as e:
        logger.error(f"运行Streamlit出错: {e}")
        return False

def run_init():
    """运行初始化脚本"""
    logger.info("运行初始化脚本...")
    
    init_path = os.path.join(PROJECT_ROOT, "initialize.py")
    if not os.path.exists(init_path):
        logger.error(f"初始化脚本不存在: {init_path}")
        return False
    
    try:
        # 运行初始化脚本
        subprocess.run([sys.executable, init_path])
        logger.info("初始化完成")
        return True
    except Exception as e:
        logger.error(f"初始化出错: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="项目运行脚本")
    
    # 添加参数
    parser.add_argument("--init", action="store_true", help="只运行初始化脚本")
    parser.add_argument("--no-init", action="store_true", help="跳过初始化")
    parser.add_argument("--port", type=int, default=8501, help="Streamlit端口(默认8501)")
    
    args = parser.parse_args()
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 是否仅运行初始化
    if args.init:
        run_init()
        return
    
    # 是否需要初始化
    if not args.no_init:
        run_init()
    
    # 运行Streamlit应用
    run_streamlit()

if __name__ == "__main__":
    main() 