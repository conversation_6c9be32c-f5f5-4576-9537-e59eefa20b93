#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import os
import json
from collections import Counter
import numpy as np
from wordcloud import WordCloud
import jieba
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_data(file_path):
    """加载数据文件"""
    if file_path.endswith('.csv'):
        return pd.read_csv(file_path)
    elif file_path.endswith('.xlsx'):
        return pd.read_excel(file_path)
    else:
        raise ValueError("不支持的文件格式，仅支持CSV和Excel")

def plot_daily_distribution(df, output_dir):
    """绘制每日数据分布图"""
    if 'event_day' not in df.columns:
        logger.warning("数据中没有event_day列，无法绘制每日分布图")
        return
    
    plt.figure(figsize=(12, 6))
    daily_counts = df['event_day'].value_counts().sort_index()
    ax = daily_counts.plot(kind='bar', color='skyblue')
    plt.title('每日数据分布')
    plt.xlabel('日期')
    plt.ylabel('记录数')
    plt.xticks(rotation=45)
    
    # 添加数值标签
    for i, count in enumerate(daily_counts):
        ax.text(i, count + 0.1, str(count), ha='center')
    
    plt.tight_layout()
    output_path = os.path.join(output_dir, 'daily_distribution.png')
    plt.savefig(output_path)
    logger.info(f"每日分布图已保存到 {output_path}")
    plt.close()

def extract_text_from_answers(df):
    """从回答中提取文本内容"""
    if 'answer' not in df.columns:
        logger.warning("数据中没有answer列，无法进行文本分析")
        return []
    
    all_text = ' '.join(df['answer'].fillna('').astype(str))
    return all_text

def create_wordcloud(text, output_dir):
    """创建词云图"""
    # 使用jieba分词
    words = jieba.cut(text)
    word_space_split = ' '.join(words)
    
    # 创建词云
    wordcloud = WordCloud(
        font_path='simhei.ttf',  # 请确保有中文字体
        width=800,
        height=400,
        background_color='white',
        max_words=100,
        max_font_size=150,
        random_state=42
    ).generate(word_space_split)
    
    plt.figure(figsize=(10, 6))
    plt.imshow(wordcloud, interpolation='bilinear')
    plt.axis('off')
    plt.tight_layout()
    
    output_path = os.path.join(output_dir, 'wordcloud.png')
    plt.savefig(output_path)
    logger.info(f"词云图已保存到 {output_path}")
    plt.close()

def analyze_query_length(df, output_dir):
    """分析查询长度分布"""
    if 'query' not in df.columns:
        logger.warning("数据中没有query列，无法分析查询长度")
        return
    
    df['query_length'] = df['query'].fillna('').astype(str).apply(len)
    
    plt.figure(figsize=(10, 6))
    sns.histplot(df['query_length'], bins=30, kde=True)
    plt.title('查询长度分布')
    plt.xlabel('查询长度（字符数）')
    plt.ylabel('频率')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    output_path = os.path.join(output_dir, 'query_length_distribution.png')
    plt.savefig(output_path)
    logger.info(f"查询长度分布图已保存到 {output_path}")
    plt.close()

def analyze_answer_length(df, output_dir):
    """分析回答长度分布"""
    if 'answer' not in df.columns:
        logger.warning("数据中没有answer列，无法分析回答长度")
        return
    
    df['answer_length'] = df['answer'].fillna('').astype(str).apply(len)
    
    plt.figure(figsize=(10, 6))
    sns.histplot(df['answer_length'], bins=30, kde=True)
    plt.title('回答长度分布')
    plt.xlabel('回答长度（字符数）')
    plt.ylabel('频率')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    output_path = os.path.join(output_dir, 'answer_length_distribution.png')
    plt.savefig(output_path)
    logger.info(f"回答长度分布图已保存到 {output_path}")
    plt.close()

def create_summary_report(df, output_dir):
    """创建数据摘要报告"""
    report = {
        "记录总数": len(df),
        "唯一会话数": df['session_id'].nunique() if 'session_id' in df.columns else "N/A",
        "平均查询长度": df['query_length'].mean() if 'query_length' in df.columns else "N/A",
        "平均回答长度": df['answer_length'].mean() if 'answer_length' in df.columns else "N/A",
        "包含图片的记录百分比": (df['chat_files'].str.contains('image').sum() / len(df) * 100) if 'chat_files' in df.columns else "N/A"
    }
    
    output_path = os.path.join(output_dir, 'summary_report.json')
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=4)
    
    logger.info(f"摘要报告已保存到 {output_path}")
    
    # 创建一个简单的文本报告
    text_report_path = os.path.join(output_dir, 'summary_report.txt')
    with open(text_report_path, 'w', encoding='utf-8') as f:
        f.write("===== 数据分析摘要报告 =====\n\n")
        for key, value in report.items():
            if isinstance(value, float):
                f.write(f"{key}: {value:.2f}\n")
            else:
                f.write(f"{key}: {value}\n")
    
    logger.info(f"文本摘要报告已保存到 {text_report_path}")

def main():
    parser = argparse.ArgumentParser(description="数据可视化工具")
    parser.add_argument('--input', '-i', required=True, help='输入数据文件路径（CSV或Excel格式）')
    parser.add_argument('--output-dir', '-o', default='visualizations', help='输出目录（默认: visualizations）')
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载数据
    logger.info(f"正在加载数据: {args.input}")
    df = load_data(args.input)
    logger.info(f"已加载 {len(df)} 条记录")
    
    # 绘制每日分布图
    plot_daily_distribution(df, args.output_dir)
    
    # 分析查询长度
    analyze_query_length(df, args.output_dir)
    
    # 分析回答长度
    analyze_answer_length(df, args.output_dir)
    
    # 创建词云
    logger.info("正在创建词云图")
    text = extract_text_from_answers(df)
    if text:
        create_wordcloud(text, args.output_dir)
    
    # 创建摘要报告
    logger.info("正在创建摘要报告")
    create_summary_report(df, args.output_dir)
    
    logger.info(f"可视化完成！所有图表已保存到目录: {args.output_dir}")

if __name__ == "__main__":
    main() 