# BOS JSONL字段顺序处理工具

这个工具用于处理BOS上的JSONL文件，根据指定的字段顺序调整JSON数据，然后重新上传到原位置。

## 功能特点

- 从BOS下载指定的JSONL文件
- 调整JSON字段顺序，将核心字段（如果存在）按以下顺序放在最前面：
  - `chat_id`
  - `query`
  - `answer`
  - `chat_type`
  - `eda_type`
- 保持其他字段不变
- 将处理后的文件上传回BOS，替换原文件

## 使用前准备

1. 安装所需的Python依赖：

```bash
pip install baidubce
```

2. 配置BOS访问信息：

从样例配置文件创建你的配置文件：

```bash
cp bos_sample_conf.py bos_conf.py
```

然后编辑 `bos_conf.py` 文件，填入您的实际BOS配置信息：

```python
access_key_id = "您的Access Key ID"
secret_access_key = "您的Secret Access Key"
bucket_name = "您的Bucket名称"
endpoint = "bj.bcebos.com"  # 根据需要修改
```

3. 准备工作文件，每行包含一个BOS中的object key。例如：
```
path/to/file1.jsonl
path/to/file2.jsonl
```

## 运行方法

### 方法一：使用运行脚本（推荐）

```bash
python run_process.py
```

按提示输入工作文件路径即可开始处理。

### 方法二：直接使用处理脚本

```bash
python process_bos_jsonl.py --work-file <工作文件路径> --ak <Access Key ID> --sk <Secret Access Key> --bucket <Bucket名称>
```

可选参数：
- `--endpoint <端点>`: 指定BOS服务端点，默认为 `bj.bcebos.com`
- `--temp-dir <临时目录>`: 指定临时文件存储目录，默认为 `temp_files`
- `--log-level <日志级别>`: 指定日志级别，可选值为 DEBUG, INFO, WARNING, ERROR，默认为 INFO

## 注意事项

- 工具会在处理过程中创建临时文件，完成后会自动清理
- 如果处理过程中断，可能需要手动清理临时目录
- 确保您有足够的磁盘空间用于临时文件存储
- 处理大文件时，请确保有足够的内存 