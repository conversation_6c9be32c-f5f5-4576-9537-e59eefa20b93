#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Streamlit主应用
负责初始化应用、加载模块、渲染主界面
"""

import os
import streamlit as st
import tempfile
import sys
from pathlib import Path

# 确保项目根目录在系统路径中
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

from config import settings
from core.logger import logger
from core.utils import ensure_dir
from modules import REGISTERED_MODULES, load_modules

def setup_app():
    """设置应用基本配置"""
    # 设置页面标题、图标、布局
    st.set_page_config(
        page_title=settings.APP_TITLE,
        page_icon=settings.APP_ICON,
        layout=settings.APP_LAYOUT,
        initial_sidebar_state=settings.SIDEBAR_STATE,
    )
    
    # 加载自定义CSS
    _load_custom_css()
    
    # 确保必要的目录存在
    _ensure_directories()
    
    # 记录应用启动
    logger.info(f"应用已启动: {settings.APP_TITLE}")

def _load_custom_css():
    """加载自定义CSS"""
    css_file = os.path.join(settings.STATIC_DIR, "css", "style.css")
    if os.path.exists(css_file):
        with open(css_file, "r", encoding="utf-8") as f:
            st.markdown(f"<style>{f.read()}</style>", unsafe_allow_html=True)
    else:
        logger.warning(f"CSS文件不存在: {css_file}")
        # 使用内联CSS
        st.markdown("""
        <style>
        .stApp {
            max-width: 1200px;
            margin: 0 auto;
        }
        .sidebar .sidebar-content {
            background-color: #f5f7f9;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .stButton>button {
            border-radius: 4px;
            font-weight: 500;
        }
        </style>
        """, unsafe_allow_html=True)

def _ensure_directories():
    """确保必要的目录存在"""
    # 确保静态资源目录存在
    ensure_dir(settings.STATIC_DIR)
    ensure_dir(os.path.join(settings.STATIC_DIR, "css"))
    
    # 确保数据目录存在
    ensure_dir(settings.DATA_DIR)
    
    # 确保模块数据目录存在
    for module_config in settings.MODULES:
        module_data_dir = os.path.join(settings.DATA_DIR, module_config["name"])
        ensure_dir(module_data_dir)
        # 确保各个子目录存在
        ensure_dir(os.path.join(module_data_dir, "temp"))
        ensure_dir(os.path.join(module_data_dir, "results"))

def main():
    """主函数"""
    # 设置应用
    setup_app()
    
    # 重新加载模块（开发时可能会用到）
    load_modules()
    
    # 侧边栏导航
    st.sidebar.title(settings.APP_TITLE)
    
    # 添加图标
    if settings.APP_ICON:
        st.sidebar.image(settings.APP_ICON, width=64)
    
    # 模块选择
    module_names = list(REGISTERED_MODULES.keys())
    
    if not module_names:
        st.error("未找到可用模块，请检查模块配置。")
        return
    
    # 配置模块选择使用单选按钮
    module_options = []
    for module_name in module_names:
        module_config = next((m for m in settings.MODULES if m["name"] == module_name), None)
        if module_config:
            icon = module_config.get("icon", "📦")
            description = module_config.get("description", "")
            option_text = f"{icon} {module_name}"
            module_options.append((module_name, option_text, description))
    
    selected_option = st.sidebar.radio(
        "选择功能模块",
        options=[opt[0] for opt in module_options],
        format_func=lambda x: next((opt[1] for opt in module_options if opt[0] == x), x),
        help="选择要使用的功能模块",
        index=0
    )
    
    # 显示模块描述
    for module_name, _, description in module_options:
        if module_name == selected_option and description:
            st.sidebar.markdown(f"*{description}*")
            break
    
    # 渲染选中的模块
    if selected_option in REGISTERED_MODULES:
        module = REGISTERED_MODULES[selected_option]
        module.render()
    else:
        st.error(f"模块 '{selected_option}' 未找到或无法加载。")
    
    # 添加侧边栏页脚
    st.sidebar.markdown("---")
    st.sidebar.markdown(
        """
        <div style='text-align: center; color: gray; font-size: 0.8em;'>
        由Streamlit提供支持<br>
        © 2023-2024
        </div>
        """,
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main() 