#!/usr/bin/env python
# -*- coding: utf-8 -*-

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import json
import shutil
import uuid
import time
import logging
import requests
import tempfile
import io
import zipfile
from PIL import Image
import subprocess
import base64

# 引入项目中的模块
from data_converter import read_jsonl, convert_to_dataframe, save_to_csv, save_to_excel, extract_image_urls, download_images, analyze_data
from data_visualizer import plot_daily_distribution, analyze_query_length, analyze_answer_length, create_wordcloud

# 配置页面
st.set_page_config(
    page_title="数据转换工具",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 创建临时目录
TEMP_DIR = os.path.join(os.path.dirname(__file__), "streamlit_temp")
RESULTS_DIR = os.path.join(os.path.dirname(__file__), "streamlit_results")
os.makedirs(TEMP_DIR, exist_ok=True)
os.makedirs(RESULTS_DIR, exist_ok=True)

# 设置样式
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    color: #1E88E5;
    text-align: center;
    margin-bottom: 2rem;
}
.sub-header {
    font-size: 1.5rem;
    color: #0D47A1;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}
.info-box {
    background-color: #E3F2FD;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
.success-box {
    background-color: #E8F5E9;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
</style>
""", unsafe_allow_html=True)

# 页面标题
st.markdown('<div class="main-header">JSONL数据转换与可视化工具</div>', unsafe_allow_html=True)

# 创建工作目录
def create_job_dir():
    job_id = str(uuid.uuid4())
    job_dir = os.path.join(RESULTS_DIR, job_id)
    os.makedirs(job_dir, exist_ok=True)
    return job_id, job_dir

# 生成下载链接
def get_binary_file_downloader_html(file_path, file_label='文件'):
    with open(file_path, 'rb') as f:
        data = f.read()
    b64 = base64.b64encode(data).decode()
    href = f'<a href="data:application/octet-stream;base64,{b64}" download="{os.path.basename(file_path)}">{file_label}</a>'
    return href

# 侧边栏
with st.sidebar:
    st.markdown('<div class="sub-header">功能导航</div>', unsafe_allow_html=True)
    
    # 选择功能
    selected_page = st.radio(
        "选择功能：",
        ["数据转换", "数据可视化", "关于项目"]
    )
    
    st.markdown("---")
    st.markdown("### 项目说明")
    st.markdown("这个工具可以帮助你处理JSONL格式的对话数据，转换为CSV或Excel格式，下载对话中包含的图片，并提供数据分析和可视化功能。")
    st.markdown("---")
    st.markdown("### 数据示例")
    
    # 显示示例数据
    if st.button("显示示例数据"):
        if os.path.exists("demo1.jsonl"):
            with open("demo1.jsonl", "r", encoding="utf-8") as f:
                st.code(f.read(), language="json")
        else:
            st.warning("示例文件不存在")

# 数据转换页面
if selected_page == "数据转换":
    st.markdown('<div class="sub-header">JSONL数据转换</div>', unsafe_allow_html=True)
    
    with st.expander("选择转换选项", expanded=True):
        col1, col2 = st.columns(2)
        with col1:
            # 文件上传
            uploaded_file = st.file_uploader("上传JSONL文件", type=["jsonl"])
            output_format = st.selectbox(
                "选择输出格式",
                ["CSV", "Excel", "两者都要"],
                index=0
            )
        
        with col2:
            download_images = st.checkbox("下载图片", value=False)
            analyze_data = st.checkbox("数据分析", value=True)
            
            # 添加一些高级选项 - 修改此处，避免嵌套expander
            st.markdown("#### 高级选项")
            max_image_count = st.slider("最大下载图片数量", 1, 1000, 100)
            timeout = st.slider("下载超时(秒)", 1, 30, 10)
    
    # 处理按钮
    if uploaded_file is not None:
        if st.button("开始处理"):
            with st.spinner("正在处理数据..."):
                try:
                    # 创建任务目录
                    job_id, job_dir = create_job_dir()
                    
                    # 保存上传的文件
                    input_file_path = os.path.join(job_dir, uploaded_file.name)
                    with open(input_file_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())
                    
                    # 读取数据
                    data = read_jsonl(input_file_path)
                    df = convert_to_dataframe(data)
                    
                    results = {}
                    
                    # 保存为CSV
                    if output_format in ["CSV", "两者都要"]:
                        csv_path = os.path.join(job_dir, "data.csv")
                        save_to_csv(df, csv_path)
                        results["CSV"] = csv_path
                    
                    # 保存为Excel
                    if output_format in ["Excel", "两者都要"]:
                        excel_path = os.path.join(job_dir, "data.xlsx")
                        save_to_excel(df, excel_path)
                        results["Excel"] = excel_path
                    
                    # 下载图片
                    if download_images:
                        image_dir = os.path.join(job_dir, "images")
                        os.makedirs(image_dir, exist_ok=True)
                        image_urls = extract_image_urls(data)
                        
                        # 限制下载数量
                        image_urls = image_urls[:max_image_count]
                        
                        if image_urls:
                            with st.status("正在下载图片...") as status:
                                download_images(image_urls, image_dir)
                                status.update(label="图片下载完成！", state="complete")
                            
                            # 创建图片目录的zip文件
                            shutil.make_archive(os.path.join(job_dir, "images"), 'zip', image_dir)
                            results["图片(ZIP)"] = f"{job_dir}/images.zip"
                    
                    # 分析数据
                    if analyze_data:
                        stats = analyze_data(df)
                        stats_path = os.path.join(job_dir, "stats.json")
                        with open(stats_path, 'w', encoding='utf-8') as f:
                            json.dump(stats, f, ensure_ascii=False, indent=4)
                        results["统计数据(JSON)"] = stats_path
                        
                        # 显示分析结果
                        st.markdown('<div class="sub-header">数据分析结果</div>', unsafe_allow_html=True)
                        st.json(stats)
                    
                    # 显示处理结果和下载链接
                    st.markdown('<div class="sub-header">处理完成！</div>', unsafe_allow_html=True)
                    st.markdown('<div class="success-box">数据处理成功，您可以下载以下文件：</div>', unsafe_allow_html=True)
                    
                    for name, path in results.items():
                        st.markdown(f"**{name}文件：** {get_binary_file_downloader_html(path, name+'文件')}", unsafe_allow_html=True)
                    
                    # 预览数据
                    if "CSV" in results or "Excel" in results:
                        st.markdown('<div class="sub-header">数据预览</div>', unsafe_allow_html=True)
                        st.dataframe(df.head(10))
                
                except Exception as e:
                    st.error(f"处理出错: {str(e)}")
    else:
        st.markdown('<div class="info-box">请上传JSONL文件以开始处理</div>', unsafe_allow_html=True)

# 数据可视化页面
elif selected_page == "数据可视化":
    st.markdown('<div class="sub-header">数据可视化</div>', unsafe_allow_html=True)
    
    # 文件上传
    uploaded_file = st.file_uploader("上传CSV或Excel文件", type=["csv", "xlsx"])
    
    if uploaded_file is not None:
        try:
            # 读取数据
            if uploaded_file.name.endswith('.csv'):
                df = pd.read_csv(uploaded_file)
            else:
                df = pd.read_excel(uploaded_file)
            
            # 显示数据预览
            st.markdown('<div class="sub-header">数据预览</div>', unsafe_allow_html=True)
            st.dataframe(df.head(5))
            
            # 创建任务目录
            job_id, job_dir = create_job_dir()
            viz_dir = os.path.join(job_dir, "visualizations")
            os.makedirs(viz_dir, exist_ok=True)
            
            # 可视化选项
            st.markdown('<div class="sub-header">选择可视化选项</div>', unsafe_allow_html=True)
            col1, col2 = st.columns(2)
            
            with col1:
                show_daily_dist = st.checkbox("每日数据分布", value=True)
                show_query_length = st.checkbox("查询长度分布", value=True)
            
            with col2:
                show_answer_length = st.checkbox("回答长度分布", value=True)
                show_wordcloud = st.checkbox("创建词云", value=True)
            
            if st.button("生成可视化"):
                with st.spinner("正在生成可视化图表..."):
                    # 保存数据用于处理
                    temp_csv = os.path.join(viz_dir, "temp_data.csv")
                    df.to_csv(temp_csv, index=False)
                    
                    # 创建可视化
                    if show_daily_dist and 'event_day' in df.columns:
                        plot_daily_distribution(df, viz_dir)
                        st.markdown('<div class="sub-header">每日数据分布</div>', unsafe_allow_html=True)
                        st.image(os.path.join(viz_dir, 'daily_distribution.png'))
                    
                    if show_query_length and 'query' in df.columns:
                        analyze_query_length(df, viz_dir)
                        st.markdown('<div class="sub-header">查询长度分布</div>', unsafe_allow_html=True)
                        st.image(os.path.join(viz_dir, 'query_length_distribution.png'))
                    
                    if show_answer_length and 'answer' in df.columns:
                        analyze_answer_length(df, viz_dir)
                        st.markdown('<div class="sub-header">回答长度分布</div>', unsafe_allow_html=True)
                        st.image(os.path.join(viz_dir, 'answer_length_distribution.png'))
                    
                    if show_wordcloud and 'answer' in df.columns:
                        all_text = ' '.join(df['answer'].fillna('').astype(str))
                        if all_text:
                            create_wordcloud(all_text, viz_dir)
                            st.markdown('<div class="sub-header">词云图</div>', unsafe_allow_html=True)
                            st.image(os.path.join(viz_dir, 'wordcloud.png'))
                    
                    # 创建所有可视化的ZIP包
                    shutil.make_archive(os.path.join(job_dir, "all_visualizations"), 'zip', viz_dir)
                    
                    # 显示下载链接
                    st.markdown('<div class="success-box">可视化生成完成！您可以下载所有图表：</div>', unsafe_allow_html=True)
                    st.markdown(get_binary_file_downloader_html(f"{job_dir}/all_visualizations.zip", "所有可视化图表(ZIP)"), unsafe_allow_html=True)
        
        except Exception as e:
            st.error(f"处理出错: {str(e)}")
    else:
        st.markdown('<div class="info-box">请上传CSV或Excel文件以开始可视化</div>', unsafe_allow_html=True)

# 关于页面
else:
    st.markdown('<div class="sub-header">关于项目</div>', unsafe_allow_html=True)
    
    st.markdown("""
    ## 数据转换工具

    这是一个用于处理和转换数据的Python工具集，特别适用于JSONL格式的对话数据处理。

    ### 功能特点

    - JSONL文件转换为CSV或Excel格式
    - 数据分析和统计功能
    - 支持从对话数据中提取和下载图片
    - 可扩展的数据处理管道
    - Web API服务，无需前端即可使用
    - 友好的Streamlit Web界面

    ### 项目组件

    - **data_converter.py**: JSONL数据转换核心功能
    - **data_visualizer.py**: 数据可视化功能
    - **api_server.py**: FastAPI Web服务
    - **streamlit_app.py**: Streamlit Web界面
    - **run_demo.py**: 命令行演示脚本
    
    ### 用法指南
    
    #### 命令行工具
    ```bash
    python data_converter.py --input demo1.jsonl --output output_data --format both --analyze
    ```
    
    #### FastAPI服务
    ```bash
    python api_server.py
    # 然后访问 http://localhost:8000/docs
    ```
    
    #### Streamlit应用
    ```bash
    streamlit run streamlit_app.py
    ```
    
    ### 项目源码
    
    项目源码托管在GitHub上，可以查看更多信息和贡献代码。
    """)

# 清理旧文件
@st.cache_resource
def cleanup_old_files():
    for dir_path in [TEMP_DIR, RESULTS_DIR]:
        if os.path.exists(dir_path):
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                if os.path.isdir(item_path):
                    # 检查目录是否超过24小时
                    if time.time() - os.path.getmtime(item_path) > 24 * 60 * 60:
                        shutil.rmtree(item_path)
                elif os.path.isfile(item_path):
                    # 检查文件是否超过24小时
                    if time.time() - os.path.getmtime(item_path) > 24 * 60 * 60:
                        os.remove(item_path)

# 启动时执行清理
cleanup_old_files() 