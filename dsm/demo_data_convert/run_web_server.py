#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
启动Web服务器
"""

import os
import sys
import subprocess
import webbrowser
import time
import logging
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import fastapi
        import uvicorn
        import aiofiles
        import python_multipart
        return True
    except ImportError as e:
        logger.error(f"缺少依赖: {e}")
        logger.info("请运行: pip install -r requirements.txt")
        return False

def main():
    parser = argparse.ArgumentParser(description="启动数据转换Web服务")
    parser.add_argument('--port', '-p', type=int, default=8000, help='服务端口（默认: 8000）')
    parser.add_argument('--host', type=str, default="0.0.0.0", help='服务主机（默认: 0.0.0.0）')
    parser.add_argument('--no-browser', action='store_true', help='不自动打开浏览器')
    
    args = parser.parse_args()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 启动服务器
    logger.info(f"正在启动服务器，端口: {args.port}")
    
    # 在新线程中启动浏览器
    if not args.no_browser:
        def open_browser():
            time.sleep(2)  # 等待服务器启动
            url = f"http://localhost:{args.port}/docs"
            logger.info(f"正在浏览器中打开: {url}")
            webbrowser.open(url)
        
        import threading
        threading.Thread(target=open_browser).start()
    
    # 启动服务器
    cmd = [
        f"python -m uvicorn api_server:app",
        f"--host {args.host}",
        f"--port {args.port}",
        "--reload"
    ]
    
    try:
        subprocess.run(" ".join(cmd), shell=True)
    except KeyboardInterrupt:
        logger.info("服务器已停止")

if __name__ == "__main__":
    main() 