#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行演示：完整的数据处理和可视化流程
"""

import os
import subprocess
import logging
import argparse
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(command):
    """执行命令并返回结果"""
    logger.info(f"执行命令: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            encoding='utf-8'
        )
        logger.info(f"命令执行成功: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False

def main():
    parser = argparse.ArgumentParser(description="数据处理和可视化演示")
    parser.add_argument('--input', '-i', default='demo1.jsonl', help='输入JSONL文件路径（默认: demo1.jsonl）')
    parser.add_argument('--output-dir', '-o', default='demo_output', help='输出目录（默认: demo_output）')
    parser.add_argument('--skip-convert', action='store_true', help='跳过数据转换步骤')
    parser.add_argument('--skip-visualize', action='store_true', help='跳过数据可视化步骤')
    parser.add_argument('--download-images', '-d', action='store_true', help='下载图片')
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 步骤1：数据转换
    if not args.skip_convert:
        logger.info("开始数据转换...")
        output_file = os.path.join(args.output_dir, 'converted_data')
        convert_cmd = [
            f"python data_converter.py",
            f"--input {args.input}",
            f"--output {output_file}",
            "--format both",
            "--analyze"
        ]
        
        if args.download_images:
            image_dir = os.path.join(args.output_dir, 'images')
            convert_cmd.append("--download-images")
            convert_cmd.append(f"--image-dir {image_dir}")
        
        convert_result = run_command(" ".join(convert_cmd))
        if not convert_result:
            logger.error("数据转换失败，演示终止")
            sys.exit(1)
    
    # 步骤2：数据可视化
    if not args.skip_visualize:
        logger.info("开始数据可视化...")
        viz_dir = os.path.join(args.output_dir, 'visualizations')
        csv_output = os.path.join(args.output_dir, 'converted_data.csv')
        
        # 检查是否存在转换后的CSV文件
        if not os.path.exists(csv_output):
            logger.error(f"转换后的CSV文件 {csv_output} 不存在")
            logger.error("请先运行数据转换步骤或检查文件路径")
            sys.exit(1)
        
        visualize_cmd = [
            f"python data_visualizer.py",
            f"--input {csv_output}",
            f"--output-dir {viz_dir}"
        ]
        
        visualize_result = run_command(" ".join(visualize_cmd))
        if not visualize_result:
            logger.error("数据可视化失败")
            sys.exit(1)
    
    logger.info("演示完成！")
    logger.info(f"输出目录: {args.output_dir}")
    logger.info(f"- 转换后的CSV文件: {os.path.join(args.output_dir, 'converted_data.csv')}")
    logger.info(f"- 转换后的Excel文件: {os.path.join(args.output_dir, 'converted_data.xlsx')}")
    logger.info(f"- 可视化文件: {os.path.join(args.output_dir, 'visualizations')}")
    
    if args.download_images:
        logger.info(f"- 下载的图片: {os.path.join(args.output_dir, 'images')}")

if __name__ == "__main__":
    main() 