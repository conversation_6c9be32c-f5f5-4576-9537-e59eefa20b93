#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import datetime
import requests
import logging
import time
import urllib.parse
from pathlib import Path
import hashlib
import concurrent.futures
import threading
import queue
import io

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局请求会话，重用连接
session = requests.Session()
session.headers.update({
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
})

# 推迟导入 BOS 相关模块
HAVE_BOS = False
try:
    from baidubce.auth.bce_credentials import BceCredentials
    from baidubce.bce_client_configuration import BceClientConfiguration
    from baidubce.services.bos.bos_client import BosClient
    HAVE_BOS = True
    logger.info("BOS客户端导入成功")
except ImportError:
    logger.warning("BOS模块未导入，图片处理功能不可用")

# 图片处理任务类型
class ImageTask:
    """图片处理任务类，包含处理单个图片所需的所有信息"""
    def __init__(self, line_num, json_field_path, image_url, item):
        self.line_num = line_num  # 行号
        self.json_field_path = json_field_path  # JSON中图片字段的路径
        self.image_url = image_url  # 原始图片URL
        self.item = item  # 原始图片项(字典)
        self.result = None  # 处理结果

class JsonConverter:
    def __init__(self, input_path, output_path, temp_dir="temp_images", field_order=None, threads=8):
        """初始化转换器
        
        Args:
            input_path: 输入JSONL文件路径
            output_path: 输出JSONL文件路径
            temp_dir: 临时存储下载图片的目录
            field_order: 指定输出字段排序的列表，如["chat_id", "query", "chat_files"]
            threads: 工作线程数
        """
        self.input_path = input_path
        self.output_path = output_path
        self.temp_dir = temp_dir
        self.field_order = field_order or []
        self.threads = threads
        
        # 创建临时目录
        Path(temp_dir).mkdir(parents=True, exist_ok=True)
        
        # BOS客户端配置
        self.bos_config = None
        self.bucket_name = None
        self.endpoint = None
        
        # 任务队列
        self.task_queue = queue.Queue(maxsize=1000)  # 图片处理任务队列
        self.result_queue = queue.Queue(maxsize=1000)  # 结果队列
        
        # 运行控制标志
        self.running = False
        self.parsing_complete = False
        
        # 线程锁，用于保护共享资源
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'processed_lines': 0,
            'parsed_json': 0,
            'images_processed': 0,
            'download_errors': 0,
            'upload_errors': 0
        }
        
    def init_bos_client(self, access_key_id, secret_access_key, bucket_name, endpoint="bj.bcebos.com"):
        """初始化BOS客户端配置
        
        Args:
            access_key_id: 百度云存储AK
            secret_access_key: 百度云存储SK
            bucket_name: 存储桶名称
            endpoint: BOS服务端点
        """
        if not HAVE_BOS:
            logger.error("BOS模块未导入")
            return False
            
        try:
            config = BceClientConfiguration(
                credentials=BceCredentials(access_key_id, secret_access_key),
                endpoint=endpoint
            )
            # 设置更高的超时时间和缓冲区大小
            config.connection_timeout_in_mills = 10000  # 10秒
            config.socket_timeout_in_mills = 30000  # 30秒
            config.recv_buf_size = 16 << 20  # 16MB
            config.send_buf_size = 16 << 20  # 16MB
            
            self.bos_config = config
            self.bucket_name = bucket_name
            self.endpoint = endpoint
            
            logger.info(f"BOS配置完成：{bucket_name}")
            
            # 验证存储桶访问权限
            try:
                client = BosClient(self.bos_config)
                response = client.list_objects(
                    bucket_name=bucket_name,
                    prefix="image_store/",
                    max_keys=1
                )
                logger.info(f"存储桶连接成功")
            except Exception as e:
                logger.warning(f"存储桶访问失败: {str(e)}")
            
            return True
        except Exception as e:
            logger.error(f"BOS配置失败: {str(e)}")
            return False
            
    def get_bos_client(self):
        """创建新的BOS客户端
        
        Returns:
            BosClient: BOS客户端实例
        """
        return BosClient(self.bos_config)
            
    def download_image(self, url):
        """下载图片到内存
        
        Args:
            url: 图片URL
            
        Returns:
            tuple: (成功标志, 内容字节)
        """
        try:
            response = session.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            # 检查内容类型
            content_type = response.headers.get('Content-Type', '')
            if not content_type.startswith('image/'):
                logger.warning(f"非图片类型: {content_type}")
            
            # 读取内容到内存
            content = response.content
            
            # 检查是否为空
            if len(content) == 0:
                logger.error(f"下载图片为空")
                return False, None
                
            return True, content
        except Exception as e:
            logger.error(f"图片下载失败: {str(e)}")
            with self._lock:
                self.stats['download_errors'] += 1
            return False, None
    
    def upload_to_bos(self, data, object_key):
        """上传图片到BOS并获取永久URL
        
        Args:
            data: 图片二进制内容
            object_key: BOS对象键
            
        Returns:
            str: 上传成功后的永久URL，失败返回None
        """
        if not self.bos_config:
            logger.error("BOS未初始化")
            return None
            
        try:
            # 创建BOS客户端
            bos_client = self.get_bos_client()
            
            # 从内存数据上传
            bos_client.put_object_from_string(
                bucket=self.bucket_name,
                key=object_key,
                data=data
            )
            
            # 生成永久URL
            timestamp = int(time.time())
            expiration_in_seconds = -1
            
            url = bos_client.generate_pre_signed_url(
                bucket_name=self.bucket_name,
                key=object_key,
                timestamp=timestamp,
                expiration_in_seconds=expiration_in_seconds
            )
            
            # 确保URL是字符串类型
            if isinstance(url, bytes):
                url = url.decode('utf-8')
            
            return url
        except Exception as e:
            logger.error(f"上传失败: {str(e)}")
            with self._lock:
                self.stats['upload_errors'] += 1
            return None
    
    def process_image(self, task):
        """处理单个图片任务
        
        Args:
            task: ImageTask实例
        
        Returns:
            ImageTask: 处理后的任务对象
        """
        old_url = task.image_url
        
        # 提取文件名
        parsed_url = urllib.parse.urlparse(old_url)
        raw_file_name = os.path.basename(parsed_url.path)
        
        # 使用URL的MD5哈希生成唯一文件名
        url_hash = hashlib.md5(old_url.encode('utf-8')).hexdigest()[:8]
        
        # 净化文件名
        file_name = "".join(c for c in raw_file_name if c.isalnum() or c in "._-")
        if not file_name or file_name == '':
            # 文件名为空使用哈希值
            file_name = f"{url_hash}.jpg"
        else:
            # 保留原始扩展名或默认为jpg
            file_base, file_ext = os.path.splitext(file_name)
            if not file_ext:
                file_ext = '.jpg'
            file_name = f"{url_hash}_{file_base}{file_ext}"
        
        # 生成日期文件夹
        date_folder = datetime.datetime.now().strftime("%Y%m%d")
        
        # 下载图片
        success, content = self.download_image(old_url)
        
        if success:
            # 构建对象键路径
            object_key = f"image_store/{date_folder}/{file_name}"
            
            # 上传到BOS
            new_url = self.upload_to_bos(content, object_key)
            if new_url:
                # 更新图片URL
                new_item = task.item.copy()
                new_item["url"] = new_url
                task.item = new_item
                
                with self._lock:
                    self.stats['images_processed'] += 1
            else:
                logger.warning(f"URL替换失败")
        else:
            logger.warning(f"图片下载失败，跳过处理")
            
        return task
    
    def image_worker(self):
        """图片处理工作线程"""
        while self.running:
            try:
                # 从任务队列获取图片任务
                task = self.task_queue.get(timeout=1)
                
                # 处理图片
                processed_task = self.process_image(task)
                
                # 将处理结果添加到结果队列
                self.result_queue.put(processed_task)
                
                # 标记任务完成
                self.task_queue.task_done()
            except queue.Empty:
                # 检查是否所有任务都已经添加到队列
                if self.parsing_complete:
                    # 最后检查队列是否为空
                    if self.task_queue.empty():
                        break
                # 继续等待任务
                continue
            except Exception as e:
                logger.error(f"图片处理线程错误: {str(e)}")
                # 出错也要标记任务完成
                self.task_queue.task_done()
    
    def reorder_fields(self, json_obj, processed_chat_files=None):
        """重新排序JSON字段
        
        Args:
            json_obj: 原始JSON对象
            processed_chat_files: 处理后的chat_files字段值
            
        Returns:
            dict: 字段已排序的JSON对象
        """
        result = {}
        
        # 如果指定了字段顺序，按照指定顺序排列字段
        if self.field_order:
            # 首先按照指定顺序添加存在的字段
            for field in self.field_order:
                if field in json_obj:
                    if field == "chat_files" and processed_chat_files is not None:
                        result[field] = processed_chat_files
                    else:
                        result[field] = json_obj[field]
            
            # 然后添加所有其他字段（保持原始顺序）
            for key, value in json_obj.items():
                if key not in result:  # 如果字段未在前面处理过
                    if key == "chat_files" and processed_chat_files is not None:
                        result[key] = processed_chat_files
                    else:
                        result[key] = value
        else:
            # 没有指定字段顺序，保持原始顺序
            for key, value in json_obj.items():
                if key == "chat_files" and processed_chat_files is not None:
                    result[key] = processed_chat_files
                else:
                    result[key] = value
                    
        return result
    
    def parse_and_submit_tasks(self):
        """解析输入文件并提交图片处理任务"""
        try:
            with open(self.input_path, 'r', encoding='utf-8') as infile:
                for line_num, line in enumerate(infile):
                    try:
                        line = line.strip()
                        if not line:
                            continue
                        
                        # 解析JSON
                        json_obj = json.loads(line)
                        
                        # 处理chat_files字段
                        pending_images = []  # 待处理的图片列表
                        new_chat_files = []  # 更新后的chat_files列表
                        
                        if "chat_files" in json_obj:
                            raw_chat_files = json_obj["chat_files"]
                            chat_files_list = []
                            
                            try:
                                # 解析chat_files
                                if raw_chat_files:
                                    if isinstance(raw_chat_files, str):
                                        chat_files_list = json.loads(raw_chat_files)
                                    elif isinstance(raw_chat_files, list):
                                        chat_files_list = raw_chat_files
                            except json.JSONDecodeError:
                                logger.error(f"无法解析chat_files")
                                chat_files_list = []
                            
                            # 识别需要处理的图片
                            for i, item in enumerate(chat_files_list):
                                if isinstance(item, dict) and item.get("type") == "image" and "url" in item:
                                    if self.bos_config:  # 只有配置了BOS才处理图片
                                        # 创建图片处理任务
                                        task = ImageTask(
                                            line_num=line_num,
                                            json_field_path=f"chat_files[{i}]",
                                            image_url=item["url"],
                                            item=item
                                        )
                                        # 提交任务到队列
                                        self.task_queue.put(task)
                                        pending_images.append(task)
                                    
                                    # 先保留原始item
                                    new_chat_files.append(item)
                                else:
                                    # 非图片项直接保留
                                    new_chat_files.append(item)
                        
                        # 将行数据和相关信息存储到结果队列
                        result_data = {
                            'line_num': line_num,
                            'json_obj': json_obj,
                            'pending_images': pending_images,
                            'new_chat_files': new_chat_files
                        }
                        self.result_queue.put(result_data)
                        
                        with self._lock:
                            self.stats['parsed_json'] += 1
                            
                        # 定期报告进度
                        if line_num % 1000 == 0:
                            logger.info(f"已解析: {line_num}行")
                            
                    except json.JSONDecodeError:
                        logger.error(f"第{line_num}行: 无效JSON")
                    except Exception as e:
                        logger.error(f"第{line_num}行: 解析错误: {str(e)}")
                        
            # 标记解析完成
            self.parsing_complete = True
            logger.info(f"解析完成: {self.stats['parsed_json']}条记录")
                
        except Exception as e:
            logger.error(f"文件解析错误: {str(e)}")
            # 标记解析完成，让其他线程可以退出
            self.parsing_complete = True
            
    def process_results(self):
        """处理结果并写入输出文件"""
        try:
            with open(self.output_path, 'w', encoding='utf-8') as outfile:
                # 跟踪下一个要写入的行号
                next_line = 0
                # 缓存尚未处理完的行结果
                pending_results = {}
                # 跟踪每行待处理的图片任务数
                pending_images_count = {}
                # 缓存已处理的图片结果
                processed_images = {}
                
                last_report_time = time.time()
                start_time = time.time()
                
                # 继续处理直到所有任务完成且所有行已写入
                while self.running:
                    try:
                        # 从结果队列获取处理结果
                        result = self.result_queue.get(timeout=0.5)
                        
                        # 处理不同类型的结果
                        if isinstance(result, ImageTask):
                            # 图片处理结果
                            img_task = result
                            key = (img_task.line_num, img_task.json_field_path)
                            processed_images[key] = img_task.item
                            
                            # 减少该行的待处理图片计数
                            if img_task.line_num in pending_images_count:
                                pending_images_count[img_task.line_num] -= 1
                                
                                # 如果该行所有图片都已处理，检查是否可以写入
                                if pending_images_count[img_task.line_num] <= 0:
                                    if img_task.line_num in pending_results:
                                        line_data = pending_results[img_task.line_num]
                                        # 更新chat_files
                                        new_chat_files = line_data['new_chat_files']
                                        # 替换已处理的图片项
                                        for i, item in enumerate(new_chat_files):
                                            key = (img_task.line_num, f"chat_files[{i}]")
                                            if key in processed_images:
                                                new_chat_files[i] = processed_images[key]
                                        
                                        # 更新JSON并写入
                                        processed_chat_files = json.dumps(new_chat_files, ensure_ascii=False)
                                        final_json = self.reorder_fields(line_data['json_obj'], processed_chat_files)
                                        
                                        # 如果是下一个待写入的行，直接写入
                                        if img_task.line_num == next_line:
                                            outfile.write(json.dumps(final_json, ensure_ascii=False) + '\n')
                                            next_line += 1
                                            del pending_results[img_task.line_num]
                                            
                                            # 检查是否有连续的行可以写入
                                            while next_line in pending_results and pending_images_count.get(next_line, 0) <= 0:
                                                line_data = pending_results[next_line]
                                                new_chat_files = line_data['new_chat_files']
                                                
                                                # 替换已处理的图片项
                                                for i, item in enumerate(new_chat_files):
                                                    key = (next_line, f"chat_files[{i}]")
                                                    if key in processed_images:
                                                        new_chat_files[i] = processed_images[key]
                                                
                                                processed_chat_files = json.dumps(new_chat_files, ensure_ascii=False)
                                                final_json = self.reorder_fields(line_data['json_obj'], processed_chat_files)
                                                
                                                outfile.write(json.dumps(final_json, ensure_ascii=False) + '\n')
                                                del pending_results[next_line]
                                                next_line += 1
                                        
                                        with self._lock:
                                            self.stats['processed_lines'] += 1
                        else:
                            # 行解析结果
                            line_data = result
                            line_num = line_data['line_num']
                            
                            # 如果没有待处理的图片，直接处理
                            if not line_data['pending_images']:
                                if "chat_files" in line_data['json_obj']:
                                    processed_chat_files = json.dumps(line_data['new_chat_files'], ensure_ascii=False)
                                    final_json = self.reorder_fields(line_data['json_obj'], processed_chat_files)
                                else:
                                    final_json = self.reorder_fields(line_data['json_obj'])
                                
                                # 如果是下一个待写入的行，直接写入
                                if line_num == next_line:
                                    outfile.write(json.dumps(final_json, ensure_ascii=False) + '\n')
                                    next_line += 1
                                    
                                    # 检查是否有连续的行可以写入
                                    while next_line in pending_results and pending_images_count.get(next_line, 0) <= 0:
                                        next_data = pending_results[next_line]
                                        new_chat_files = next_data['new_chat_files']
                                        
                                        # 替换已处理的图片项
                                        for i, item in enumerate(new_chat_files):
                                            key = (next_line, f"chat_files[{i}]")
                                            if key in processed_images:
                                                new_chat_files[i] = processed_images[key]
                                        
                                        processed_chat_files = json.dumps(new_chat_files, ensure_ascii=False)
                                        next_json = self.reorder_fields(next_data['json_obj'], processed_chat_files)
                                        
                                        outfile.write(json.dumps(next_json, ensure_ascii=False) + '\n')
                                        del pending_results[next_line]
                                        next_line += 1
                                else:
                                    # 存储起来等待其前面的行处理完成
                                    pending_results[line_num] = line_data
                                
                                with self._lock:
                                    self.stats['processed_lines'] += 1
                            else:
                                # 有待处理图片，记录待处理数量并缓存结果
                                pending_results[line_num] = line_data
                                pending_images_count[line_num] = len(line_data['pending_images'])
                        
                        # 定期报告进度
                        current_time = time.time()
                        if current_time - last_report_time > 5:  # 每5秒报告一次
                            elapsed = current_time - start_time
                            rate = self.stats['processed_lines'] / elapsed if elapsed > 0 else 0
                            logger.info(f"已完成: {self.stats['processed_lines']}行 ({rate:.2f}行/秒), 图片: {self.stats['images_processed']}张")
                            last_report_time = current_time
                        
                        self.result_queue.task_done()
                    
                    except queue.Empty:
                        # 检查是否所有处理都已完成
                        if self.parsing_complete and self.task_queue.empty():
                            if len(pending_results) == 0:
                                break
                        continue
                    except Exception as e:
                        logger.error(f"结果处理错误: {str(e)}")
                        self.result_queue.task_done()
                        
        except Exception as e:
            logger.error(f"输出文件处理错误: {str(e)}")
    
    def run(self, access_key_id=None, secret_access_key=None, bucket_name=None, endpoint="bj.bcebos.com"):
        """运行JSON转换处理
        
        Args:
            access_key_id: 百度云存储AK
            secret_access_key: 百度云存储SK
            bucket_name: 存储桶名称
            endpoint: BOS服务端点
            
        Returns:
            int: 处理的记录数
        """
        # 检查参数
        if all([access_key_id, secret_access_key, bucket_name]) and HAVE_BOS:
            self.init_bos_client(access_key_id, secret_access_key, bucket_name, endpoint)
        else:
            logger.warning("未提供BOS配置，跳过图片处理")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(os.path.abspath(self.output_path))
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        start_time = time.time()
        logger.info(f"开始处理: {self.input_path}")
        logger.info(f"工作线程数: {self.threads}")
        
        try:
            # 设置运行标志
            self.running = True
            self.parsing_complete = False
            
            # 启动图片处理工作线程池
            workers = []
            for _ in range(self.threads):
                worker = threading.Thread(target=self.image_worker)
                worker.daemon = True
                worker.start()
                workers.append(worker)
            
            # 启动结果处理线程
            result_thread = threading.Thread(target=self.process_results)
            result_thread.daemon = True
            result_thread.start()
            
            # 启动解析线程（主线程）
            self.parse_and_submit_tasks()
            
            # 等待所有任务完成
            self.task_queue.join()
            self.result_queue.join()
            
            # 等待所有线程完成
            self.running = False
            for worker in workers:
                worker.join(timeout=1)
            result_thread.join(timeout=5)
            
        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            self.running = False
            raise
        finally:
            # 确保标志被重置
            self.running = False
            self.parsing_complete = True
        
        total_time = time.time() - start_time
        logger.info(f"完成: {self.stats['processed_lines']}条记录, 处理图片{self.stats['images_processed']}张, 耗时{total_time:.2f}秒")
        logger.info(f"错误统计: 下载失败{self.stats['download_errors']}次, 上传失败{self.stats['upload_errors']}次")
        
        return self.stats['processed_lines']
            
if __name__ == "__main__":
    print("JSON格式转换工具")
    print("请输入以下参数，或直接按回车使用默认值")
    
    input_path = input("输入文件路径 [demo1.jsonl]: ") or "demo1.jsonl"
    output_path = input("输出文件路径 [demo1_converted.jsonl]: ") or "demo1_converted.jsonl"
    
    field_order_input = input("指定字段顺序（用逗号分隔，例如：chat_id,query,chat_files）[留空表示不指定]: ")
    field_order = field_order_input.split(',') if field_order_input else None
    
    print("\n是否需要处理图片URL？(Y/n): ")
    process_images = input().lower() != 'n'
    
    converter = JsonConverter(input_path, output_path, field_order=field_order)
    
    if process_images and HAVE_BOS:
        print("\n请输入BOS配置信息:")
        ak = input("Access Key ID: ")
        sk = input("Secret Access Key: ")
        bucket = input("Bucket名称: ")
        endpoint = input("服务端点 [bj.bcebos.com]: ") or "bj.bcebos.com"
        
        if all([ak, sk, bucket]):
            converter.run(ak, sk, bucket, endpoint)
        else:
            print("BOS配置信息不完整，跳过图片处理")
            converter.run()
    else:
        converter.run() 
        
        
        