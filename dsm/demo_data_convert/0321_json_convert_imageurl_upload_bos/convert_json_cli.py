#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import os
import sys
import logging
from json_converter import JsonConverter

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """命令行工具入口点"""
    parser = argparse.ArgumentParser(description='JSON格式转换工具: 调整字段顺序并处理图片URL')
    
    parser.add_argument('--input', '-i', type=str, required=True,
                        help='输入JSONL文件路径')
    parser.add_argument('--output', '-o', type=str,
                        help='输出JSONL文件路径，默认为输入文件名_converted.jsonl')
    parser.add_argument('--temp-dir', '-t', type=str, default='temp_images',
                        help='临时图片存储目录，默认为temp_images')
    parser.add_argument('--skip-images', '-s', action='store_true',
                        help='跳过图片处理，只调整字段顺序')
    parser.add_argument('--field-order', '-f', type=str,
                        default='chat_id,query,chat_files,answer,event_day,session_id',
                        help='指定输出字段顺序，用逗号分隔，例如：chat_id,query,chat_files')
    parser.add_argument('--threads', '-j', type=int, default=8,
                        help='工作线程数，默认为8')
    

    # BOS相关参数
    parser.add_argument('--ak', type=str, help='百度云存储Access Key ID')
    parser.add_argument('--sk', type=str, help='百度云存储Secret Access Key')
    parser.add_argument('--bucket', type=str, help='百度云存储Bucket名称')
    parser.add_argument('--endpoint', type=str, default='bj.bcebos.com',
                        help='BOS服务端点，默认为bj.bcebos.com')
    parser.add_argument('--log-level', type=str, choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                        default='INFO', help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # 如果未指定输出文件，则基于输入文件名生成
    if not args.output:
        input_base = os.path.splitext(args.input)[0]
        args.output = f"{input_base}_converted.jsonl"
    
    # 处理field_order参数
    field_order = args.field_order.split(',') if args.field_order else None
    
    logger.info(f"输入: {args.input}, 输出: {args.output}")
    logger.info(f"线程数: {args.threads}")
    
    try:
        # 检查输入文件是否存在
        if not os.path.exists(args.input):
            logger.error(f"输入文件不存在")
            sys.exit(1)
        
        # 创建临时目录
        os.makedirs(args.temp_dir, exist_ok=True)
        
        # 创建转换器实例
        converter = JsonConverter(args.input, args.output, args.temp_dir, field_order, args.threads)
        
        # 检查是否跳过图片处理
        if args.skip_images:
            logger.info(f"只调整字段顺序，不处理图片URL")
            converter.run()
        else:
            # 检查BOS配置
            if all([args.ak, args.sk, args.bucket]):
                logger.info(f"处理JSONL并上传图片到BOS: {args.bucket}")
                converter.run(args.ak, args.sk, args.bucket, args.endpoint)
            else:
                logger.warning("BOS配置不完整，只调整字段顺序")
                converter.run()
        
        logger.info(f"处理完成: {args.output}")
    except KeyboardInterrupt:
        logger.warning("用户中断")
        sys.exit(130)
    except Exception as e:
        logger.error(f"错误: {str(e)}")
        if args.log_level == 'DEBUG':
            import traceback
            traceback.print_exc()
        sys.exit(1)
    
if __name__ == "__main__":
    main() 