#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全局设置配置文件
包含项目的全局配置参数
"""

import os
import sys
from pathlib import Path

# 项目根目录
ROOT_DIR = Path(__file__).parent.parent.absolute()

# 模块目录
MODULES_DIR = os.path.join(ROOT_DIR, "modules")

# 静态资源目录
STATIC_DIR = os.path.join(ROOT_DIR, "static")

# 各模块数据根目录
DATA_ROOT_DIR = os.path.join(ROOT_DIR, "data")

# 日志配置
LOG_DIR = os.path.join(ROOT_DIR, "logs")
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 应用设置
APP_TITLE = "数据转换与可视化工具"
APP_ICON = "📊"
APP_LAYOUT = "wide"
SIDEBAR_STATE = "expanded"

# 模块设置
MODULES = {
    "data_converter": {
        "name": "数据转换",
        "icon": "📝",
        "description": "JSONL文件转换为CSV或Excel格式"
    },
    "data_visualizer": {
        "name": "数据可视化",
        "icon": "📊",
        "description": "数据可视化和图表生成"
    },
    "about": {
        "name": "关于项目",
        "icon": "ℹ️",
        "description": "项目信息和使用说明"
    }
}

# 文件上传设置
MAX_UPLOAD_SIZE = 200  # MB
ALLOWED_EXTENSIONS = {
    "data_converter": [".jsonl"],
    "data_visualizer": [".csv", ".xlsx"]
}

# 默认文件保存设置
DEFAULT_CSV_ENCODING = "utf-8"
DEFAULT_IMAGE_TIMEOUT = 10  # 秒
DEFAULT_MAX_IMAGES = 100 