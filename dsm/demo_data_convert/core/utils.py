#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通用工具函数模块
提供全局可用的工具函数
"""

import os
import json
import uuid
import shutil
import base64
from datetime import datetime
import time
from pathlib import Path
import pandas as pd

from config import settings
from core.logger import logger

def create_unique_id():
    """生成唯一标识符"""
    return str(uuid.uuid4())

def ensure_dir(directory):
    """确保目录存在，如果不存在则创建"""
    os.makedirs(directory, exist_ok=True)
    return directory

def get_module_data_dir(module_name):
    """获取模块的数据目录路径"""
    module_data_dir = os.path.join(settings.MODULES_DIR, module_name, "data")
    return ensure_dir(module_data_dir)

def get_module_temp_dir(module_name):
    """获取模块的临时数据目录路径"""
    temp_dir = os.path.join(get_module_data_dir(module_name), "temp")
    return ensure_dir(temp_dir)

def get_module_results_dir(module_name):
    """获取模块的结果数据目录路径"""
    results_dir = os.path.join(get_module_data_dir(module_name), "results")
    return ensure_dir(results_dir)

def create_job_dir(module_name):
    """为任务创建工作目录"""
    job_id = create_unique_id()
    job_dir = os.path.join(get_module_results_dir(module_name), job_id)
    ensure_dir(job_dir)
    return job_id, job_dir

def check_file_extension(filename, allowed_extensions):
    """检查文件扩展名是否在允许列表中"""
    if not filename:
        return False
    return Path(filename).suffix.lower() in allowed_extensions

def sanitize_filename(filename):
    """清理文件名，确保安全"""
    # 移除文件名中的不安全字符
    import re
    return re.sub(r'[^\w\.-]', '_', filename)

def get_file_download_link(file_path, label=None):
    """生成文件下载链接的HTML"""
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return "文件不存在"
    
    with open(file_path, 'rb') as f:
        data = f.read()
    
    b64 = base64.b64encode(data).decode()
    filename = os.path.basename(file_path)
    
    if label is None:
        label = filename
    
    href = f'<a href="data:application/octet-stream;base64,{b64}" download="{filename}">{label}</a>'
    return href

def load_data(file_path):
    """加载数据文件并返回DataFrame"""
    if file_path.endswith('.csv'):
        return pd.read_csv(file_path)
    elif file_path.endswith('.xlsx'):
        return pd.read_excel(file_path)
    elif file_path.endswith('.jsonl'):
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    data.append(json.loads(line))
        return pd.DataFrame(data)
    else:
        logger.error(f"不支持的文件格式: {file_path}")
        raise ValueError(f"不支持的文件格式: {file_path}")

def cleanup_old_files(directory, max_age_days=1):
    """清理超过指定天数的文件和目录"""
    if not os.path.exists(directory):
        return
    
    now = time.time()
    max_age_seconds = max_age_days * 24 * 60 * 60
    
    for item in os.listdir(directory):
        item_path = os.path.join(directory, item)
        
        # 检查是否超过最大期限
        if os.path.isdir(item_path):
            if now - os.path.getmtime(item_path) > max_age_seconds:
                logger.info(f"清理旧目录: {item_path}")
                try:
                    shutil.rmtree(item_path)
                except Exception as e:
                    logger.error(f"清理目录失败: {e}")
        elif os.path.isfile(item_path):
            if now - os.path.getmtime(item_path) > max_age_seconds:
                logger.info(f"清理旧文件: {item_path}")
                try:
                    os.remove(item_path)
                except Exception as e:
                    logger.error(f"清理文件失败: {e}")
                    
def human_readable_size(size_bytes):
    """将字节数转换为可读的大小格式"""
    if size_bytes == 0:
        return "0B"
    size_name = ("B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB")
    i = 0
    while size_bytes >= 1024 and i < len(size_name) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.2f} {size_name[i]}" 