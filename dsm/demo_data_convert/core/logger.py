#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志配置模块
提供项目的日志记录功能
"""

import os
import logging
from logging.handlers import RotatingFileHandler
import sys
from pathlib import Path

from config import settings

# 确保日志目录存在
os.makedirs(settings.LOG_DIR, exist_ok=True)

# 配置根日志记录器
def setup_logger(name="data_converter", level=None):
    """
    配置和返回logger实例
    
    参数:
        name: 日志记录器名称
        level: 日志级别，如果为None则使用配置中的级别
    
    返回:
        logger对象
    """
    if level is None:
        level = getattr(logging, settings.LOG_LEVEL)
    
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 防止重复添加处理器
    if logger.handlers:
        return logger
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_formatter = logging.Formatter(settings.LOG_FORMAT)
    console_handler.setFormatter(console_formatter)
    
    # 文件处理器
    log_file = os.path.join(settings.LOG_DIR, f"{name}.log")
    file_handler = RotatingFileHandler(
        log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
    )
    file_handler.setLevel(level)
    file_formatter = logging.Formatter(settings.LOG_FORMAT)
    file_handler.setFormatter(file_formatter)
    
    # 添加处理器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    return logger

# 默认日志记录器
logger = setup_logger() 