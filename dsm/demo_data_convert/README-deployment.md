# JSONL 格式转换工具

这个工具用于处理 JSONL 格式数据，实现以下功能：
1. 调整 JSON 字段顺序，按以下顺序展示：
   - chat_id
   - query
   - chat_files
   - answer
   - event_day
   - session_id
2. 提取图片 URL，下载并上传到百度云 BOS 存储，然后获取永久有效的 URL 链接替换原始链接

## 环境要求

- Python 3.7+
- 安装依赖：`pip install -r requirements.txt`
- 百度云 BOS 存储账号（如需处理图片）

## 使用方法

### 命令行方式

```bash
# 仅调整字段顺序，不处理图片
python convert_json_cli.py -i 输入文件.jsonl -o 输出文件.jsonl --skip-images

# 调整字段顺序并处理图片URL
python convert_json_cli.py -i 输入文件.jsonl -o 输出文件.jsonl --ak <Access Key ID> --sk <Secret Access Key> --bucket <Bucket名称>
```

### 参数说明

- `-i, --input`: 输入 JSONL 文件路径
- `-o, --output`: 输出 JSONL 文件路径
- `-t, --temp-dir`: 临时图片存储目录
- `-s, --skip-images`: 跳过图片处理，只调整字段顺序
- `--ak`: 百度云存储 Access Key ID
- `--sk`: 百度云存储 Secret Access Key
- `--bucket`: 百度云存储 Bucket 名称
- `--endpoint`: BOS 服务端点，默认为 bj.bcebos.com

### 交互式方式

也可以通过脚本文件直接使用：

```bash
python json_converter.py
```

然后按照提示输入必要的参数。

## 图片处理规则

转存图片的规则：
- BOS中的对象键格式：`image_update/yyyymmdd-hhmmss-filename/image_name`
- 生成的 URL 为永久有效的 URL

## 注意事项

1. 请确保有足够的磁盘空间和网络连接以下载和上传图片
2. 图片处理过程可能较慢，取决于图片数量和大小
3. 所有处理过程会记录在日志中，可用于排查问题
4. 转换过程不会修改原始输入文件 