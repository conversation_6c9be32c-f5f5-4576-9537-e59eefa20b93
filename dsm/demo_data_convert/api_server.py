#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import shutil
import uuid
import json
from typing import List, Optional
from fastapi import FastAPI, File, UploadFile, Form, BackgroundTasks, HTTPException
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import pandas as pd
import logging
from pydantic import BaseModel
import subprocess
import tempfile
import time
from datetime import datetime

# 引入项目中的模块
from data_converter import read_jsonl, convert_to_dataframe, save_to_csv, save_to_excel, extract_image_urls, download_images, analyze_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建临时目录
TEMP_DIR = os.path.join(os.path.dirname(__file__), "temp")
RESULTS_DIR = os.path.join(os.path.dirname(__file__), "results")
os.makedirs(TEMP_DIR, exist_ok=True)
os.makedirs(RESULTS_DIR, exist_ok=True)

app = FastAPI(
    title="数据转换API",
    description="提供JSONL文件转换为CSV/Excel、数据分析和图片下载的API服务",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建一个静态文件目录来提供下载
app.mount("/downloads", StaticFiles(directory=RESULTS_DIR), name="downloads")

# 定义请求和响应模型
class ConversionRequest(BaseModel):
    output_format: str = "csv"  # csv, excel, both
    download_images: bool = False
    analyze_data: bool = False

class ConversionResponse(BaseModel):
    job_id: str
    status: str
    message: str
    result_files: Optional[List[str]] = None

class ConversionJob(BaseModel):
    job_id: str
    status: str  # pending, processing, completed, failed
    start_time: float
    input_file: str
    output_format: str
    download_images: bool
    analyze_data: bool
    result_files: Optional[List[str]] = None
    error_message: Optional[str] = None

# 存储转换任务
conversion_jobs = {}

# 定义API路由
@app.get("/")
async def root():
    return {"message": "欢迎使用数据转换API，访问 /docs 查看API文档"}

@app.post("/convert/", response_model=ConversionResponse)
async def convert_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    output_format: str = Form("csv"),  # csv, excel, both
    download_images: bool = Form(False),
    analyze_data: bool = Form(False)
):
    if not file.filename.endswith('.jsonl'):
        raise HTTPException(status_code=400, detail="只支持JSONL文件")
    
    # 生成任务ID
    job_id = str(uuid.uuid4())
    job_dir = os.path.join(RESULTS_DIR, job_id)
    os.makedirs(job_dir, exist_ok=True)
    
    # 保存上传的文件
    input_file_path = os.path.join(job_dir, file.filename)
    with open(input_file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    # 创建任务
    job = ConversionJob(
        job_id=job_id,
        status="pending",
        start_time=time.time(),
        input_file=input_file_path,
        output_format=output_format,
        download_images=download_images,
        analyze_data=analyze_data
    )
    conversion_jobs[job_id] = job
    
    # 在后台运行转换任务
    background_tasks.add_task(process_conversion, job)
    
    return ConversionResponse(
        job_id=job_id,
        status="pending",
        message="文件已上传，开始处理"
    )

async def process_conversion(job: ConversionJob):
    try:
        job.status = "processing"
        logger.info(f"开始处理任务 {job.job_id}")
        
        # 读取数据
        data = read_jsonl(job.input_file)
        df = convert_to_dataframe(data)
        
        job_dir = os.path.join(RESULTS_DIR, job.job_id)
        result_files = []
        
        # 保存为CSV
        if job.output_format in ["csv", "both"]:
            csv_path = os.path.join(job_dir, "data.csv")
            save_to_csv(df, csv_path)
            result_files.append(f"/downloads/{job.job_id}/data.csv")
        
        # 保存为Excel
        if job.output_format in ["excel", "both"]:
            excel_path = os.path.join(job_dir, "data.xlsx")
            save_to_excel(df, excel_path)
            result_files.append(f"/downloads/{job.job_id}/data.xlsx")
        
        # 下载图片
        if job.download_images:
            image_dir = os.path.join(job_dir, "images")
            os.makedirs(image_dir, exist_ok=True)
            image_urls = extract_image_urls(data)
            if image_urls:
                download_images(image_urls, image_dir)
                # 创建图片目录的zip文件
                shutil.make_archive(os.path.join(job_dir, "images"), 'zip', image_dir)
                result_files.append(f"/downloads/{job.job_id}/images.zip")
        
        # 分析数据
        if job.analyze_data:
            stats = analyze_data(df)
            stats_path = os.path.join(job_dir, "stats.json")
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=4)
            result_files.append(f"/downloads/{job.job_id}/stats.json")
            
            # 生成可视化
            viz_dir = os.path.join(job_dir, "visualizations")
            cmd = [
                f"python data_visualizer.py",
                f"--input {os.path.join(job_dir, 'data.csv')}",
                f"--output-dir {viz_dir}"
            ]
            subprocess.run(" ".join(cmd), shell=True, check=True)
            
            # 打包可视化结果
            if os.path.exists(viz_dir):
                shutil.make_archive(os.path.join(job_dir, "visualizations"), 'zip', viz_dir)
                result_files.append(f"/downloads/{job.job_id}/visualizations.zip")
        
        # 更新任务状态
        job.status = "completed"
        job.result_files = result_files
        
        logger.info(f"任务 {job.job_id} 处理完成")
        
    except Exception as e:
        logger.error(f"处理任务 {job.job_id} 时出错: {str(e)}")
        job.status = "failed"
        job.error_message = str(e)

@app.get("/jobs/{job_id}", response_model=ConversionResponse)
async def get_job_status(job_id: str):
    if job_id not in conversion_jobs:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    job = conversion_jobs[job_id]
    return ConversionResponse(
        job_id=job.job_id,
        status=job.status,
        message="处理中" if job.status == "processing" else ("处理完成" if job.status == "completed" else f"处理失败: {job.error_message}"),
        result_files=job.result_files
    )

@app.get("/jobs")
async def list_jobs():
    return {job_id: {
        "status": job.status,
        "start_time": datetime.fromtimestamp(job.start_time).strftime('%Y-%m-%d %H:%M:%S'),
        "input_file": os.path.basename(job.input_file),
    } for job_id, job in conversion_jobs.items()}

@app.delete("/jobs/{job_id}")
async def delete_job(job_id: str):
    if job_id not in conversion_jobs:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 删除任务目录
    job_dir = os.path.join(RESULTS_DIR, job_id)
    if os.path.exists(job_dir):
        shutil.rmtree(job_dir)
    
    # 删除任务记录
    del conversion_jobs[job_id]
    
    return {"message": f"任务 {job_id} 已删除"}

# 示例JSONL上传端点
@app.get("/example")
async def get_example_jsonl():
    """返回一个示例JSONL文件"""
    example_file = "demo1.jsonl"
    if not os.path.exists(example_file):
        raise HTTPException(status_code=404, detail="示例文件不存在")
    
    return FileResponse(example_file, filename="example.jsonl")

# 定期清理临时文件的任务
@app.on_event("startup")
async def startup_event():
    # 清理前一次运行的临时文件
    if os.path.exists(TEMP_DIR):
        for item in os.listdir(TEMP_DIR):
            item_path = os.path.join(TEMP_DIR, item)
            if os.path.isdir(item_path):
                shutil.rmtree(item_path)
            else:
                os.remove(item_path)

@app.on_event("shutdown")
async def shutdown_event():
    # 在关闭时可以执行一些清理工作
    pass

# 启动服务
if __name__ == "__main__":
    uvicorn.run("api_server:app", host="0.0.0.0", port=8000, reload=True) 