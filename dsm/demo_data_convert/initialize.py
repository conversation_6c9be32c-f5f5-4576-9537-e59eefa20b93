#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目初始化脚本
创建必要的目录和文件
"""

import os
import sys
import shutil
from pathlib import Path
import logging
import json
import argparse

# 确保项目根目录在系统路径中
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# 配置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("初始化")

def ensure_dir(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")
    return directory

def create_directories():
    """创建项目必要的目录结构"""
    logger.info("开始创建项目目录结构...")
    
    # 主要目录
    directories = [
        "config",
        "core",
        "modules",
        "modules/data_converter",
        "modules/data_converter/data",
        "modules/data_visualizer",
        "modules/data_visualizer/data",
        "modules/about",
        "static",
        "static/css",
        "static/fonts",
        "static/demo",
        "data",
        "data/data_converter",
        "data/data_converter/temp",
        "data/data_converter/results",
        "data/data_visualizer",
        "data/data_visualizer/temp",
        "data/data_visualizer/results",
        "logs"
    ]
    
    for directory in directories:
        ensure_dir(os.path.join(PROJECT_ROOT, directory))
    
    logger.info("目录结构创建完成")

def create_css_file():
    """创建基本的CSS样式文件"""
    css_dir = os.path.join(PROJECT_ROOT, "static", "css")
    css_file = os.path.join(css_dir, "style.css")
    
    if not os.path.exists(css_file):
        with open(css_file, "w", encoding="utf-8") as f:
            f.write("""/* 全局样式 */
body {
    font-family: 'Source Sans Pro', sans-serif;
    color: #2c3e50;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    font-weight: 600;
}

/* 按钮样式 */
.stButton>button {
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.stButton>button:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 卡片样式 */
div.stBlock {
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* 提高侧边栏的可读性 */
.sidebar .sidebar-content {
    background-color: #f5f7f9;
    padding: 1em;
}

/* 自定义进度条样式 */
div.stProgress > div > div {
    background-color: #4CAF50;
}

/* 表格样式 */
table {
    border-collapse: collapse;
    width: 100%;
}

th, td {
    padding: 8px 16px;
    border-bottom: 1px solid #dfe2e5;
}

th {
    background-color: #f1f3f4;
    font-weight: 600;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
    .stBlock {
        padding: 10px;
    }
}
""")
        logger.info(f"创建CSS文件: {css_file}")

def create_demo_files():
    """创建示例数据文件"""
    logger.info("创建示例数据文件...")
    
    demo_dir = os.path.join(PROJECT_ROOT, "static", "demo")
    ensure_dir(demo_dir)
    
    # 创建一个简单的JSONL示例文件
    jsonl_file = os.path.join(demo_dir, "demo1.jsonl")
    if not os.path.exists(jsonl_file):
        with open(jsonl_file, "w", encoding="utf-8") as f:
            demo_data = [
                {"id": 1, "query": "如何使用Python处理JSON数据?", "answer": "Python有内置的json模块，可以使用json.loads()解析JSON字符串，json.dumps()将Python对象转换为JSON字符串。pandas也提供了read_json函数处理JSON文件。", "image_url": "https://example.com/images/python.jpg", "event_day": "2023-01-01"},
                {"id": 2, "query": "Python的列表和元组有什么区别?", "answer": "列表(list)是可变的，可以添加、删除和修改元素。元组(tuple)是不可变的，创建后不能修改。列表用方括号[]表示，元组用圆括号()表示。元组通常用于不应修改的数据。", "image_url": "https://example.com/images/python-list.jpg", "event_day": "2023-01-02"},
                {"id": 3, "query": "什么是Pandas DataFrame?", "answer": "DataFrame是Pandas中的一个二维表格数据结构，类似于Excel表格或SQL表。它有行和列，可以存储不同类型的数据，并提供丰富的方法进行数据操作、清洗和分析。", "image_url": "https://example.com/images/pandas.jpg", "event_day": "2023-01-03"},
                {"id": 4, "query": "如何在Python中处理Excel文件?", "answer": "可以使用pandas库的read_excel()函数读取Excel文件，使用to_excel()函数写入Excel文件。也可以使用openpyxl或xlwt/xlrd库进行更底层的Excel操作。", "image_url": "https://example.com/images/excel.jpg", "event_day": "2023-01-03"},
                {"id": 5, "query": "Python中如何进行数据可视化?", "answer": "Python中常用的数据可视化库有Matplotlib(基础绘图)、Seaborn(统计可视化)、Plotly(交互式图表)和Bokeh等。这些库可以创建各种图表如折线图、柱状图、散点图等。", "image_url": "https://example.com/images/dataviz.jpg", "event_day": "2023-01-04"}
            ]
            for item in demo_data:
                f.write(json.dumps(item, ensure_ascii=False) + "\n")
        logger.info(f"创建JSONL示例文件: {jsonl_file}")
    
    # 创建一个简单的CSV示例文件
    csv_file = os.path.join(demo_dir, "demo.csv")
    if not os.path.exists(csv_file):
        with open(csv_file, "w", encoding="utf-8") as f:
            f.write("id,query,answer,event_day,score\n")
            f.write('1,"如何使用Python处理JSON数据?","Python有内置的json模块，可以使用json.loads()解析JSON字符串。",2023-01-01,0.95\n')
            f.write('2,"Python的列表和元组有什么区别?","列表是可变的，元组是不可变的。",2023-01-02,0.88\n')
            f.write('3,"什么是Pandas DataFrame?","DataFrame是Pandas中的一个二维表格数据结构。",2023-01-03,0.92\n')
            f.write('4,"如何在Python中处理Excel文件?","可以使用pandas库的read_excel()函数读取Excel文件。",2023-01-03,0.85\n')
            f.write('5,"Python中如何进行数据可视化?","Python中常用的数据可视化库有Matplotlib和Seaborn等。",2023-01-04,0.91\n')
        logger.info(f"创建CSV示例文件: {csv_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="项目初始化脚本")
    parser.add_argument("--force", action="store_true", help="强制重新初始化所有内容")
    args = parser.parse_args()
    
    logger.info("开始初始化项目...")
    
    if args.force:
        logger.warning("强制模式启用，将重新创建所有内容")
    
    # 创建目录结构
    create_directories()
    
    # 创建CSS文件
    create_css_file()
    
    # 创建示例数据文件
    create_demo_files()
    
    logger.info("项目初始化完成!")
    logger.info("使用以下命令运行应用: streamlit run app.py")

if __name__ == "__main__":
    main() 