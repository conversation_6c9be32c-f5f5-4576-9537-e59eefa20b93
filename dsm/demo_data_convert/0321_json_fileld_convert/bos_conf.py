#!/usr/bin/env python
# -*- coding: utf-8 -*-

# BOS配置信息
# 将此文件重命名为bos_conf.py并填入您的实际配置信息

# 百度云存储Access Key ID
access_key_id = "ALTAKF2gUJak7ycdp3cYaznbL5"

# 百度云存储Secret Access Key
secret_access_key = "677dd0b9288f408c907ec8aa33b4532f"

# 百度云存储Bucket名称
bucket_name = "data-storage-mgr"

# BOS服务端点，默认为bj.bcebos.com
endpoint = "https://su.bcebos.com" 


# python convert_json_cli.py -i 样例数据.jsonl -o 样例数据1.jsonl --ak ALTAKF2gUJak7ycdp3cYaznbL5 --sk 677dd0b9288f408c907ec8aa33b4532f --bucket data-storage-mgr --endpoint https://su.bcebos.com --log-level DEBUG