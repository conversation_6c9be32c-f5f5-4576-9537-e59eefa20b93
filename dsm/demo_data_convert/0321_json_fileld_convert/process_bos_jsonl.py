#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import os
import sys
import json
import logging
from baidubce.services.bos.bos_client import BosClient
from baidubce.auth.bce_credentials import BceCredentials
from baidubce.bce_client_configuration import BceClientConfiguration

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def process_jsonl_file(input_file, output_file):
    """处理JSONL文件，调整字段顺序"""
    with open(input_file, "r", encoding="utf-8") as f_in, open(
        output_file, "w", encoding="utf-8"
    ) as f_out:
        for line_num, line in enumerate(f_in, 1):
            try:
                # 解析JSON
                data = json.loads(line.strip())

                # 创建新的有序字典
                new_data = {}

                # 按照指定顺序添加字段
                for field in [
                    "chat_id",
                    "query",
                    "chat_files",
                    "answer",
                    "response",
                    "chat_type",
                    "event_day",
                    "session_id",
                    "eda_type",
                ]:
                    if field in data:
                        new_data[field] = data.pop(field)

                # 添加其他字段
                for key, value in data.items():
                    new_data[key] = value

                # 写入输出文件
                f_out.write(json.dumps(new_data, ensure_ascii=False) + "\n")

            except json.JSONDecodeError:
                logger.warning(f"第{line_num}行不是有效的JSON格式，将原样保留")
                f_out.write(line)
            except Exception as e:
                logger.error(f"处理第{line_num}行时出错: {str(e)}")
                f_out.write(line)

    logger.info(f"处理完成，输出到: {output_file}")


def create_bos_client(ak, sk, endpoint):
    """创建BOS客户端"""
    config = BceClientConfiguration(
        credentials=BceCredentials(ak, sk), endpoint=endpoint
    )
    return BosClient(config)


def download_from_bos(client, bucket, object_key, local_file):
    """从BOS下载文件"""
    logger.info(f"从BOS下载文件: {bucket}/{object_key} 到 {local_file}")
    client.get_object_to_file(bucket, object_key, local_file)


def upload_to_bos(client, bucket, object_key, local_file):
    """上传文件到BOS"""
    logger.info(f"上传文件到BOS: {local_file} 到 {bucket}/{object_key}")
    client.put_object_from_file(bucket, object_key, local_file)


def process_work_file(work_file, ak, sk, bucket, endpoint, temp_dir):
    """处理工作文件中的每一行"""
    os.makedirs(temp_dir, exist_ok=True)

    # 创建BOS客户端
    bos_client = create_bos_client(ak, sk, endpoint)

    # 读取工作文件
    with open(work_file, "r", encoding="utf-8") as f:
        for line_num, line in enumerate(f, 1):
            object_key = line.strip()
            if not object_key:
                logger.warning(f"第{line_num}行为空，跳过")
                continue

            try:
                # 为每个文件创建临时文件
                download_file = os.path.join(temp_dir, f"download_{line_num}.jsonl")
                processed_file = os.path.join(temp_dir, f"processed_{line_num}.jsonl")

                # 下载文件
                download_from_bos(bos_client, bucket, object_key, download_file)

                # 处理文件
                process_jsonl_file(download_file, processed_file)

                # 上传处理后的文件
                upload_to_bos(bos_client, bucket, object_key, processed_file)

                logger.info(f"文件 {object_key} 处理完成")

                # 清理临时文件
                try:
                    os.remove(download_file)
                    os.remove(processed_file)
                except Exception as e:
                    logger.warning(f"清理临时文件时出错: {str(e)}")

            except Exception as e:
                logger.error(f"处理第{line_num}行 ({object_key}) 时出错: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="处理BOS中的JSONL文件，调整字段顺序并重新上传"
    )

    parser.add_argument(
        "--work-file",
        "-w",
        type=str,
        required=True,
        help="工作文件路径，每行一个BOS object key",
    )
    parser.add_argument("--ak", type=str, required=True, help="百度云存储Access Key ID")
    parser.add_argument(
        "--sk", type=str, required=True, help="百度云存储Secret Access Key"
    )
    parser.add_argument(
        "--bucket", type=str, required=True, help="百度云存储Bucket名称"
    )
    parser.add_argument(
        "--endpoint",
        type=str,
        default="bj.bcebos.com",
        help="BOS服务端点，默认为bj.bcebos.com",
    )
    parser.add_argument(
        "--temp-dir",
        "-t",
        type=str,
        default="temp_files",
        help="临时文件存储目录，默认为temp_files",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别",
    )

    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    try:
        # 检查工作文件是否存在
        if not os.path.exists(args.work_file):
            logger.error(f"工作文件不存在: {args.work_file}")
            sys.exit(1)

        # 处理工作文件
        process_work_file(
            args.work_file, args.ak, args.sk, args.bucket, args.endpoint, args.temp_dir
        )

        logger.info("所有文件处理完成")
    except KeyboardInterrupt:
        logger.warning("用户中断操作")
        sys.exit(130)
    except Exception as e:
        logger.error(f"执行过程中发生错误: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
