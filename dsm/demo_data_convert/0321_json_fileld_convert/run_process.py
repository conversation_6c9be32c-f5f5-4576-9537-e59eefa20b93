#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """启动处理工作的脚本"""
    try:
        # 检查配置文件是否存在
        if not os.path.exists('bos_conf.py'):
            logger.error("配置文件 bos_conf.py 不存在，请先从 bos_sample_conf.py 创建")
            sys.exit(1)
        
        # 导入配置
        try:
            import bos_conf as conf
        except ImportError as e:
            logger.error(f"导入配置文件时出错: {str(e)}")
            sys.exit(1)
        
        # 检查必要的配置
        required_configs = ['access_key_id', 'secret_access_key', 'bucket_name']
        for config in required_configs:
            if not hasattr(conf, config) or getattr(conf, config) == f"您的{config.replace('_', ' ').title()}":
                logger.error(f"配置文件中缺少有效的 {config}，请检查配置文件")
                sys.exit(1)
        
        # 获取工作文件
        # work_file = input("请输入工作文件路径 (每行一个BOS object key): ", "work.txt").strip()
        work_file = "work.txt"
        if not work_file:
            sys.exit(1)
        
        if not os.path.exists(work_file):
            logger.error(f"工作文件不存在: {work_file}")
            sys.exit(1)
        
        # 构建命令行
        endpoint = getattr(conf, 'endpoint', 'bj.bcebos.com')
        cmd = (
            f"python process_bos_jsonl.py "
            f"--work-file '{work_file}' "
            f"--ak '{conf.access_key_id}' "
            f"--sk '{conf.secret_access_key}' "
            f"--bucket '{conf.bucket_name}' "
            f"--endpoint '{endpoint}'"
        )
        
        logger.info("开始处理JSONL文件...")
        # 执行命令
        exit_code = os.system(cmd)
        
        if exit_code == 0:
            logger.info("处理完成!")
        else:
            logger.error(f"处理失败，退出代码: {exit_code}")
            sys.exit(exit_code)
    
    except KeyboardInterrupt:
        logger.warning("用户中断操作")
        sys.exit(130)
    except Exception as e:
        logger.error(f"执行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 