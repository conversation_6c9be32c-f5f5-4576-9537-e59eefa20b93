#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
关于页面模块
提供项目说明、使用指南和开发说明
"""

import streamlit as st
import os
from datetime import datetime
import platform
import sys
import pandas as pd

from config import settings

def render():
    """渲染关于页面"""
    st.markdown("## ℹ️ 关于数据处理工具")
    
    # 项目介绍
    st.markdown("""
    这是一个多功能数据处理工具，主要用于处理和可视化数据。目前支持的功能包括：
    
    1. **数据转换** - 将JSONL文件转换为CSV或Excel格式，支持图片下载和数据分析
    2. **数据可视化** - 对CSV或Excel数据进行可视化处理，生成各类图表和分析报告
    
    本项目使用Python和Streamlit构建，采用模块化设计，便于扩展新功能。
    """)
    
    # 使用选项卡组织内容
    tab1, tab2, tab3 = st.tabs(["使用指南", "技术架构", "系统信息"])
    
    # 使用指南
    with tab1:
        st.markdown("""
        ### 使用指南
        
        #### 数据转换模块
        
        数据转换模块可以处理JSONL格式的数据，转换为更易于分析的CSV或Excel格式。
        
        **基本步骤:**
        1. 上传JSONL文件或使用示例数据
        2. 选择输出格式（CSV、Excel或两者）
        3. 设置是否需要下载图片和分析数据
        4. 点击"开始转换"按钮
        5. 下载转换后的文件和分析报告
        
        #### 数据可视化模块
        
        数据可视化模块可以对CSV或Excel数据生成各类图表和分析报告。
        
        **基本步骤:**
        1. 上传CSV或Excel文件或使用示例数据
        2. 在侧边栏中选择可视化选项
        3. 点击"生成可视化"按钮
        4. 查看和下载生成的图表和报告
        """)
    
    # 技术架构
    with tab2:
        st.markdown("""
        ### 技术架构
        
        本项目采用模块化设计，主要组件包括：
        
        #### 目录结构
        ```
        项目根目录/
        ├── config/                # 配置文件目录
        ├── core/                  # 核心功能
        ├── modules/               # 业务模块目录
        │   ├── data_converter/    # 数据转换模块
        │   ├── data_visualizer/   # 数据可视化模块
        │   └── about/             # 关于页面
        ├── static/                # 静态资源
        └── app.py                 # 主应用
        ```
        
        #### 主要技术栈
        - **Python**: 核心编程语言
        - **Streamlit**: Web界面框架
        - **Pandas**: 数据处理
        - **Matplotlib/Seaborn**: 数据可视化
        - **Requests**: 网络请求
        
        #### 模块扩展
        
        要添加新模块，只需在modules目录下创建新的模块目录，并实现以下文件：
        - `__init__.py`: 模块初始化
        - `page.py`: 包含render()函数的Streamlit页面
        - 其他业务逻辑文件
        
        新模块会在应用启动时自动加载到导航栏中。
        """)
    
    # 系统信息
    with tab3:
        st.markdown("### 系统信息")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 运行环境")
            info = {
                "Python版本": sys.version.split()[0],
                "操作系统": platform.platform(),
                "处理器架构": platform.machine(),
                "当前时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            for k, v in info.items():
                st.markdown(f"**{k}**: {v}")
        
        with col2:
            st.markdown("#### 依赖包版本")
            
            packages = {
                "streamlit": st.__version__,
                "pandas": pd.__version__,
                "numpy": pd.np.__version__
            }
            
            # 尝试导入并获取其他包的版本
            try:
                import matplotlib
                packages["matplotlib"] = matplotlib.__version__
            except:
                pass
            
            try:
                import seaborn
                packages["seaborn"] = seaborn.__version__
            except:
                pass
            
            try:
                import requests
                packages["requests"] = requests.__version__
            except:
                pass
            
            for k, v in packages.items():
                st.markdown(f"**{k}**: {v}")
    
    # 页脚
    st.markdown("---")
    st.markdown(
        f"<div style='text-align: center; color: gray; font-size: 0.8em;'>"
        f"数据处理工具 © {datetime.now().year} | "
        f"版本: {settings.APP_VERSION if hasattr(settings, 'APP_VERSION') else '1.0.0'}"
        f"</div>", 
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    render() 