#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据可视化核心功能
提供CSV/Excel数据的可视化功能
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
from datetime import datetime
import shutil
from typing import List, Dict, Any, Tuple, Optional
import io
from PIL import Image

# 可选依赖，可能不存在
try:
    from wordcloud import WordCloud
    import jieba
    WORDCLOUD_AVAILABLE = True
except ImportError:
    WORDCLOUD_AVAILABLE = False

from config import settings
from core.logger import logger
from core.utils import ensure_dir, create_job_dir

def plot_daily_distribution(df: pd.DataFrame, output_dir: str, date_column: str = 'event_day'):
    """绘制每日数据分布图"""
    logger.info(f"绘制每日分布图 (使用列: {date_column})")
    
    if date_column not in df.columns:
        logger.warning(f"数据中没有{date_column}列，无法绘制每日分布图")
        return None
    
    plt.figure(figsize=(12, 6))
    daily_counts = df[date_column].value_counts().sort_index()
    ax = daily_counts.plot(kind='bar', color='skyblue')
    plt.title('每日数据分布')
    plt.xlabel('日期')
    plt.ylabel('记录数')
    plt.xticks(rotation=45)
    
    # 添加数值标签
    for i, count in enumerate(daily_counts):
        ax.text(i, count + 0.1, str(count), ha='center')
    
    plt.tight_layout()
    output_path = os.path.join(output_dir, 'daily_distribution.png')
    plt.savefig(output_path)
    logger.info(f"每日分布图已保存到 {output_path}")
    plt.close()
    
    return output_path

def plot_column_distribution(df: pd.DataFrame, output_dir: str, column: str, 
                            title: str = None, bins: int = 30):
    """绘制列数据分布图"""
    if column not in df.columns:
        logger.warning(f"数据中没有{column}列，无法绘制分布图")
        return None
    
    title = title or f"{column}分布"
    logger.info(f"绘制{title}")
    
    plt.figure(figsize=(10, 6))
    
    # 检查列数据类型
    if df[column].dtype in [np.int64, np.float64]:
        # 数值型数据
        sns.histplot(df[column], bins=bins, kde=True)
    else:
        # 分类数据
        value_counts = df[column].value_counts().head(20)  # 限制为前20个值
        sns.barplot(x=value_counts.index, y=value_counts.values)
        plt.xticks(rotation=45)
    
    plt.title(title)
    plt.xlabel(column)
    plt.ylabel('频率')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    output_path = os.path.join(output_dir, f"{column}_distribution.png")
    plt.savefig(output_path)
    logger.info(f"{title}已保存到 {output_path}")
    plt.close()
    
    return output_path

def plot_text_length(df: pd.DataFrame, output_dir: str, text_column: str, 
                    title: str = None, bins: int = 30):
    """绘制文本长度分布图"""
    if text_column not in df.columns:
        logger.warning(f"数据中没有{text_column}列，无法分析文本长度")
        return None
    
    # 计算文本长度
    length_column = f"{text_column}_length"
    df[length_column] = df[text_column].fillna('').astype(str).apply(len)
    
    title = title or f"{text_column}长度分布"
    logger.info(f"绘制{title}")
    
    plt.figure(figsize=(10, 6))
    sns.histplot(df[length_column], bins=bins, kde=True)
    plt.title(title)
    plt.xlabel(f'{text_column}长度（字符数）')
    plt.ylabel('频率')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    output_path = os.path.join(output_dir, f"{text_column}_length_distribution.png")
    plt.savefig(output_path)
    logger.info(f"{title}已保存到 {output_path}")
    plt.close()
    
    return output_path

def create_wordcloud(text: str, output_dir: str, font_path: str = None, 
                    title: str = "词云图", max_words: int = 100):
    """创建词云图"""
    if not WORDCLOUD_AVAILABLE:
        logger.warning("缺少wordcloud或jieba依赖，无法创建词云图")
        return None
    
    if not text or len(text) < 10:
        logger.warning("文本内容不足，无法创建词云图")
        return None
    
    logger.info("正在创建词云图")
    
    # 使用jieba分词
    words = jieba.cut(text)
    word_space_split = ' '.join(words)
    
    # 默认字体路径
    if not font_path:
        font_path = os.path.join(settings.STATIC_DIR, "fonts", "simhei.ttf")
        if not os.path.exists(font_path):
            font_path = None  # 不存在就使用默认字体
    
    # 创建词云
    try:
        wordcloud = WordCloud(
            font_path=font_path,  # 中文字体
            width=800,
            height=400,
            background_color='white',
            max_words=max_words,
            max_font_size=150,
            random_state=42
        ).generate(word_space_split)
        
        plt.figure(figsize=(10, 6))
        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        plt.title(title)
        plt.tight_layout()
        
        output_path = os.path.join(output_dir, 'wordcloud.png')
        plt.savefig(output_path)
        logger.info(f"词云图已保存到 {output_path}")
        plt.close()
        
        return output_path
    except Exception as e:
        logger.error(f"创建词云图失败: {e}")
        return None

def create_correlation_heatmap(df: pd.DataFrame, output_dir: str, 
                              columns: List[str] = None, title: str = "相关性热力图"):
    """创建相关性热力图"""
    # 如果指定了列，则只使用这些列
    if columns:
        numeric_df = df[columns].select_dtypes(include=[np.number])
    else:
        # 只使用数值型列
        numeric_df = df.select_dtypes(include=[np.number])
    
    # 检查是否有足够的数值列
    if numeric_df.shape[1] < 2:
        logger.warning("数据中数值列不足，无法创建相关性热力图")
        return None
    
    logger.info(f"创建相关性热力图 (使用 {numeric_df.shape[1]} 列)")
    
    # 计算相关性
    corr = numeric_df.corr()
    
    # 绘制热力图
    plt.figure(figsize=(12, 10))
    mask = np.triu(np.ones_like(corr, dtype=bool))  # 创建上三角掩码
    cmap = sns.diverging_palette(220, 10, as_cmap=True)
    
    sns.heatmap(
        corr, 
        mask=mask,
        cmap=cmap,
        vmax=1.0, vmin=-1.0, center=0,
        square=True, linewidths=.5,
        annot=True, fmt=".2f"
    )
    
    plt.title(title)
    plt.tight_layout()
    
    output_path = os.path.join(output_dir, 'correlation_heatmap.png')
    plt.savefig(output_path)
    logger.info(f"相关性热力图已保存到 {output_path}")
    plt.close()
    
    return output_path

def create_summary_report(df: pd.DataFrame, output_dir: str):
    """创建数据摘要报告"""
    logger.info("创建数据摘要报告")
    
    # 基本统计信息
    basic_stats = {
        "记录总数": len(df),
        "列数": len(df.columns),
        "列名": df.columns.tolist()
    }
    
    # 数值列统计
    numeric_stats = {}
    for col in df.select_dtypes(include=[np.number]).columns:
        numeric_stats[col] = {
            "平均值": float(df[col].mean()),
            "中位数": float(df[col].median()),
            "最大值": float(df[col].max()),
            "最小值": float(df[col].min()),
            "标准差": float(df[col].std()),
            "缺失值数量": int(df[col].isna().sum())
        }
    
    # 分类列统计
    categorical_stats = {}
    for col in df.select_dtypes(include=['object', 'category']).columns:
        value_counts = df[col].value_counts().head(10).to_dict()  # 只取前10个最常见值
        categorical_stats[col] = {
            "唯一值数量": df[col].nunique(),
            "最常见值": value_counts,
            "缺失值数量": int(df[col].isna().sum())
        }
    
    # 日期列统计
    date_stats = {}
    for col in df.select_dtypes(include=['datetime64']).columns:
        date_stats[col] = {
            "最早日期": str(df[col].min()),
            "最晚日期": str(df[col].max()),
            "日期范围(天)": (df[col].max() - df[col].min()).days,
            "缺失值数量": int(df[col].isna().sum())
        }
    
    # 合并所有统计信息
    report = {
        "basic_stats": basic_stats,
        "numeric_stats": numeric_stats,
        "categorical_stats": categorical_stats,
        "date_stats": date_stats,
        "generated_at": datetime.now().isoformat()
    }
    
    # 保存为JSON文件
    report_path = os.path.join(output_dir, 'summary_report.json')
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=4)
    
    # 创建一个简单的文本报告
    text_report_path = os.path.join(output_dir, 'summary_report.txt')
    with open(text_report_path, 'w', encoding='utf-8') as f:
        f.write("===== 数据分析摘要报告 =====\n\n")
        f.write(f"记录总数: {basic_stats['记录总数']}\n")
        f.write(f"列数: {basic_stats['列数']}\n\n")
        
        f.write("== 列统计 ==\n")
        for col in df.columns:
            missing = df[col].isna().sum()
            missing_pct = (missing / len(df)) * 100
            f.write(f"{col}: {df[col].dtype} (缺失: {missing}, {missing_pct:.2f}%)\n")
        
        f.write("\n== 数值列统计 ==\n")
        for col, stats in numeric_stats.items():
            f.write(f"{col}:\n")
            for stat_name, value in stats.items():
                f.write(f"  {stat_name}: {value}\n")
            f.write("\n")
    
    logger.info(f"摘要报告已保存到 {report_path} 和 {text_report_path}")
    return report_path, text_report_path

def visualize_data(df: pd.DataFrame, output_dir: str, options: Dict[str, bool] = None):
    """生成数据可视化和分析报告"""
    logger.info(f"开始数据可视化，输出目录: {output_dir}")
    ensure_dir(output_dir)
    
    # 默认选项
    default_options = {
        "daily_distribution": True,
        "query_length": True,
        "answer_length": True,
        "wordcloud": True,
        "correlation": True,
        "summary_report": True
    }
    
    # 合并选项
    options = options or {}
    for key, value in default_options.items():
        if key not in options:
            options[key] = value
    
    # 保存生成的文件路径
    result_files = {}
    
    # 每日分布图
    if options["daily_distribution"] and 'event_day' in df.columns:
        daily_dist_file = plot_daily_distribution(df, output_dir)
        if daily_dist_file:
            result_files["daily_distribution"] = daily_dist_file
    
    # 查询长度分布
    if options["query_length"] and 'query' in df.columns:
        query_length_file = plot_text_length(df, output_dir, 'query', '查询长度分布')
        if query_length_file:
            result_files["query_length"] = query_length_file
    
    # 回答长度分布
    if options["answer_length"] and 'answer' in df.columns:
        answer_length_file = plot_text_length(df, output_dir, 'answer', '回答长度分布')
        if answer_length_file:
            result_files["answer_length"] = answer_length_file
    
    # 词云图
    if options["wordcloud"] and 'answer' in df.columns and WORDCLOUD_AVAILABLE:
        all_text = ' '.join(df['answer'].fillna('').astype(str))
        if all_text:
            wordcloud_file = create_wordcloud(all_text, output_dir)
            if wordcloud_file:
                result_files["wordcloud"] = wordcloud_file
    
    # 相关性热力图
    if options["correlation"]:
        corr_file = create_correlation_heatmap(df, output_dir)
        if corr_file:
            result_files["correlation"] = corr_file
    
    # 数据摘要报告
    if options["summary_report"]:
        json_report, text_report = create_summary_report(df, output_dir)
        result_files["json_report"] = json_report
        result_files["text_report"] = text_report
    
    # 创建ZIP文件
    try:
        zip_path = os.path.join(os.path.dirname(output_dir), "visualizations.zip")
        shutil.make_archive(os.path.splitext(zip_path)[0], 'zip', output_dir)
        result_files["zip"] = zip_path
        logger.info(f"已创建可视化结果ZIP文件: {zip_path}")
    except Exception as e:
        logger.error(f"创建ZIP文件失败: {e}")
    
    logger.info(f"数据可视化完成，生成了 {len(result_files)} 个文件")
    return result_files

def process_data_visualization(input_file: str, output_dir: str = None, options: Dict[str, bool] = None):
    """
    处理数据可视化的完整流程
    
    参数:
        input_file: 输入文件路径(CSV或Excel)
        output_dir: 输出目录，如果为None则自动创建
        options: 可视化选项
    
    返回:
        包含结果信息的字典
    """
    logger.info(f"开始处理文件: {input_file}")
    
    # 确定输出目录
    if output_dir is None:
        job_id, job_dir = create_job_dir("data_visualizer")
        output_dir = os.path.join(job_dir, "visualizations")
    
    ensure_dir(output_dir)
    logger.info(f"输出目录: {output_dir}")
    
    # 读取数据
    try:
        if input_file.lower().endswith('.csv'):
            df = pd.read_csv(input_file)
        elif input_file.lower().endswith(('.xlsx', '.xls')):
            df = pd.read_excel(input_file)
        else:
            error_msg = f"不支持的文件格式: {input_file}"
            logger.error(error_msg)
            return {"error": error_msg}
        
        logger.info(f"成功读取数据: {df.shape[0]} 行, {df.shape[1]} 列")
    except Exception as e:
        error_msg = f"读取文件出错: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}
    
    # 生成可视化
    try:
        result_files = visualize_data(df, output_dir, options)
        
        return {
            "success": True,
            "output_dir": output_dir,
            "files": result_files,
            "shape": df.shape,
            "columns": df.columns.tolist()
        }
    except Exception as e:
        error_msg = f"生成可视化出错: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg} 