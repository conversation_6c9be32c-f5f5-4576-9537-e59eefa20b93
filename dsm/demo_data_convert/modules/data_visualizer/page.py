#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据可视化模块的Streamlit页面
提供CSV/Excel数据的可视化功能界面
"""

import os
import streamlit as st
import pandas as pd
from pathlib import Path
import tempfile
import json
import time
from datetime import datetime

from config import settings
from core.logger import logger
from core.utils import (
    ensure_dir, get_file_download_link, create_job_dir,
    check_file_extension, human_readable_size, load_data,
    get_module_data_dir, get_module_results_dir
)
from modules.data_visualizer.visualizer import process_data_visualization

def render():
    """渲染数据可视化模块的界面"""
    st.markdown("## 📊 数据可视化")
    st.markdown("""
    将CSV或Excel文件转换为可视化图表和分析报告，帮助您快速理解数据特征。
    """)
    
    # 创建模块数据目录
    module_data_dir = get_module_data_dir("data_visualizer")
    module_results_dir = get_module_results_dir("data_visualizer")
    ensure_dir(module_data_dir)
    ensure_dir(module_results_dir)
    
    # 侧边栏配置选项
    st.sidebar.markdown("### 可视化选项")
    
    # 基本可视化选项
    basic_options = {
        "daily_distribution": st.sidebar.checkbox("每日数据分布", value=True, help="如果数据包含'event_day'列，将创建每日数据分布图"),
        "query_length": st.sidebar.checkbox("查询长度分布", value=True, help="如果数据包含'query'列，将创建查询长度分布图"),
        "answer_length": st.sidebar.checkbox("回答长度分布", value=True, help="如果数据包含'answer'列，将创建回答长度分布图"),
        "wordcloud": st.sidebar.checkbox("词云图", value=True, help="如果数据包含'answer'列，将创建词云图"),
        "correlation": st.sidebar.checkbox("相关性热力图", value=True, help="创建数值列之间的相关性热力图"),
        "summary_report": st.sidebar.checkbox("数据摘要报告", value=True, help="创建包含数据统计信息的摘要报告")
    }
    
    # 展示示例数据选项
    use_demo = st.checkbox("使用示例数据", value=False, help="使用内置的示例数据进行可视化")
    
    # 上传方式：上传文件或选择已有文件
    upload_method = st.radio("选择数据来源", ["上传新文件", "使用已上传的文件"])
    
    uploaded_file = None
    selected_file = None
    
    if upload_method == "上传新文件":
        # 文件上传
        uploaded_file = st.file_uploader(
            "上传CSV或Excel文件",
            type=["csv", "xlsx", "xls"],
            help="支持CSV和Excel格式，数据将会被处理为可视化图表和报告"
        )
    else:
        # 列出已经上传的文件
        uploaded_files = _list_uploaded_files(module_data_dir)
        if uploaded_files:
            selected_file = st.selectbox(
                "选择已上传的文件",
                options=uploaded_files,
                format_func=lambda x: os.path.basename(x),
                help="选择已上传的文件进行可视化"
            )
        else:
            st.info("没有找到已上传的文件，请上传新文件。")
    
    if st.button("生成可视化", type="primary"):
        if use_demo:
            # 使用示例数据
            demo_file = os.path.join(settings.PROJECT_ROOT, "static", "demo", "demo.csv")
            if not os.path.exists(demo_file):
                st.error("示例数据文件不存在，请上传自己的文件。")
                return
            
            with st.spinner("正在处理示例数据..."):
                job_id, job_dir = create_job_dir("data_visualizer")
                results = process_data_visualization(
                    demo_file, 
                    os.path.join(job_dir, "visualizations"),
                    basic_options
                )
                _display_results(results)
            
        elif uploaded_file is not None:
            # 保存上传的文件
            with st.spinner("正在上传文件..."):
                job_id, job_dir = create_job_dir("data_visualizer")
                file_path = os.path.join(module_data_dir, uploaded_file.name)
                
                with open(file_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())
                
                st.success(f"文件已上传: {uploaded_file.name}")
                
                # 处理文件
                with st.spinner("正在生成可视化..."):
                    results = process_data_visualization(
                        file_path, 
                        os.path.join(job_dir, "visualizations"),
                        basic_options
                    )
                    _display_results(results)
        
        elif selected_file is not None:
            # 处理选择的文件
            with st.spinner("正在生成可视化..."):
                job_id, job_dir = create_job_dir("data_visualizer")
                results = process_data_visualization(
                    selected_file, 
                    os.path.join(job_dir, "visualizations"),
                    basic_options
                )
                _display_results(results)
        
        else:
            st.warning("请上传文件或选择示例数据。")
    
    # 显示已生成的可视化历史记录
    _show_visualization_history(module_results_dir)

def _list_uploaded_files(directory):
    """列出目录中的所有CSV和Excel文件"""
    if not os.path.exists(directory):
        return []
    
    files = []
    for filename in os.listdir(directory):
        if filename.lower().endswith(('.csv', '.xlsx', '.xls')):
            files.append(os.path.join(directory, filename))
    
    # 按修改时间排序，最新的在前面
    files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    return files

def _show_visualization_history(results_dir):
    """显示已生成的可视化历史记录"""
    if not os.path.exists(results_dir):
        return
    
    # 获取所有子目录（每个作业一个目录）
    job_dirs = [d for d in os.listdir(results_dir) if os.path.isdir(os.path.join(results_dir, d))]
    
    if not job_dirs:
        return
    
    st.markdown("---")
    st.markdown("### 历史可视化结果")
    
    # 按时间排序，最新的在前面
    job_dirs.sort(key=lambda x: os.path.getmtime(os.path.join(results_dir, x)), reverse=True)
    
    # 只显示最近的5个
    for job_dir in job_dirs[:5]:
        full_job_dir = os.path.join(results_dir, job_dir)
        viz_dir = os.path.join(full_job_dir, "visualizations")
        
        if not os.path.exists(viz_dir):
            continue
        
        # 获取时间戳
        timestamp = datetime.fromtimestamp(os.path.getmtime(full_job_dir))
        time_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")
        
        # 查找zip文件
        zip_file = os.path.join(full_job_dir, "visualizations.zip")
        
        with st.expander(f"可视化结果 ({time_str})"):
            # 显示下载链接
            if os.path.exists(zip_file):
                st.markdown(get_file_download_link(
                    zip_file, 
                    "下载所有可视化结果 (ZIP)"
                ), unsafe_allow_html=True)
            
            # 显示图片预览
            image_files = [f for f in os.listdir(viz_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            
            if image_files:
                st.markdown("#### 图表预览")
                
                # 将图片分成两列显示
                cols = st.columns(2)
                for i, img_file in enumerate(image_files):
                    img_path = os.path.join(viz_dir, img_file)
                    cols[i % 2].image(img_path, caption=os.path.splitext(img_file)[0], use_column_width=True)
            
            # 显示报告
            report_file = os.path.join(viz_dir, "summary_report.json")
            if os.path.exists(report_file):
                with open(report_file, 'r', encoding='utf-8') as f:
                    try:
                        report = json.load(f)
                        st.markdown("#### 数据摘要")
                        
                        if "basic_stats" in report:
                            bs = report["basic_stats"]
                            st.markdown(f"- 记录总数: {bs.get('记录总数', '未知')}")
                            st.markdown(f"- 列数: {bs.get('列数', '未知')}")
                    except Exception as e:
                        st.error(f"读取报告文件出错: {e}")

def _display_results(results):
    """显示可视化结果"""
    if "error" in results:
        st.error(f"处理出错: {results['error']}")
        return
    
    if results.get("success", False):
        st.success("🎉 可视化生成成功!")
        
        # 数据基本信息
        st.markdown(f"**数据形状**: {results.get('shape', '未知')[0]} 行 × {results.get('shape', '未知')[1]} 列")
        
        # 文件下载链接
        if "files" in results and "zip" in results["files"]:
            st.markdown(
                get_file_download_link(
                    results["files"]["zip"], 
                    "📥 下载所有可视化结果 (ZIP)"
                ), 
                unsafe_allow_html=True
            )
        
        # 展示图片
        if "files" in results:
            files = results["files"]
            st.markdown("### 数据可视化结果")
            
            image_files = [v for k, v in files.items() 
                          if k not in ["zip", "json_report", "text_report"] 
                          and v and os.path.exists(v)]
            
            if image_files:
                # 将图片分成两列显示
                cols = st.columns(2)
                for i, img_path in enumerate(image_files):
                    cols[i % 2].image(img_path, caption=os.path.basename(img_path).split(".")[0], use_column_width=True)
            
            # 显示报告
            if "text_report" in files and os.path.exists(files["text_report"]):
                with st.expander("📊 数据分析摘要报告"):
                    with open(files["text_report"], 'r', encoding='utf-8') as f:
                        report_text = f.read()
                        st.text(report_text)
    else:
        st.error("处理失败，请检查文件格式是否正确。")

if __name__ == "__main__":
    render() 