#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据转换器核心功能
处理JSONL文件转换为CSV/Excel和数据分析功能
"""

import os
import json
import pandas as pd
import numpy as np
import requests
from tqdm import tqdm
import time
import shutil
import json
from typing import List, Dict, Any, Tuple, Optional

from config import settings
from core.logger import logger
from core.utils import ensure_dir, create_job_dir, get_module_results_dir

def read_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """读取JSONL文件并返回数据列表"""
    logger.info(f"正在读取JSONL文件: {file_path}")
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    data.append(json.loads(line))
        logger.info(f"成功读取 {len(data)} 条记录")
        return data
    except Exception as e:
        logger.error(f"读取JSONL文件出错: {e}")
        raise

def convert_to_dataframe(data: List[Dict[str, Any]]) -> pd.DataFrame:
    """将JSON数据转换为DataFrame"""
    logger.info("正在转换数据为DataFrame")
    return pd.DataFrame(data)

def save_to_csv(df: pd.DataFrame, output_path: str, encoding: str = 'utf-8') -> str:
    """保存DataFrame到CSV文件"""
    logger.info(f"正在保存CSV文件: {output_path}")
    df.to_csv(output_path, index=False, encoding=encoding)
    logger.info(f"CSV文件已保存: {output_path}")
    return output_path

def save_to_excel(df: pd.DataFrame, output_path: str) -> str:
    """保存DataFrame到Excel文件"""
    logger.info(f"正在保存Excel文件: {output_path}")
    df.to_excel(output_path, index=False, engine='openpyxl')
    logger.info(f"Excel文件已保存: {output_path}")
    return output_path

def extract_image_urls(data: List[Dict[str, Any]]) -> List[Tuple[str, str]]:
    """从数据中提取所有图片URL"""
    logger.info("正在提取图片URL")
    image_urls = []
    
    for item in data:
        if 'chat_files' in item and item['chat_files']:
            try:
                # 如果是字符串，尝试解析为JSON
                if isinstance(item['chat_files'], str):
                    chat_files = json.loads(item['chat_files'])
                else:
                    chat_files = item['chat_files']
                
                # 支持两种格式: 列表或字典
                if isinstance(chat_files, list):
                    for file in chat_files:
                        if isinstance(file, dict) and file.get('type') == 'image' and 'url' in file:
                            image_urls.append((item.get('chat_id', ''), file['url']))
                elif isinstance(chat_files, dict) and chat_files.get('type') == 'image' and 'url' in chat_files:
                    image_urls.append((item.get('chat_id', ''), chat_files['url']))
            except json.JSONDecodeError:
                logger.warning(f"无法解析chat_files: {item.get('chat_files')}")
            except Exception as e:
                logger.error(f"处理图片URL时出错: {str(e)}")
    
    logger.info(f"找到 {len(image_urls)} 个图片URL")
    return image_urls

def download_images(image_urls: List[Tuple[str, str]], output_dir: str, 
                    max_count: int = None, timeout: int = 10) -> List[str]:
    """下载图片并保存到指定目录"""
    logger.info(f"开始下载图片到目录: {output_dir}")
    ensure_dir(output_dir)
    
    # 限制下载数量
    if max_count and len(image_urls) > max_count:
        logger.info(f"限制下载数量: {max_count}/{len(image_urls)}")
        image_urls = image_urls[:max_count]
    
    downloaded_files = []
    
    for chat_id, url in tqdm(image_urls, desc="下载图片"):
        try:
            # 从URL中提取文件名
            img_filename = f"{chat_id}_{url.split('/')[-1]}"
            img_path = os.path.join(output_dir, img_filename)
            
            # 下载图片
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                with open(img_path, 'wb') as f:
                    f.write(response.content)
                downloaded_files.append(img_path)
                logger.info(f"图片已保存: {img_path}")
            else:
                logger.warning(f"无法下载图片 {url}, 状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"下载图片时出错 {url}: {str(e)}")
    
    logger.info(f"下载完成，共 {len(downloaded_files)}/{len(image_urls)} 张图片")
    return downloaded_files

def analyze_data(df: pd.DataFrame) -> Dict[str, Any]:
    """分析数据并生成统计信息"""
    logger.info("正在分析数据")
    
    stats = {
        "记录总数": len(df),
        "列数": len(df.columns),
        "列名": df.columns.tolist()
    }
    
    # 检查是否存在特定列并添加相应统计信息
    if 'session_id' in df.columns:
        stats["唯一会话数"] = df['session_id'].nunique()
    
    if 'event_day' in df.columns:
        stats["每日记录分布"] = df['event_day'].value_counts().to_dict()
    
    if 'query' in df.columns:
        df['query_length'] = df['query'].astype(str).apply(len)
        stats["平均查询长度"] = float(df['query_length'].mean())
        stats["最长查询长度"] = int(df['query_length'].max())
        stats["最短查询长度"] = int(df['query_length'].min())
    
    if 'answer' in df.columns:
        df['answer_length'] = df['answer'].astype(str).apply(len)
        stats["平均回答长度"] = float(df['answer_length'].mean())
        stats["最长回答长度"] = int(df['answer_length'].max())
        stats["最短回答长度"] = int(df['answer_length'].min())
    
    if 'chat_files' in df.columns:
        stats["包含图片的记录数"] = df['chat_files'].astype(str).str.contains('image').sum()
    
    logger.info("数据分析完成")
    return stats

def process_jsonl_file(input_file: str, output_format: str = 'csv', 
                      download_images: bool = False, analyze_data_flag: bool = True,
                      max_image_count: int = 100, timeout: int = 10) -> Dict[str, Any]:
    """处理JSONL文件的完整流程"""
    logger.info(f"开始处理JSONL文件: {input_file}")
    
    # 创建工作目录
    job_id, job_dir = create_job_dir("data_converter")
    logger.info(f"创建工作目录: {job_dir}")
    
    # 读取数据
    data = read_jsonl(input_file)
    df = convert_to_dataframe(data)
    
    results = {
        "job_id": job_id,
        "job_dir": job_dir,
        "files": {}
    }
    
    # 保存为CSV
    if output_format in ["csv", "both"]:
        csv_path = os.path.join(job_dir, "data.csv")
        save_to_csv(df, csv_path)
        results["files"]["csv"] = csv_path
    
    # 保存为Excel
    if output_format in ["excel", "both"]:
        excel_path = os.path.join(job_dir, "data.xlsx")
        save_to_excel(df, excel_path)
        results["files"]["excel"] = excel_path
    
    # 下载图片
    if download_images:
        image_dir = os.path.join(job_dir, "images")
        ensure_dir(image_dir)
        image_urls = extract_image_urls(data)
        
        if image_urls:
            downloaded_files = download_images(
                image_urls, image_dir, max_count=max_image_count, timeout=timeout
            )
            
            # 创建图片目录的zip文件
            if downloaded_files:
                shutil.make_archive(os.path.join(job_dir, "images"), 'zip', image_dir)
                results["files"]["images_zip"] = f"{job_dir}/images.zip"
    
    # 分析数据
    if analyze_data_flag:
        stats = analyze_data(df)
        stats_path = os.path.join(job_dir, "stats.json")
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=4)
        results["files"]["stats"] = stats_path
        results["stats"] = stats
    
    logger.info(f"处理完成，结果保存在: {job_dir}")
    return results 