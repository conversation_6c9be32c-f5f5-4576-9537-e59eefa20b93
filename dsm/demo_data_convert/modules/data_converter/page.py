#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据转换模块的Streamlit页面
提供JSONL文件转换为CSV/Excel的UI界面
"""

import os
import streamlit as st
import pandas as pd
import json
import time
from pathlib import Path

from config import settings
from core.logger import logger
from core.utils import (
    get_module_data_dir, create_job_dir, ensure_dir, 
    get_file_download_link, check_file_extension
)
from modules.data_converter.converter import (
    read_jsonl, convert_to_dataframe, save_to_csv, save_to_excel,
    extract_image_urls, download_images, analyze_data, process_jsonl_file
)

# 模块信息
MODULE_NAME = "data_converter"
MODULE_TITLE = "数据转换"

def render():
    """渲染数据转换页面"""
    st.markdown(f"## {MODULE_TITLE}")
    st.markdown("将JSONL格式文件转换为CSV或Excel格式，支持数据分析和图片下载。")
    
    # 创建模块数据目录
    module_data_dir = get_module_data_dir(MODULE_NAME)
    
    # 选择转换选项
    with st.expander("选择转换选项", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            # 文件上传
            uploaded_file = st.file_uploader(
                "上传JSONL文件", 
                type=["jsonl"],
                help="请选择JSONL格式的文件上传"
            )
            
            output_format = st.selectbox(
                "选择输出格式",
                ["CSV", "Excel", "两者都要"],
                index=0,
                help="选择要转换的输出格式"
            )
        
        with col2:
            download_images_flag = st.checkbox(
                "下载图片", 
                value=False,
                help="是否从数据中下载图片URL"
            )
            
            analyze_data_flag = st.checkbox(
                "数据分析", 
                value=True,
                help="是否分析数据并生成统计信息"
            )
            
            # 高级选项
            st.markdown("#### 高级选项")
            
            max_image_count = st.slider(
                "最大下载图片数量", 
                1, 1000, 100,
                help="限制要下载的最大图片数量"
            )
            
            timeout = st.slider(
                "下载超时(秒)", 
                1, 30, 10,
                help="设置图片下载的超时时间"
            )
    
    # 添加示例数据按钮
    if st.button("使用示例数据"):
        # 检查是否存在示例数据
        example_file = os.path.join(settings.ROOT_DIR, "demo1.jsonl")
        if os.path.exists(example_file):
            # 创建临时工作目录
            job_id, job_dir = create_job_dir(MODULE_NAME)
            
            # 将示例文件复制到工作目录
            import shutil
            temp_example = os.path.join(job_dir, "demo1.jsonl")
            shutil.copy(example_file, temp_example)
            
            # 处理示例文件
            with st.spinner("正在处理示例数据..."):
                map_format = {"CSV": "csv", "Excel": "excel", "两者都要": "both"}
                results = process_jsonl_file(
                    temp_example,
                    output_format=map_format.get(output_format, "csv").lower(),
                    download_images=download_images_flag,
                    analyze_data_flag=analyze_data_flag,
                    max_image_count=max_image_count,
                    timeout=timeout
                )
                
                # 显示处理结果
                _display_results(results)
        else:
            st.error("找不到示例数据文件 demo1.jsonl")
    
    # 处理按钮
    if uploaded_file is not None:
        if st.button("开始处理"):
            with st.spinner("正在处理数据..."):
                try:
                    # 创建工作目录
                    job_id, job_dir = create_job_dir(MODULE_NAME)
                    
                    # 保存上传的文件
                    input_file_path = os.path.join(job_dir, uploaded_file.name)
                    with open(input_file_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())
                    
                    # 处理文件
                    map_format = {"CSV": "csv", "Excel": "excel", "两者都要": "both"}
                    results = process_jsonl_file(
                        input_file_path,
                        output_format=map_format.get(output_format, "csv").lower(),
                        download_images=download_images_flag,
                        analyze_data_flag=analyze_data_flag,
                        max_image_count=max_image_count,
                        timeout=timeout
                    )
                    
                    # 显示处理结果
                    _display_results(results)
                
                except Exception as e:
                    logger.error(f"处理出错: {str(e)}")
                    st.error(f"处理出错: {str(e)}")
    else:
        st.info("请上传JSONL文件以开始处理")

def _display_results(results):
    """显示处理结果"""
    # 获取结果文件和统计信息
    files = results.get("files", {})
    stats = results.get("stats", {})
    
    # 显示处理结果标题
    st.markdown("### 处理完成!")
    st.success("数据处理成功，您可以下载以下文件：")
    
    # 显示下载链接
    for file_type, file_path in files.items():
        if os.path.exists(file_path):
            if file_type == "csv":
                st.markdown(f"**CSV文件：** {get_file_download_link(file_path, 'CSV文件')}", unsafe_allow_html=True)
            elif file_type == "excel":
                st.markdown(f"**Excel文件：** {get_file_download_link(file_path, 'Excel文件')}", unsafe_allow_html=True)
            elif file_type == "images_zip":
                st.markdown(f"**图片打包：** {get_file_download_link(file_path, '图片压缩包')}", unsafe_allow_html=True)
            elif file_type == "stats":
                st.markdown(f"**统计数据：** {get_file_download_link(file_path, '统计数据(JSON)')}", unsafe_allow_html=True)
    
    # 显示数据分析结果
    if stats:
        st.markdown("### 数据分析结果")
        st.json(stats)
    
    # 显示数据预览
    if "csv" in files or "excel" in files:
        # 获取数据文件路径
        data_file = files.get("csv") or files.get("excel")
        
        if data_file and os.path.exists(data_file):
            st.markdown("### 数据预览")
            
            # 读取数据
            if data_file.endswith('.csv'):
                df = pd.read_csv(data_file)
            else:
                df = pd.read_excel(data_file)
            
            # 显示前10行
            st.dataframe(df.head(10)) 