"""
业务模块包
包含所有业务功能模块
"""

import os
import importlib
import inspect
from config import settings

# 模块注册表
REGISTERED_MODULES = {}

def load_modules():
    """
    动态加载所有业务模块
    """
    global REGISTERED_MODULES
    
    # 清空注册表
    REGISTERED_MODULES = {}
    
    # 遍历模块目录
    for module_name in settings.MODULES:
        try:
            # 检查模块是否存在
            module_path = f"modules.{module_name}.page"
            module = importlib.import_module(module_path)
            
            # 检查模块是否有render函数
            if hasattr(module, "render") and callable(module.render):
                REGISTERED_MODULES[module_name] = {
                    "module": module,
                    "config": settings.MODULES.get(module_name, {})
                }
            else:
                print(f"模块 {module_name} 没有render函数")
        except ImportError as e:
            print(f"无法导入模块 {module_name}: {e}")
    
    return REGISTERED_MODULES

# 加载所有模块
load_modules() 