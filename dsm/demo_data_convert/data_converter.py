#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import pandas as pd
import argparse
import os
from tqdm import tqdm
import requests
from PIL import Image
from io import BytesIO
import numpy as np
import matplotlib.pyplot as plt
import logging
import jsonlines

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def read_jsonl(file_path):
    """读取JSONL文件并返回列表"""
    data = []
    with jsonlines.open(file_path) as reader:
        for obj in reader:
            data.append(obj)
    return data

def convert_to_dataframe(data):
    """将JSON数据转换为DataFrame"""
    return pd.DataFrame(data)

def save_to_csv(df, output_path):
    """保存DataFrame到CSV文件"""
    df.to_csv(output_path, index=False, encoding='utf-8')
    logger.info(f"数据已保存到 {output_path}")

def save_to_excel(df, output_path):
    """保存DataFrame到Excel文件"""
    df.to_excel(output_path, index=False, engine='openpyxl')
    logger.info(f"数据已保存到 {output_path}")

def extract_image_urls(data):
    """从数据中提取所有图片URL"""
    image_urls = []
    for item in data:
        if 'chat_files' in item and item['chat_files']:
            try:
                chat_files = json.loads(item['chat_files'])
                for file in chat_files:
                    if file.get('type') == 'image' and 'url' in file:
                        image_urls.append((item.get('chat_id', ''), file['url']))
            except json.JSONDecodeError:
                logger.warning(f"无法解析chat_files: {item.get('chat_files')}")
            except Exception as e:
                logger.error(f"处理图片URL时出错: {str(e)}")
    return image_urls

def download_images(image_urls, output_dir):
    """下载图片并保存到指定目录"""
    os.makedirs(output_dir, exist_ok=True)
    for chat_id, url in tqdm(image_urls, desc="下载图片"):
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                img_filename = f"{chat_id}_{url.split('/')[-1]}"
                img_path = os.path.join(output_dir, img_filename)
                with open(img_path, 'wb') as f:
                    f.write(response.content)
                logger.info(f"图片已保存: {img_path}")
            else:
                logger.warning(f"无法下载图片 {url}, 状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"下载图片时出错 {url}: {str(e)}")

def analyze_data(df):
    """分析数据并生成简单的统计信息"""
    stats = {
        "记录总数": len(df),
        "唯一会话数": df['session_id'].nunique(),
        "包含图片的记录数": df['chat_files'].apply(lambda x: '图片' in x).sum() if '图片' in df.columns else None,
        "每日记录分布": df.get('event_day', pd.Series()).value_counts().to_dict()
    }
    return stats

def main():
    parser = argparse.ArgumentParser(description="数据转换工具")
    parser.add_argument('--input', '-i', required=True, help='输入JSONL文件路径')
    parser.add_argument('--output', '-o', required=True, help='输出文件路径')
    parser.add_argument('--format', '-f', choices=['csv', 'excel', 'both'], default='csv', 
                        help='输出格式: csv, excel 或 both (默认: csv)')
    parser.add_argument('--download-images', '-d', action='store_true', 
                        help='是否下载图片')
    parser.add_argument('--image-dir', default='images', 
                        help='图片保存目录 (默认: images)')
    parser.add_argument('--analyze', '-a', action='store_true', 
                        help='是否分析数据')
    
    args = parser.parse_args()
    
    # 读取数据
    logger.info(f"正在读取文件: {args.input}")
    data = read_jsonl(args.input)
    logger.info(f"已读取 {len(data)} 条记录")
    
    # 转换为DataFrame
    df = convert_to_dataframe(data)
    
    # 保存转换后的数据
    if args.format in ['csv', 'both']:
        csv_path = args.output if args.output.endswith('.csv') else f"{args.output}.csv"
        save_to_csv(df, csv_path)
    
    if args.format in ['excel', 'both']:
        excel_path = args.output if args.output.endswith('.xlsx') else f"{args.output}.xlsx"
        save_to_excel(df, excel_path)
    
    # 下载图片
    if args.download_images:
        logger.info("提取图片URL")
        image_urls = extract_image_urls(data)
        logger.info(f"找到 {len(image_urls)} 个图片URL")
        if image_urls:
            logger.info(f"正在下载图片到目录: {args.image_dir}")
            download_images(image_urls, args.image_dir)
    
    # 分析数据
    if args.analyze:
        logger.info("正在分析数据")
        stats = analyze_data(df)
        print("\n数据分析结果:")
        for key, value in stats.items():
            print(f"{key}: {value}")
            
    logger.info("处理完成")

if __name__ == "__main__":
    main() 